# 多屏显示控制系统

## 概述

多屏显示控制系统是智慧课堂系统的核心功能之一，支持教师对多个显示设备进行统一管理和内容分发。系统提供了灵活的显示模式、布局配置、分组计时器和讨论主题分发功能。

## 功能特性

### 1. 多种显示模式

- **广播模式 (broadcast)**: 将内容同时广播到所有目标设备
- **独立显示模式 (independent)**: 每个设备显示不同的独立内容
- **小组广播模式 (group_broadcast)**: 向指定小组广播内容
- **对比显示模式 (comparison)**: 在同一屏幕上对比显示多个内容源
- **双屏联动模式 (dual_screen)**: 双屏协同显示，支持主副屏内容联动

### 2. 多种布局类型

- **单屏 (single)**: 全屏显示单一内容
- **双屏 (dual)**: 左右分屏显示两个内容
- **四分屏 (quad)**: 2x2网格显示四个内容
- **2x2网格 (grid_2x2)**: 标准2x2网格布局
- **3x3网格 (grid_3x3)**: 3x3网格布局，支持最多9个内容源
- **画中画 (pip)**: 主画面中嵌入小画面

### 3. 分组计时器

- 支持创建多个计时器，每个计时器可指定不同的小组
- 提供启动、暂停、恢复、停止等控制操作
- 实时同步显示剩余时间到所有相关设备
- 支持自定义计时器标题和持续时间

### 4. 讨论主题分发

- 支持创建和分发讨论主题到指定小组
- 支持文字内容和图片附件
- 可选择分发到全部小组或指定小组
- 实时推送到目标设备

### 5. 实时状态监控

- 显示所有活动的显示配置状态
- 监控设备连接状态
- 提供系统运行状态统计

## API接口

### 显示配置管理

#### 创建显示配置
```http
POST /api/display/configs
Content-Type: application/json

{
    "classroom_id": "classroom_001",
    "mode": "broadcast",
    "layout": "dual",
    "target_devices": ["device_001", "device_002"],
    "content_sources": ["source_001", "source_002"],
    "sync_enabled": true
}
```

#### 应用显示配置
```http
POST /api/display/configs/{config_id}/apply
```

#### 获取显示配置列表
```http
GET /api/display/configs?classroom_id=classroom_001
```

### 分组计时器管理

#### 创建分组计时器
```http
POST /api/display/timers
Content-Type: application/json

{
    "classroom_id": "classroom_001",
    "title": "小组讨论计时器",
    "duration": 300,
    "group_ids": ["group_001", "group_002"]
}
```

#### 控制计时器
```http
POST /api/display/timers/{timer_id}/start
POST /api/display/timers/{timer_id}/pause
POST /api/display/timers/{timer_id}/resume
POST /api/display/timers/{timer_id}/stop
```

### 讨论主题管理

#### 创建讨论主题
```http
POST /api/display/topics
Content-Type: application/json

{
    "classroom_id": "classroom_001",
    "title": "讨论主题",
    "content": "请讨论以下问题...",
    "target_groups": ["group_001", "group_002"]
}
```

#### 分发讨论主题
```http
POST /api/display/topics/{topic_id}/distribute
```

## 使用示例

### 1. 教师广播课件

```python
import requests

# 创建广播配置
config_data = {
    'classroom_id': 'classroom_001',
    'mode': 'broadcast',
    'layout': 'single',
    'target_devices': ['group_screen_001', 'group_screen_002', 'group_screen_003'],
    'content_sources': ['teacher_courseware'],
    'sync_enabled': True
}

response = requests.post('http://localhost:5000/api/display/configs', json=config_data)
config_id = response.json()['config']['config_id']

# 应用配置
requests.post(f'http://localhost:5000/api/display/configs/{config_id}/apply')
```

### 2. 启动分组计时器

```python
# 创建计时器
timer_data = {
    'classroom_id': 'classroom_001',
    'title': '小组讨论时间',
    'duration': 600,  # 10分钟
    'group_ids': ['group_001', 'group_002', 'group_003']
}

response = requests.post('http://localhost:5000/api/display/timers', json=timer_data)
timer_id = response.json()['timer']['timer_id']

# 启动计时器
requests.post(f'http://localhost:5000/api/display/timers/{timer_id}/start')
```

### 3. 分发讨论主题

```python
# 创建讨论主题
topic_data = {
    'classroom_id': 'classroom_001',
    'title': '团队协作讨论',
    'content': '请各小组讨论如何提高团队协作效率',
    'target_groups': ['group_001', 'group_002']
}

response = requests.post('http://localhost:5000/api/display/topics', json=topic_data)
topic_id = response.json()['topic']['topic_id']

# 分发主题
requests.post(f'http://localhost:5000/api/display/topics/{topic_id}/distribute')
```

## 客户端集成

### 教师端界面

教师端提供了完整的多屏显示控制界面，包括：

- 显示配置管理选项卡
- 分组计时器控制选项卡
- 讨论主题管理选项卡
- 系统状态监控选项卡

```python
from teacher_client.ui.display_control_widget import DisplayControlWidget

# 创建显示控制界面
display_widget = DisplayControlWidget()
display_widget.set_classroom_id('classroom_001')
display_widget.show()
```

### 小组端显示

小组端自动接收和显示多屏控制指令：

```python
from group_client.ui.display_widget import MultiScreenDisplayWidget

# 创建多屏显示组件
display_widget = MultiScreenDisplayWidget()
display_widget.set_group_info('group_001', 'classroom_001')

# 应用显示配置
display_widget.apply_display_config(config_data)

# 显示计时器
display_widget.show_timer(timer_data)

# 显示讨论主题
display_widget.show_discussion_topic(topic_data)
```

## WebSocket事件

系统通过WebSocket实现实时通信：

### 客户端事件

- `join_classroom`: 加入课堂房间
- `join_group`: 加入小组房间
- `request_timer_status`: 请求计时器状态
- `request_display_status`: 请求显示状态

### 服务端事件

- `display_config_applied`: 显示配置已应用
- `display_stopped`: 显示已停止
- `timer_started`: 计时器已启动
- `timer_paused`: 计时器已暂停
- `timer_resumed`: 计时器已恢复
- `timer_stopped`: 计时器已停止
- `discussion_topic_received`: 收到讨论主题

## 测试和演示

### 运行测试

```bash
# 运行完整测试套件
python smart_classroom/backend/test_display_system.py

# 指定服务器地址和课堂ID
python smart_classroom/backend/test_display_system.py --url http://localhost:5000 --classroom test_classroom
```

### 运行演示

```bash
# 运行演示脚本
python smart_classroom/scripts/demo_display_system.py
```

演示脚本将展示以下场景：
1. 教师广播课件到所有小组屏幕
2. 启动小组讨论计时器
3. 分发讨论主题到指定小组
4. 切换到对比显示模式
5. 启用双屏联动模式

## 配置说明

### 显示模式配置

每种显示模式都有特定的用途和配置要求：

| 模式 | 用途 | 目标设备 | 内容源 |
|------|------|----------|--------|
| broadcast | 统一广播 | 多个设备 | 单个或多个源 |
| independent | 独立显示 | 多个设备 | 每设备一个源 |
| group_broadcast | 小组广播 | 小组设备 | 单个或多个源 |
| comparison | 对比显示 | 单个设备 | 多个源 |
| dual_screen | 双屏联动 | 两个设备 | 两个源 |

### 布局类型配置

| 布局 | 适用场景 | 最大内容源数 |
|------|----------|--------------|
| single | 单一内容展示 | 1 |
| dual | 左右对比 | 2 |
| quad | 四象限显示 | 4 |
| grid_2x2 | 2x2网格 | 4 |
| grid_3x3 | 3x3网格 | 9 |
| pip | 画中画 | 2 |

## 性能优化

### 同步机制

- 使用WebSocket确保实时同步
- 支持批量操作减少网络开销
- 智能缓存减少重复数据传输

### 错误处理

- 自动重连机制
- 优雅降级处理
- 详细的错误日志记录

### 扩展性

- 支持插件式扩展新的显示模式
- 模块化设计便于功能扩展
- 标准化API接口

## 故障排除

### 常见问题

1. **显示配置应用失败**
   - 检查目标设备是否在线
   - 验证内容源是否可用
   - 确认网络连接正常

2. **计时器同步异常**
   - 检查WebSocket连接状态
   - 验证系统时间同步
   - 重启相关服务

3. **讨论主题分发失败**
   - 确认目标小组设备在线
   - 检查主题内容格式
   - 验证权限设置

### 日志查看

系统提供详细的操作日志：

```python
from backend.models.display import DisplayLog

# 查看课堂操作日志
logs = DisplayLog.get_classroom_logs('classroom_001')
for log in logs:
    print(f"{log.created_time}: {log.operation} - {log.description}")
```

## 总结

多屏显示控制系统为智慧课堂提供了强大的多媒体展示和互动功能，支持多种显示模式和布局配置，能够满足不同教学场景的需求。系统具有良好的扩展性和稳定性，为现代化教学提供了有力支持。