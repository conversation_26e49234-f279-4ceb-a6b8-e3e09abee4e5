# 设备发现与连接管理

本文档描述了智慧课堂系统中的设备发现与连接管理功能。

## 功能概述

设备发现与连接管理功能允许智慧课堂系统自动发现网络中的设备，并管理这些设备与课堂的连接。主要功能包括：

1. 自动发现网络中的设备
2. 设备注册与心跳检测
3. 设备与课堂的连接管理
4. 设备状态监控

## 技术实现

### 设备发现协议

设备发现基于UDP广播实现，使用以下流程：

1. 服务器定期向网络广播发现消息
2. 客户端监听广播消息并响应自身信息
3. 服务器接收响应并注册/更新设备信息

### 消息格式

#### 发现请求消息

```json
{
    "type": "discovery",
    "service": "smart_classroom",
    "version": "1.0"
}
```

#### 发现响应消息

```json
{
    "type": "discovery_response",
    "service": "smart_classroom",
    "device_id": "device-001",
    "device_type": "teacher",
    "device_name": "Teacher Tablet",
    "port": 8080,
    "capabilities": ["screen_share", "remote_control"],
    "os_info": "Windows 10",
    "screen_resolution": "1920x1080"
}
```

### 心跳机制

- 设备每30秒发送一次心跳消息
- 服务器定期检查设备心跳，超过5分钟无心跳的设备标记为离线
- 设备重新连接时会更新心跳时间

## API接口

### 设备发现

```
POST /api/devices/discover
```

触发设备发现广播，返回已发现的设备列表。

### 获取已发现设备

```
GET /api/devices/discovered
```

获取当前已发现的设备列表。

### 注册设备

```
POST /api/devices/register
```

请求体：
```json
{
    "device_id": "device-001",
    "device_type": "teacher",
    "device_name": "Teacher Tablet",
    "ip_address": "*************",
    "port": 8080,
    "capabilities": ["screen_share", "remote_control"],
    "screen_resolution": "1920x1080",
    "os_info": "Windows 10"
}
```

### 连接设备到课堂

```
POST /api/devices/{device_id}/connect
```

请求体：
```json
{
    "classroom_id": "classroom-001"
}
```

### 断开设备连接

```
POST /api/devices/{device_id}/disconnect
```

### 更新设备心跳

```
POST /api/devices/{device_id}/heartbeat
```

### 获取设备状态

```
GET /api/devices/{device_id}
```

### 获取设备统计信息

```
GET /api/devices/statistics
```

## 客户端使用示例

### 启动设备发现客户端

```python
from shared.device_discovery_client import DeviceDiscoveryClient

# 创建客户端
client = DeviceDiscoveryClient(
    device_type="student",
    device_name="Student Tablet",
    port=8080,
    capabilities=["screen_share", "file_receive"]
)

# 启动客户端
client.start()
```

### 演示脚本

可以使用以下命令运行演示脚本：

```bash
# 模拟教师设备
python scripts/device_discovery_demo.py --type teacher --name "Teacher Tablet"

# 模拟学生设备
python scripts/device_discovery_demo.py --type student --name "Student Tablet"

# 模拟小组设备
python scripts/device_discovery_demo.py --type group --name "Group Display"
```

## 测试

可以使用以下命令运行测试：

```bash
python -m unittest backend/test_device_discovery.py
```