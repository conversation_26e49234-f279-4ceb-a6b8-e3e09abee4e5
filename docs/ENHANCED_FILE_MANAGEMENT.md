# 增强文件管理系统文档

## 概述

智慧课堂系统的增强文件管理模块提供了全面的文件管理功能，支持文件版本控制、分块上传、预览、权限管理、访问审计、智能标签、批量操作和高级分发等特性。

## 功能特性

### 1. 文件版本管理
- ✅ 自动版本创建和管理
- ✅ 版本历史查看
- ✅ 版本恢复功能
- ✅ 版本比较和差异显示
- ✅ 版本清理策略

### 2. 分块上传支持
- ✅ 大文件分块上传
- ✅ 断点续传功能
- ✅ 上传进度跟踪
- ✅ 并发上传优化
- ✅ 失败重试机制

### 3. 文件预览功能
- ✅ 多格式文件预览
- ✅ 缩略图生成
- ✅ 在线文档查看
- ✅ 视频音频预览
- ✅ 图片快速浏览

### 4. 权限控制系统
- ✅ 细粒度权限管理
- ✅ 角色基础访问控制
- ✅ 文件共享权限
- ✅ 权限继承机制
- ✅ 动态权限调整

### 5. 访问审计日志
- ✅ 完整操作记录
- ✅ 访问统计分析
- ✅ 安全审计报告
- ✅ 异常行为检测
- ✅ 合规性支持

### 6. 智能标签分类
- ✅ 自动文件分类
- ✅ 标签管理系统
- ✅ 智能推荐标签
- ✅ 标签搜索功能
- ✅ 热门标签统计

### 7. 批量操作支持
- ✅ 批量文件处理
- ✅ 异步任务执行
- ✅ 操作进度跟踪
- ✅ 失败重试机制
- ✅ 批量结果报告

### 8. 高级分发功能
- ✅ 灵活分发策略
- ✅ 目标群体管理
- ✅ 分发状态跟踪
- ✅ 分发历史记录
- ✅ 自动化分发

## API 接口文档

### 基础文件操作

#### 上传文件
```http
POST /api/files/upload
Content-Type: multipart/form-data

file: [文件数据]
uploader_id: string
classroom_id: string (可选)
description: string (可选)
```

#### 下载文件
```http
GET /api/files/download/{file_id}
```

#### 获取文件信息
```http
GET /api/files/{file_id}
```

#### 删除文件
```http
DELETE /api/files/{file_id}
```

### 文件版本管理

#### 获取版本历史
```http
GET /api/files/{file_id}/versions
```

#### 恢复到指定版本
```http
POST /api/files/{file_id}/versions/{version_number}/restore
```

### 分块上传

#### 初始化分块上传
```http
POST /api/files/chunk/init
Content-Type: application/json

{
  "filename": "large_file.pdf",
  "total_size": 10485760,
  "total_chunks": 10,
  "uploader_id": "user_001",
  "classroom_id": "class_001"
}
```

#### 上传文件块
```http
POST /api/files/chunk/{upload_id}
Content-Type: multipart/form-data

chunk: [块数据]
chunk_number: integer
```

#### 获取上传状态
```http
GET /api/files/chunk/{upload_id}/status
```

### 文件预览

#### 获取文件预览
```http
GET /api/files/{file_id}/preview
```

#### 获取预览内容
```http
GET /api/files/preview/{file_id}/{preview_type}
```

### 权限管理

#### 设置文件权限
```http
POST /api/files/{file_id}/permissions
Content-Type: application/json

{
  "user_id": "user_001",
  "permissions": {
    "read": true,
    "write": false,
    "delete": false,
    "share": true
  }
}
```

#### 检查文件权限
```http
POST /api/files/{file_id}/check-permission
Content-Type: application/json

{
  "user_id": "user_001",
  "action": "read"
}
```

#### 获取文件权限列表
```http
GET /api/files/{file_id}/permissions
```

### 访问审计

#### 获取访问日志
```http
GET /api/files/{file_id}/access-logs?limit=100
```

#### 获取访问统计
```http
GET /api/files/{file_id}/access-stats
```

### 标签管理

#### 添加文件标签
```http
POST /api/files/{file_id}/tags
Content-Type: application/json

{
  "tag_name": "课件",
  "category": "type"
}
```

#### 获取文件标签
```http
GET /api/files/{file_id}/tags
```

#### 移除文件标签
```http
DELETE /api/files/{file_id}/tags/{tag_name}
```

#### 根据标签搜索文件
```http
POST /api/files/search-by-tags
Content-Type: application/json

{
  "tags": ["课件", "数学"],
  "classroom_id": "class_001"
}
```

#### 自动分类文件
```http
POST /api/files/{file_id}/auto-classify
```

#### 获取热门标签
```http
GET /api/files/popular-tags?classroom_id=class_001&limit=20
```

### 批量操作

#### 批量删除文件
```http
POST /api/files/batch/delete
Content-Type: application/json

{
  "file_ids": ["file_001", "file_002"],
  "user_id": "user_001"
}
```

#### 批量添加标签
```http
POST /api/files/batch/tag
Content-Type: application/json

{
  "file_ids": ["file_001", "file_002"],
  "tags": ["批量测试", "演示"],
  "user_id": "user_001"
}
```

#### 批量压缩文件
```http
POST /api/files/batch/compress
Content-Type: application/json

{
  "file_ids": ["file_001", "file_002"],
  "archive_name": "demo_archive",
  "user_id": "user_001"
}
```

#### 获取批量操作状态
```http
GET /api/files/batch/{batch_id}/status
```

### 文件分发

#### 高级文件分发
```http
POST /api/files/distribute/advanced
Content-Type: application/json

{
  "file_ids": ["file_001"],
  "target_type": "group",
  "target_ids": ["group_001", "group_002"],
  "distributor_id": "teacher_001",
  "distribution_note": "课件分发"
}
```

#### 获取分发状态
```http
GET /api/files/distribute/{distribution_id}/status
```

#### 获取分发历史
```http
GET /api/files/distribute/history?distributor_id=teacher_001&limit=50
```

## 使用示例

### Python 客户端示例

```python
import requests
import json

class FileManagementClient:
    def __init__(self, base_url='http://localhost:5000'):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/files"
        self.session = requests.Session()
    
    def upload_file(self, file_path, uploader_id, classroom_id=None):
        """上传文件"""
        with open(file_path, 'rb') as f:
            files = {'file': f}
            data = {
                'uploader_id': uploader_id,
                'classroom_id': classroom_id
            }
            response = self.session.post(f"{self.api_base}/upload", 
                                       files=files, data=data)
            return response.json()
    
    def add_file_tags(self, file_id, tags):
        """添加文件标签"""
        for tag in tags:
            data = {'tag_name': tag}
            response = self.session.post(f"{self.api_base}/{file_id}/tags", 
                                       json=data)
            if not response.json().get('success'):
                print(f"添加标签失败: {tag}")
    
    def set_file_permissions(self, file_id, user_id, permissions):
        """设置文件权限"""
        data = {
            'user_id': user_id,
            'permissions': permissions
        }
        response = self.session.post(f"{self.api_base}/{file_id}/permissions", 
                                   json=data)
        return response.json()
    
    def distribute_files(self, file_ids, target_type, target_ids, distributor_id):
        """分发文件"""
        data = {
            'file_ids': file_ids,
            'target_type': target_type,
            'target_ids': target_ids,
            'distributor_id': distributor_id
        }
        response = self.session.post(f"{self.api_base}/distribute/advanced", 
                                   json=data)
        return response.json()

# 使用示例
client = FileManagementClient()

# 上传文件
result = client.upload_file('document.pdf', 'teacher_001', 'class_001')
file_id = result['file']['id']

# 添加标签
client.add_file_tags(file_id, ['课件', '数学', '重要'])

# 设置权限
permissions = {'read': True, 'write': False, 'delete': False, 'share': True}
client.set_file_permissions(file_id, 'student_001', permissions)

# 分发文件
client.distribute_files([file_id], 'group', ['group_001', 'group_002'], 'teacher_001')
```

### JavaScript 客户端示例

```javascript
class FileManagementClient {
    constructor(baseUrl = 'http://localhost:5000') {
        this.baseUrl = baseUrl;
        this.apiBase = `${baseUrl}/api/files`;
    }
    
    async uploadFile(file, uploaderId, classroomId = null) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('uploader_id', uploaderId);
        if (classroomId) {
            formData.append('classroom_id', classroomId);
        }
        
        const response = await fetch(`${this.apiBase}/upload`, {
            method: 'POST',
            body: formData
        });
        
        return await response.json();
    }
    
    async addFileTags(fileId, tags) {
        for (const tag of tags) {
            const response = await fetch(`${this.apiBase}/${fileId}/tags`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ tag_name: tag })
            });
            
            const result = await response.json();
            if (!result.success) {
                console.error(`添加标签失败: ${tag}`);
            }
        }
    }
    
    async getFilePreview(fileId) {
        const response = await fetch(`${this.apiBase}/${fileId}/preview`);
        return await response.json();
    }
    
    async batchDeleteFiles(fileIds, userId) {
        const response = await fetch(`${this.apiBase}/batch/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                file_ids: fileIds,
                user_id: userId
            })
        });
        
        return await response.json();
    }
}

// 使用示例
const client = new FileManagementClient();

// 上传文件
document.getElementById('fileInput').addEventListener('change', async (event) => {
    const file = event.target.files[0];
    if (file) {
        const result = await client.uploadFile(file, 'teacher_001', 'class_001');
        console.log('上传结果:', result);
        
        if (result.success) {
            const fileId = result.file.id;
            
            // 添加标签
            await client.addFileTags(fileId, ['课件', '演示']);
            
            // 获取预览
            const preview = await client.getFilePreview(fileId);
            console.log('预览信息:', preview);
        }
    }
});
```

## 配置说明

### 基础配置

```python
# 文件存储配置
UPLOAD_FOLDER = 'instance/uploads'
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'ppt', 'pptx', ...}

# 分块上传配置
CHUNK_SIZE = 1024 * 1024  # 1MB
MAX_CHUNKS = 1000
SESSION_TIMEOUT = 3600  # 1小时
```

### 预览配置

```python
PREVIEW_CONFIG = {
    'enable_preview': True,
    'thumbnail_size': (200, 200),
    'supported_formats': {
        'pdf': {'engine': 'pdf2image'},
        'doc': {'engine': 'libreoffice'},
        'jpg': {'engine': 'native'}
    }
}
```

### 权限配置

```python
PERMISSION_CONFIG = {
    'default_permissions': {
        'owner': {'read': True, 'write': True, 'delete': True, 'share': True},
        'teacher': {'read': True, 'write': True, 'delete': False, 'share': True},
        'student': {'read': True, 'write': False, 'delete': False, 'share': False}
    }
}
```

## 部署指南

### 1. 环境准备

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装系统依赖（Ubuntu/Debian）
sudo apt-get update
sudo apt-get install -y \
    libreoffice \
    imagemagick \
    ffmpeg \
    poppler-utils

# 创建必要目录
mkdir -p instance/{uploads,versions,chunks,previews,batch,distributions}
```

### 2. 数据库初始化

```python
from backend.models import db
from backend.app import create_app

app = create_app()
with app.app_context():
    db.create_all()
```

### 3. 配置文件

```python
# config.py
class Config:
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = 'sqlite:///smart_classroom.db'
    
    # 文件管理配置
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB
    UPLOAD_FOLDER = 'instance/uploads'
    
    # 安全配置
    SECRET_KEY = 'your-secret-key'
    WTF_CSRF_ENABLED = True
```

### 4. 启动服务

```bash
# 开发环境
python -m backend.app

# 生产环境
gunicorn -w 4 -b 0.0.0.0:5000 backend.app:app
```

## 性能优化

### 1. 数据库优化

- 使用数据库连接池
- 添加适当的索引
- 定期清理过期数据
- 使用分页查询

### 2. 文件存储优化

- 使用对象存储服务（如MinIO）
- 启用文件压缩
- 实现CDN加速
- 配置缓存策略

### 3. 并发处理

- 使用异步任务队列（Celery）
- 实现文件上传并发控制
- 优化批量操作性能
- 使用缓存减少数据库查询

## 安全考虑

### 1. 文件安全

- 文件类型验证
- 文件内容扫描
- 病毒检测集成
- 恶意文件隔离

### 2. 访问控制

- 用户身份验证
- 权限细粒度控制
- API访问限制
- 操作审计日志

### 3. 数据保护

- 敏感数据加密
- 传输层安全（HTTPS）
- 数据备份策略
- 隐私保护措施

## 监控和维护

### 1. 系统监控

- 文件存储使用情况
- API响应时间监控
- 错误率统计
- 用户活动分析

### 2. 定期维护

- 清理临时文件
- 压缩历史版本
- 更新安全补丁
- 性能调优

### 3. 故障处理

- 自动故障检测
- 备份恢复机制
- 服务降级策略
- 紧急响应流程

## 常见问题

### Q: 如何处理大文件上传？
A: 使用分块上传功能，将大文件分割成小块逐个上传，支持断点续传。

### Q: 文件预览不工作怎么办？
A: 检查预览工具是否正确安装，确认文件格式是否支持，查看错误日志。

### Q: 如何批量处理文件？
A: 使用批量操作API，支持批量删除、标签、压缩等操作。

### Q: 权限设置不生效？
A: 检查用户角色配置，确认权限继承设置，验证API调用参数。

### Q: 如何优化存储空间？
A: 启用文件压缩，定期清理过期版本，使用重复文件检测。

## 更新日志

### v1.0.0 (2024-01-20)
- ✅ 实现文件版本管理功能
- ✅ 添加分块上传支持
- ✅ 集成文件预览功能
- ✅ 完善权限控制系统
- ✅ 实现访问审计日志
- ✅ 开发智能标签分类
- ✅ 支持批量操作功能
- ✅ 增强文件分发机制

## 技术支持

如有问题或建议，请联系开发团队：

- 📧 Email: <EMAIL>
- 📱 电话: +86-xxx-xxxx-xxxx
- 💬 在线支持: https://support.smartclassroom.com
- 📖 文档中心: https://docs.smartclassroom.com