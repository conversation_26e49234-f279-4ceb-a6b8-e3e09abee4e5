# 智慧课堂系统维护指南

## 概述

本文档提供智慧课堂系统的日常维护、监控、故障排除和性能优化指导。系统管理员应定期执行这些维护任务以确保系统稳定运行。

## 日常维护任务

### 每日检查清单

#### 1. 系统状态检查

```bash
#!/bin/bash
# 每日系统检查脚本 - daily_check.sh

echo "=== 智慧课堂系统每日检查报告 ==="
echo "检查时间: $(date)"
echo

# 1. 服务状态检查
echo "1. 服务状态检查"
echo "=================="
services=("smart-classroom-backend" "mediamtx" "nginx")

for service in "${services[@]}"; do
    if systemctl is-active --quiet "$service"; then
        echo "✅ $service: 运行正常"
    else
        echo "❌ $service: 服务异常"
        systemctl status "$service" --no-pager -l
    fi
done
echo

# 2. 端口监听检查
echo "2. 端口监听检查"
echo "=================="
ports=("80" "5000" "1935" "8889")

for port in "${ports[@]}"; do
    if netstat -tlnp | grep -q ":$port "; then
        echo "✅ 端口 $port: 正常监听"
    else
        echo "❌ 端口 $port: 未监听"
    fi
done
echo

# 3. 磁盘空间检查
echo "3. 磁盘空间检查"
echo "=================="
df -h | grep -E "(/$|/opt|/var)"
echo

# 4. 内存使用检查
echo "4. 内存使用检查"
echo "=================="
free -h
echo

# 5. 数据库状态检查
echo "5. 数据库状态检查"
echo "=================="
DB_FILE="/var/lib/smart-classroom/smart_classroom.db"
if [ -f "$DB_FILE" ]; then
    DB_SIZE=$(du -h "$DB_FILE" | cut -f1)
    echo "✅ 数据库文件存在，大小: $DB_SIZE"
    
    # 检查数据库完整性
    if sudo -u smartclass sqlite3 "$DB_FILE" "PRAGMA integrity_check;" | grep -q "ok"; then
        echo "✅ 数据库完整性检查通过"
    else
        echo "❌ 数据库完整性检查失败"
    fi
else
    echo "❌ 数据库文件不存在"
fi
echo

# 6. 日志文件检查
echo "6. 日志文件检查"
echo "=================="
LOG_DIR="/var/log/smart-classroom"
if [ -d "$LOG_DIR" ]; then
    echo "日志目录大小: $(du -sh $LOG_DIR | cut -f1)"
    echo "最新日志文件:"
    ls -lht "$LOG_DIR"/*.log 2>/dev/null | head -5
else
    echo "❌ 日志目录不存在"
fi
echo

# 7. 网络连接检查
echo "7. 网络连接检查"
echo "=================="
ACTIVE_CONNECTIONS=$(netstat -an | grep :5000 | grep ESTABLISHED | wc -l)
echo "当前活跃连接数: $ACTIVE_CONNECTIONS"

# 检查MediaMTX API
if curl -s http://localhost:9997/v3/config > /dev/null; then
    echo "✅ MediaMTX API响应正常"
else
    echo "❌ MediaMTX API无响应"
fi
echo

echo "=== 检查完成 ==="
```

#### 2. 性能监控

```bash
#!/bin/bash
# 性能监控脚本 - performance_monitor.sh

echo "=== 系统性能监控 ==="
echo "监控时间: $(date)"
echo

# CPU使用率
echo "CPU使用率:"
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//'

# 内存使用率
echo "内存使用率:"
free | grep Mem | awk '{printf "%.2f%%\n", $3/$2 * 100.0}'

# 磁盘I/O
echo "磁盘I/O统计:"
iostat -x 1 1 | grep -A 20 "Device"

# 网络流量
echo "网络流量统计:"
cat /proc/net/dev | grep -E "(eth0|ens|enp)" | head -1

# 进程资源使用
echo "主要进程资源使用:"
ps aux | grep -E "(python|mediamtx|nginx)" | grep -v grep | awk '{print $1, $2, $3, $4, $11}'

# 系统负载
echo "系统负载:"
uptime
```

### 每周维护任务

#### 1. 日志清理和分析

```bash
#!/bin/bash
# 每周日志维护脚本 - weekly_log_maintenance.sh

LOG_DIR="/var/log/smart-classroom"
BACKUP_DIR="/backup/logs"
RETENTION_DAYS=30

echo "=== 每周日志维护 ==="
echo "维护时间: $(date)"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 压缩旧日志
find "$LOG_DIR" -name "*.log" -mtime +7 -exec gzip {} \;

# 删除过期日志
find "$LOG_DIR" -name "*.log.gz" -mtime +$RETENTION_DAYS -delete

# 分析错误日志
echo "=== 错误日志分析 ==="
if [ -f "$LOG_DIR/app.log" ]; then
    echo "最近7天的错误统计:"
    grep -i error "$LOG_DIR/app.log" | tail -100 | cut -d' ' -f1-3 | sort | uniq -c | sort -nr
fi

# 分析访问日志
echo "=== 访问日志分析 ==="
if [ -f "/var/log/nginx/access.log" ]; then
    echo "最近7天的访问统计:"
    tail -10000 /var/log/nginx/access.log | awk '{print $1}' | sort | uniq -c | sort -nr | head -10
fi

echo "=== 日志维护完成 ==="
```

#### 2. 数据库维护

```bash
#!/bin/bash
# 数据库维护脚本 - database_maintenance.sh

DB_FILE="/var/lib/smart-classroom/smart_classroom.db"
BACKUP_DIR="/backup/database"

echo "=== 数据库维护 ==="
echo "维护时间: $(date)"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 数据库备份
BACKUP_FILE="$BACKUP_DIR/smart_classroom_$(date +%Y%m%d_%H%M%S).db"
sudo -u smartclass cp "$DB_FILE" "$BACKUP_FILE"
echo "数据库已备份到: $BACKUP_FILE"

# 数据库优化
echo "执行数据库优化..."
sudo -u smartclass sqlite3 "$DB_FILE" << EOF
PRAGMA optimize;
VACUUM;
ANALYZE;
EOF

# 检查数据库大小
DB_SIZE_BEFORE=$(du -h "$DB_FILE" | cut -f1)
echo "优化后数据库大小: $DB_SIZE_BEFORE"

# 清理过期数据（保留90天）
echo "清理过期数据..."
sudo -u smartclass sqlite3 "$DB_FILE" << EOF
DELETE FROM logs WHERE created_at < datetime('now', '-90 days');
DELETE FROM sessions WHERE created_at < datetime('now', '-30 days');
DELETE FROM temporary_files WHERE created_at < datetime('now', '-7 days');
EOF

echo "=== 数据库维护完成 ==="
```

### 每月维护任务

#### 1. 系统更新和安全检查

```bash
#!/bin/bash
# 每月系统维护脚本 - monthly_maintenance.sh

echo "=== 每月系统维护 ==="
echo "维护时间: $(date)"

# 系统更新
echo "1. 系统更新检查"
echo "=================="
apt list --upgradable 2>/dev/null | grep -v "WARNING"

# 安全更新
echo "2. 安全更新"
echo "============"
unattended-upgrade --dry-run

# 磁盘使用分析
echo "3. 磁盘使用分析"
echo "================"
du -sh /opt/smart-classroom/*
du -sh /var/log/smart-classroom/*
du -sh /var/lib/smart-classroom/*

# 清理临时文件
echo "4. 清理临时文件"
echo "================"
find /tmp -type f -atime +7 -delete
find /var/tmp -type f -atime +7 -delete

# 检查系统服务
echo "5. 系统服务检查"
echo "================"
systemctl list-units --failed

# 网络安全检查
echo "6. 网络安全检查"
echo "================"
ss -tlnp | grep -E ":(80|443|5000|1935|8889)"

echo "=== 每月维护完成 ==="
```

## 监控和告警

### 1. 系统监控脚本

```bash
#!/bin/bash
# 系统监控脚本 - system_monitor.sh

# 配置参数
CPU_THRESHOLD=80
MEMORY_THRESHOLD=85
DISK_THRESHOLD=90
LOAD_THRESHOLD=5.0

# 告警函数
send_alert() {
    local message="$1"
    local severity="$2"
    
    echo "[$(date)] $severity: $message" >> /var/log/smart-classroom/alerts.log
    
    # 发送邮件告警（需要配置邮件服务）
    # echo "$message" | mail -s "智慧课堂系统告警 - $severity" <EMAIL>
    
    # 发送到监控系统（如Prometheus）
    # curl -X POST http://monitoring-server/api/alerts -d "$message"
}

# CPU监控
check_cpu() {
    CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//' | cut -d'%' -f1)
    if (( $(echo "$CPU_USAGE > $CPU_THRESHOLD" | bc -l) )); then
        send_alert "CPU使用率过高: ${CPU_USAGE}%" "WARNING"
    fi
}

# 内存监控
check_memory() {
    MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    if (( $(echo "$MEMORY_USAGE > $MEMORY_THRESHOLD" | bc -l) )); then
        send_alert "内存使用率过高: ${MEMORY_USAGE}%" "WARNING"
    fi
}

# 磁盘监控
check_disk() {
    DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -gt "$DISK_THRESHOLD" ]; then
        send_alert "磁盘使用率过高: ${DISK_USAGE}%" "CRITICAL"
    fi
}

# 系统负载监控
check_load() {
    LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    if (( $(echo "$LOAD_AVG > $LOAD_THRESHOLD" | bc -l) )); then
        send_alert "系统负载过高: $LOAD_AVG" "WARNING"
    fi
}

# 服务监控
check_services() {
    services=("smart-classroom-backend" "mediamtx" "nginx")
    
    for service in "${services[@]}"; do
        if ! systemctl is-active --quiet "$service"; then
            send_alert "服务 $service 未运行" "CRITICAL"
            # 尝试重启服务
            systemctl restart "$service"
            sleep 5
            if systemctl is-active --quiet "$service"; then
                send_alert "服务 $service 已自动重启" "INFO"
            fi
        fi
    done
}

# 数据库监控
check_database() {
    DB_FILE="/var/lib/smart-classroom/smart_classroom.db"
    
    if [ ! -f "$DB_FILE" ]; then
        send_alert "数据库文件不存在" "CRITICAL"
        return
    fi
    
    # 检查数据库连接
    if ! sudo -u smartclass sqlite3 "$DB_FILE" "SELECT 1;" > /dev/null 2>&1; then
        send_alert "数据库连接失败" "CRITICAL"
    fi
    
    # 检查数据库大小
    DB_SIZE=$(du -m "$DB_FILE" | cut -f1)
    if [ "$DB_SIZE" -gt 1000 ]; then  # 1GB
        send_alert "数据库文件过大: ${DB_SIZE}MB" "WARNING"
    fi
}

# 网络监控
check_network() {
    # 检查端口监听
    ports=("80" "5000" "1935" "8889")
    
    for port in "${ports[@]}"; do
        if ! netstat -tlnp | grep -q ":$port "; then
            send_alert "端口 $port 未监听" "CRITICAL"
        fi
    done
    
    # 检查网络连接数
    CONNECTIONS=$(netstat -an | grep :5000 | grep ESTABLISHED | wc -l)
    if [ "$CONNECTIONS" -gt 100 ]; then
        send_alert "网络连接数过多: $CONNECTIONS" "WARNING"
    fi
}

# 执行所有检查
main() {
    echo "开始系统监控检查 - $(date)"
    
    check_cpu
    check_memory
    check_disk
    check_load
    check_services
    check_database
    check_network
    
    echo "系统监控检查完成 - $(date)"
}

# 运行监控
main
```

### 2. 性能指标收集

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能指标收集脚本
"""

import psutil
import sqlite3
import json
import time
from datetime import datetime
from pathlib import Path


class PerformanceCollector:
    """性能指标收集器"""
    
    def __init__(self):
        self.db_path = "/var/lib/smart-classroom/performance.db"
        self.init_database()
    
    def init_database(self):
        """初始化性能数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                metric_type TEXT NOT NULL,
                metric_data TEXT NOT NULL
            )
        """)
        
        conn.commit()
        conn.close()
    
    def collect_system_metrics(self):
        """收集系统指标"""
        metrics = {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'cpu_count': psutil.cpu_count(),
            'memory': dict(psutil.virtual_memory()._asdict()),
            'disk': dict(psutil.disk_usage('/')._asdict()),
            'network': dict(psutil.net_io_counters()._asdict()),
            'load_avg': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None,
            'boot_time': psutil.boot_time(),
            'uptime': time.time() - psutil.boot_time()
        }
        
        return metrics
    
    def collect_process_metrics(self):
        """收集进程指标"""
        processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
            try:
                if any(keyword in proc.info['name'].lower() 
                      for keyword in ['python', 'mediamtx', 'nginx']):
                    processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return processes
    
    def collect_application_metrics(self):
        """收集应用指标"""
        metrics = {}
        
        # 检查服务状态
        import subprocess
        services = ['smart-classroom-backend', 'mediamtx', 'nginx']
        
        for service in services:
            try:
                result = subprocess.run(
                    ['systemctl', 'is-active', service],
                    capture_output=True, text=True
                )
                metrics[f'{service}_status'] = result.stdout.strip()
            except Exception as e:
                metrics[f'{service}_status'] = f'error: {e}'
        
        # 检查数据库大小
        db_file = Path("/var/lib/smart-classroom/smart_classroom.db")
        if db_file.exists():
            metrics['database_size'] = db_file.stat().st_size
        
        # 检查日志大小
        log_dir = Path("/var/log/smart-classroom")
        if log_dir.exists():
            metrics['log_size'] = sum(f.stat().st_size for f in log_dir.glob('*.log'))
        
        return metrics
    
    def save_metrics(self, metric_type, data):
        """保存指标数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            "INSERT INTO performance_metrics (metric_type, metric_data) VALUES (?, ?)",
            (metric_type, json.dumps(data))
        )
        
        conn.commit()
        conn.close()
    
    def cleanup_old_metrics(self, days=30):
        """清理旧的指标数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            "DELETE FROM performance_metrics WHERE timestamp < datetime('now', '-{} days')".format(days)
        )
        
        conn.commit()
        conn.close()
    
    def run_collection(self):
        """运行指标收集"""
        print(f"开始收集性能指标 - {datetime.now()}")
        
        # 收集系统指标
        system_metrics = self.collect_system_metrics()
        self.save_metrics('system', system_metrics)
        
        # 收集进程指标
        process_metrics = self.collect_process_metrics()
        self.save_metrics('processes', process_metrics)
        
        # 收集应用指标
        app_metrics = self.collect_application_metrics()
        self.save_metrics('application', app_metrics)
        
        # 清理旧数据
        self.cleanup_old_metrics()
        
        print(f"性能指标收集完成 - {datetime.now()}")


if __name__ == "__main__":
    collector = PerformanceCollector()
    collector.run_collection()
```

### 3. 自动化监控配置

```bash
# 添加到crontab进行定期监控
# crontab -e

# 每5分钟执行系统监控
*/5 * * * * /opt/smart-classroom/scripts/system_monitor.sh

# 每小时收集性能指标
0 * * * * /opt/smart-classroom/scripts/performance_collector.py

# 每天执行日常检查
0 8 * * * /opt/smart-classroom/scripts/daily_check.sh

# 每周日执行日志维护
0 2 * * 0 /opt/smart-classroom/scripts/weekly_log_maintenance.sh

# 每月1号执行系统维护
0 3 1 * * /opt/smart-classroom/scripts/monthly_maintenance.sh
```

## 故障排除

### 常见故障及解决方案

#### 1. 服务启动失败

**症状**: 系统服务无法启动或频繁重启

**排查步骤**:
```bash
# 1. 检查服务状态
systemctl status smart-classroom-backend -l

# 2. 查看详细日志
journalctl -u smart-classroom-backend -f

# 3. 检查配置文件
sudo -u smartclass python3 -c "import yaml; yaml.safe_load(open('/opt/smart-classroom/config/production.yml'))"

# 4. 检查端口占用
lsof -i :5000

# 5. 检查权限
ls -la /opt/smart-classroom/
ls -la /var/lib/smart-classroom/
```

**常见解决方案**:
```bash
# 修复权限问题
sudo chown -R smartclass:smartclass /opt/smart-classroom
sudo chown -R smartclass:smartclass /var/lib/smart-classroom

# 重新安装Python依赖
cd /opt/smart-classroom
sudo -u smartclass venv/bin/pip install -r requirements.txt

# 重置配置文件
sudo cp config/production.yml.example config/production.yml
```

#### 2. 数据库连接问题

**症状**: 应用无法连接数据库或数据库损坏

**排查步骤**:
```bash
# 1. 检查数据库文件
ls -la /var/lib/smart-classroom/smart_classroom.db

# 2. 测试数据库连接
sudo -u smartclass sqlite3 /var/lib/smart-classroom/smart_classroom.db ".tables"

# 3. 检查数据库完整性
sudo -u smartclass sqlite3 /var/lib/smart-classroom/smart_classroom.db "PRAGMA integrity_check;"

# 4. 查看数据库日志
grep -i database /var/log/smart-classroom/app.log
```

**解决方案**:
```bash
# 修复数据库权限
sudo chown smartclass:smartclass /var/lib/smart-classroom/smart_classroom.db

# 重建数据库（注意：会丢失数据）
sudo -u smartclass rm /var/lib/smart-classroom/smart_classroom.db
sudo -u smartclass /opt/smart-classroom/venv/bin/python /opt/smart-classroom/init_db.py

# 从备份恢复
sudo -u smartclass cp /backup/database/latest.db /var/lib/smart-classroom/smart_classroom.db
```

#### 3. 网络连接问题

**症状**: 客户端无法连接服务器或视频流中断

**排查步骤**:
```bash
# 1. 检查网络连接
ping localhost
telnet localhost 5000

# 2. 检查防火墙
ufw status
iptables -L

# 3. 检查端口监听
netstat -tlnp | grep -E ":(80|5000|1935|8889)"

# 4. 检查MediaMTX状态
curl http://localhost:9997/v3/config

# 5. 测试视频流
ffplay rtmp://localhost:1935/test
```

**解决方案**:
```bash
# 开放防火墙端口
sudo ufw allow 80/tcp
sudo ufw allow 5000/tcp
sudo ufw allow 1935/tcp
sudo ufw allow 8889/tcp

# 重启网络服务
sudo systemctl restart networking

# 重启MediaMTX
sudo systemctl restart mediamtx
```

#### 4. 性能问题

**症状**: 系统响应缓慢或视频延迟过高

**排查步骤**:
```bash
# 1. 检查系统负载
top
htop
iostat -x 1

# 2. 检查内存使用
free -h
cat /proc/meminfo

# 3. 检查磁盘I/O
iotop
df -h

# 4. 检查网络流量
iftop
nethogs

# 5. 分析应用性能
strace -p $(pgrep -f "python.*backend")
```

**优化方案**:
```bash
# 调整系统参数
echo 'vm.swappiness=10' >> /etc/sysctl.conf
echo 'net.core.rmem_max=134217728' >> /etc/sysctl.conf
sysctl -p

# 优化数据库
sudo -u smartclass sqlite3 /var/lib/smart-classroom/smart_classroom.db "VACUUM; ANALYZE;"

# 清理日志文件
find /var/log -name "*.log" -mtime +7 -exec gzip {} \;

# 重启服务
sudo systemctl restart smart-classroom-backend
```

### 故障排除工具

#### 1. 系统诊断脚本

```bash
#!/bin/bash
# 系统诊断脚本 - system_diagnosis.sh

echo "=== 智慧课堂系统诊断报告 ==="
echo "诊断时间: $(date)"
echo "系统信息: $(uname -a)"
echo

# 基础信息收集
echo "1. 基础系统信息"
echo "=================="
echo "主机名: $(hostname)"
echo "IP地址: $(hostname -I)"
echo "系统版本: $(lsb_release -d 2>/dev/null || cat /etc/os-release | grep PRETTY_NAME)"
echo "内核版本: $(uname -r)"
echo "系统启动时间: $(uptime -s)"
echo

# 硬件信息
echo "2. 硬件信息"
echo "============"
echo "CPU信息: $(lscpu | grep 'Model name' | cut -d: -f2 | xargs)"
echo "CPU核心数: $(nproc)"
echo "内存总量: $(free -h | grep Mem | awk '{print $2}')"
echo "磁盘信息:"
lsblk | grep -E "(disk|part)" | head -5
echo

# 服务状态详细检查
echo "3. 服务状态详细检查"
echo "===================="
services=("smart-classroom-backend" "mediamtx" "nginx")

for service in "${services[@]}"; do
    echo "--- $service ---"
    systemctl status "$service" --no-pager -l | head -10
    echo
done

# 网络诊断
echo "4. 网络诊断"
echo "============"
echo "网络接口:"
ip addr show | grep -E "(inet |UP|DOWN)"
echo
echo "路由表:"
ip route | head -5
echo
echo "DNS配置:"
cat /etc/resolv.conf | grep nameserver
echo

# 文件系统检查
echo "5. 文件系统检查"
echo "================"
echo "磁盘使用情况:"
df -h | grep -E "(Filesystem|/dev/)"
echo
echo "重要目录权限:"
ls -ld /opt/smart-classroom /var/lib/smart-classroom /var/log/smart-classroom
echo

# 进程检查
echo "6. 进程检查"
echo "============"
echo "相关进程:"
ps aux | grep -E "(python|mediamtx|nginx)" | grep -v grep
echo

# 日志摘要
echo "7. 最近错误日志"
echo "================"
if [ -f "/var/log/smart-classroom/app.log" ]; then
    echo "应用错误 (最近10条):"
    grep -i error /var/log/smart-classroom/app.log | tail -10
fi
echo

if [ -f "/var/log/nginx/error.log" ]; then
    echo "Nginx错误 (最近5条):"
    tail -5 /var/log/nginx/error.log
fi
echo

echo "=== 诊断报告完成 ==="
```

#### 2. 性能分析脚本

```bash
#!/bin/bash
# 性能分析脚本 - performance_analysis.sh

DURATION=${1:-60}  # 默认监控60秒

echo "=== 性能分析报告 ==="
echo "分析时间: $(date)"
echo "监控时长: ${DURATION}秒"
echo

# CPU分析
echo "1. CPU性能分析"
echo "================"
echo "CPU使用率统计 (${DURATION}秒):"
sar -u 1 $DURATION | tail -1
echo

# 内存分析
echo "2. 内存性能分析"
echo "================"
echo "内存使用统计:"
free -h
echo
echo "内存使用趋势 (${DURATION}秒):"
sar -r 1 $DURATION | tail -1
echo

# 磁盘I/O分析
echo "3. 磁盘I/O分析"
echo "==============="
echo "磁盘I/O统计 (${DURATION}秒):"
iostat -x 1 $DURATION | tail -10
echo

# 网络分析
echo "4. 网络性能分析"
echo "================"
echo "网络流量统计 (${DURATION}秒):"
sar -n DEV 1 $DURATION | grep -E "(Average|eth0|ens)" | tail -2
echo

# 进程分析
echo "5. 进程性能分析"
echo "================"
echo "CPU占用最高的进程:"
ps aux --sort=-%cpu | head -10
echo
echo "内存占用最高的进程:"
ps aux --sort=-%mem | head -10
echo

# 系统负载分析
echo "6. 系统负载分析"
echo "================"
echo "负载平均值:"
uptime
echo
echo "负载历史 (${DURATION}秒):"
sar -q 1 $DURATION | tail -1
echo

echo "=== 性能分析完成 ==="
```

## 备份和恢复

### 1. 自动备份脚本

```bash
#!/bin/bash
# 自动备份脚本 - backup.sh

BACKUP_ROOT="/backup/smart-classroom"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# 创建备份目录
mkdir -p "$BACKUP_ROOT"/{database,config,logs,full}

echo "=== 智慧课堂系统备份 ==="
echo "备份时间: $(date)"
echo "备份目录: $BACKUP_ROOT"
echo

# 1. 数据库备份
echo "1. 备份数据库..."
DB_BACKUP="$BACKUP_ROOT/database/smart_classroom_$DATE.db"
if [ -f "/var/lib/smart-classroom/smart_classroom.db" ]; then
    sudo -u smartclass cp "/var/lib/smart-classroom/smart_classroom.db" "$DB_BACKUP"
    gzip "$DB_BACKUP"
    echo "✅ 数据库备份完成: ${DB_BACKUP}.gz"
else
    echo "❌ 数据库文件不存在"
fi

# 2. 配置文件备份
echo "2. 备份配置文件..."
CONFIG_BACKUP="$BACKUP_ROOT/config/config_$DATE.tar.gz"
tar -czf "$CONFIG_BACKUP" -C /opt/smart-classroom config/
echo "✅ 配置文件备份完成: $CONFIG_BACKUP"

# 3. 日志备份
echo "3. 备份日志文件..."
LOG_BACKUP="$BACKUP_ROOT/logs/logs_$DATE.tar.gz"
if [ -d "/var/log/smart-classroom" ]; then
    tar -czf "$LOG_BACKUP" -C /var/log smart-classroom/
    echo "✅ 日志文件备份完成: $LOG_BACKUP"
fi

# 4. 完整系统备份（可选）
if [ "$1" = "--full" ]; then
    echo "4. 完整系统备份..."
    FULL_BACKUP="$BACKUP_ROOT/full/full_backup_$DATE.tar.gz"
    tar -czf "$FULL_BACKUP" \
        --exclude="/opt/smart-classroom/venv" \
        --exclude="/opt/smart-classroom/.git" \
        /opt/smart-classroom/
    echo "✅ 完整系统备份完成: $FULL_BACKUP"
fi

# 5. 清理过期备份
echo "5. 清理过期备份..."
find "$BACKUP_ROOT" -type f -mtime +$RETENTION_DAYS -delete
echo "✅ 过期备份清理完成"

# 6. 备份验证
echo "6. 备份验证..."
BACKUP_SIZE=$(du -sh "$BACKUP_ROOT" | cut -f1)
BACKUP_COUNT=$(find "$BACKUP_ROOT" -type f | wc -l)
echo "备份总大小: $BACKUP_SIZE"
echo "备份文件数: $BACKUP_COUNT"

echo "=== 备份完成 ==="
```

### 2. 恢复脚本

```bash
#!/bin/bash
# 系统恢复脚本 - restore.sh

BACKUP_ROOT="/backup/smart-classroom"

echo "=== 智慧课堂系统恢复 ==="
echo "恢复时间: $(date)"
echo

# 显示可用备份
echo "可用备份列表:"
echo "数据库备份:"
ls -lt "$BACKUP_ROOT/database/" | head -5
echo
echo "配置备份:"
ls -lt "$BACKUP_ROOT/config/" | head -5
echo

# 确认恢复操作
read -p "请输入要恢复的备份日期 (格式: YYYYMMDD_HHMMSS): " BACKUP_DATE

if [ -z "$BACKUP_DATE" ]; then
    echo "❌ 未指定备份日期"
    exit 1
fi

# 停止服务
echo "停止相关服务..."
sudo systemctl stop smart-classroom-backend
sudo systemctl stop mediamtx

# 恢复数据库
DB_BACKUP="$BACKUP_ROOT/database/smart_classroom_${BACKUP_DATE}.db.gz"
if [ -f "$DB_BACKUP" ]; then
    echo "恢复数据库..."
    # 备份当前数据库
    sudo -u smartclass cp "/var/lib/smart-classroom/smart_classroom.db" "/var/lib/smart-classroom/smart_classroom.db.backup"
    
    # 恢复数据库
    gunzip -c "$DB_BACKUP" | sudo -u smartclass tee "/var/lib/smart-classroom/smart_classroom.db" > /dev/null
    echo "✅ 数据库恢复完成"
else
    echo "❌ 数据库备份文件不存在: $DB_BACKUP"
fi

# 恢复配置文件
CONFIG_BACKUP="$BACKUP_ROOT/config/config_${BACKUP_DATE}.tar.gz"
if [ -f "$CONFIG_BACKUP" ]; then
    echo "恢复配置文件..."
    # 备份当前配置
    sudo cp -r "/opt/smart-classroom/config" "/opt/smart-classroom/config.backup"
    
    # 恢复配置
    sudo tar -xzf "$CONFIG_BACKUP" -C /opt/smart-classroom/
    sudo chown -R smartclass:smartclass /opt/smart-classroom/config/
    echo "✅ 配置文件恢复完成"
else
    echo "❌ 配置备份文件不存在: $CONFIG_BACKUP"
fi

# 重启服务
echo "重启服务..."
sudo systemctl start mediamtx
sudo systemctl start smart-classroom-backend

# 验证恢复
sleep 5
echo "验证服务状态..."
if systemctl is-active --quiet smart-classroom-backend; then
    echo "✅ 后端服务运行正常"
else
    echo "❌ 后端服务启动失败"
fi

if systemctl is-active --quiet mediamtx; then
    echo "✅ MediaMTX服务运行正常"
else
    echo "❌ MediaMTX服务启动失败"
fi

echo "=== 恢复完成 ==="
```

## 联系支持

如果遇到无法解决的问题，请联系技术支持：

- 📧 技术支持邮箱: <EMAIL>
- 📞 技术支持热线: 400-123-4567 (工作日 9:00-18:00)
- 🌐 在线文档: https://docs.smartclassroom.com
- 💬 技术社区: https://community.smartclassroom.com
- 🎫 工单系统: https://support.smartclassroom.com

### 提交问题时请提供：

1. 系统诊断报告 (`./scripts/system_diagnosis.sh`)
2. 相关错误日志
3. 问题复现步骤
4. 系统环境信息
5. 最近的系统变更记录