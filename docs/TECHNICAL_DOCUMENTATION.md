# 智慧课堂系统技术文档

## 目录
1. [系统架构](#系统架构)
2. [技术栈详解](#技术栈详解)
3. [核心模块设计](#核心模块设计)
4. [数据库设计](#数据库设计)
5. [网络通信协议](#网络通信协议)
6. [安全机制](#安全机制)
7. [性能优化](#性能优化)
8. [部署架构](#部署架构)

## 系统架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   教师端应用     │    │   小组端应用     │    │   学生端Web     │
│   (PyQt5)       │    │   (PyQt5)       │    │   (HTML5/JS)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flask后端     │    │   MediaMTX      │    │   SQLite数据库  │
│   API服务       │    │   流媒体服务     │    │   数据存储      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   UDP广播服务   │
                    │   设备发现      │
                    └─────────────────┘
```

### 分层架构设计

#### 1. 表示层 (Presentation Layer)
- **教师端界面**: PyQt5实现的桌面应用，提供设备管理、分组控制、视频监控等功能
- **小组端界面**: PyQt5实现的协作应用，支持投屏、白板、视频播放等功能
- **学生端界面**: HTML5/JavaScript实现的Web应用，支持移动端访问

#### 2. 业务逻辑层 (Business Logic Layer)
- **设备管理服务**: 处理设备发现、连接管理、状态监控
- **分组管理服务**: 处理学生分组、小组状态管理
- **视频流服务**: 处理屏幕捕获、视频编码、流分发
- **互动服务**: 处理答题、投票、白板协作
- **文件服务**: 处理文件上传、分发、权限控制

#### 3. 数据访问层 (Data Access Layer)
- **数据模型**: SQLAlchemy ORM模型定义
- **数据访问对象**: DAO模式实现数据操作
- **缓存管理**: Redis缓存热点数据

#### 4. 基础设施层 (Infrastructure Layer)
- **网络通信**: WebSocket、UDP、HTTP协议支持
- **安全框架**: 认证、授权、加密、审计
- **监控日志**: 系统监控、日志记录、性能分析

## 技术栈详解

### 后端技术栈

#### Flask框架
```python
# 核心配置
app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY')
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///classroom.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# 扩展组件
db = SQLAlchemy(app)
socketio = SocketIO(app, cors_allowed_origins="*")
jwt = JWTManager(app)
```

#### 数据库技术
- **SQLite**: 轻量级关系数据库，适合单机部署
- **SQLAlchemy**: Python ORM框架，提供对象关系映射
- **Alembic**: 数据库迁移工具

#### 视频流技术
- **MediaMTX**: 高性能流媒体服务器
- **FFmpeg**: 视频编码解码库
- **python-vlc**: Python VLC绑定，用于视频播放

### 前端技术栈

#### PyQt5桌面应用
```python
# 主窗口框架
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setupUI()
        self.setupConnections()
        self.setupServices()
    
    def setupUI(self):
        # 界面布局设计
        pass
    
    def setupConnections(self):
        # 信号槽连接
        pass
    
    def setupServices(self):
        # 服务初始化
        pass
```

#### Web前端技术
- **HTML5**: 现代Web标准，支持多媒体和移动设备
- **JavaScript ES6+**: 现代JavaScript特性
- **WebSocket**: 实时双向通信
- **Canvas API**: 白板绘图功能
- **WebRTC**: 点对点音视频通信

## 核心模块设计

### 1. 设备管理模块

#### 设备发现机制
```python
class DeviceDiscoveryService:
    def __init__(self):
        self.udp_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.broadcast_port = 8888
        self.devices = {}
    
    def start_discovery(self):
        """启动设备发现服务"""
        self.udp_socket.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        self.udp_socket.bind(('', self.broadcast_port))
        
        while True:
            data, addr = self.udp_socket.recvfrom(1024)
            self.handle_device_message(data, addr)
    
    def broadcast_presence(self):
        """广播设备存在信息"""
        message = {
            'type': 'device_presence',
            'device_id': self.device_id,
            'device_type': self.device_type,
            'capabilities': self.capabilities,
            'timestamp': time.time()
        }
        
        broadcast_data = json.dumps(message).encode()
        self.udp_socket.sendto(broadcast_data, ('<broadcast>', self.broadcast_port))
```

#### 连接管理
```python
class ConnectionManager:
    def __init__(self):
        self.connections = {}
        self.heartbeat_interval = 30
        self.connection_timeout = 60
    
    def establish_connection(self, device_id, websocket):
        """建立WebSocket连接"""
        self.connections[device_id] = {
            'websocket': websocket,
            'last_heartbeat': time.time(),
            'status': 'connected'
        }
        
        # 启动心跳检测
        self.start_heartbeat_monitor(device_id)
    
    def send_heartbeat(self, device_id):
        """发送心跳包"""
        if device_id in self.connections:
            websocket = self.connections[device_id]['websocket']
            websocket.send(json.dumps({'type': 'heartbeat'}))
```

### 2. 视频流处理模块

#### 屏幕捕获
```python
class ScreenCaptureService:
    def __init__(self):
        self.ffmpeg_process = None
        self.capture_resolution = "1920x1080"
        self.capture_fps = 30
    
    def start_screen_capture(self, display_id=":0"):
        """启动屏幕捕获"""
        ffmpeg_cmd = [
            'ffmpeg',
            '-f', 'x11grab',
            '-r', str(self.capture_fps),
            '-s', self.capture_resolution,
            '-i', display_id,
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-tune', 'zerolatency',
            '-f', 'rtmp',
            f'rtmp://localhost:1935/live/screen_{display_id}'
        ]
        
        self.ffmpeg_process = subprocess.Popen(ffmpeg_cmd)
        return f'rtmp://localhost:1935/live/screen_{display_id}'
```

#### 视频流分发
```python
class VideoStreamDistributor:
    def __init__(self):
        self.mediamtx_config = {
            'rtmpAddress': ':1935',
            'hlsAddress': ':8888',
            'webrtcAddress': ':8889'
        }
        self.active_streams = {}
    
    def publish_stream(self, stream_key, source_url):
        """发布视频流"""
        stream_info = {
            'stream_key': stream_key,
            'source_url': source_url,
            'subscribers': [],
            'start_time': time.time()
        }
        
        self.active_streams[stream_key] = stream_info
        return f'rtmp://localhost:1935/live/{stream_key}'
    
    def subscribe_stream(self, stream_key, client_id):
        """订阅视频流"""
        if stream_key in self.active_streams:
            self.active_streams[stream_key]['subscribers'].append(client_id)
            return f'http://localhost:8888/{stream_key}/index.m3u8'
```

### 3. 协同白板模块

#### 白板引擎设计
```python
class WhiteboardEngine:
    def __init__(self):
        self.rooms = {}
        self.drawing_operations = []
    
    def create_room(self, room_id, creator_id):
        """创建白板房间"""
        self.rooms[room_id] = {
            'creator': creator_id,
            'participants': [creator_id],
            'drawing_data': [],
            'version': 0,
            'created_at': time.time()
        }
    
    def add_drawing_operation(self, room_id, operation):
        """添加绘图操作"""
        if room_id in self.rooms:
            self.rooms[room_id]['drawing_data'].append(operation)
            self.rooms[room_id]['version'] += 1
            
            # 广播操作到所有参与者
            self.broadcast_operation(room_id, operation)
    
    def broadcast_operation(self, room_id, operation):
        """广播绘图操作"""
        room = self.rooms[room_id]
        for participant_id in room['participants']:
            socketio.emit('drawing_operation', operation, room=participant_id)
```

## 数据库设计

### 核心表结构

#### 设备表 (devices)
```sql
CREATE TABLE devices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    device_id VARCHAR(50) UNIQUE NOT NULL,
    device_type VARCHAR(20) NOT NULL,
    device_name VARCHAR(100),
    ip_address VARCHAR(15),
    mac_address VARCHAR(17),
    status VARCHAR(20) DEFAULT 'offline',
    capabilities TEXT,
    last_heartbeat DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 课堂表 (classrooms)
```sql
CREATE TABLE classrooms (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    classroom_id VARCHAR(50) UNIQUE NOT NULL,
    teacher_id VARCHAR(50) NOT NULL,
    classroom_name VARCHAR(100) NOT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME,
    status VARCHAR(20) DEFAULT 'active',
    settings TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 小组表 (groups)
```sql
CREATE TABLE groups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    group_id VARCHAR(50) UNIQUE NOT NULL,
    classroom_id VARCHAR(50) NOT NULL,
    group_name VARCHAR(50) NOT NULL,
    device_id VARCHAR(50),
    member_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (classroom_id) REFERENCES classrooms(classroom_id),
    FOREIGN KEY (device_id) REFERENCES devices(device_id)
);
```

#### 学生表 (students)
```sql
CREATE TABLE students (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id VARCHAR(50) UNIQUE NOT NULL,
    student_name VARCHAR(100) NOT NULL,
    group_id VARCHAR(50),
    classroom_id VARCHAR(50),
    device_info TEXT,
    attendance_status VARCHAR(20) DEFAULT 'absent',
    join_time DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (group_id) REFERENCES groups(group_id),
    FOREIGN KEY (classroom_id) REFERENCES classrooms(classroom_id)
);
```

### 数据访问层设计

#### 基础DAO类
```python
class BaseDAO:
    def __init__(self, model_class):
        self.model_class = model_class
    
    def create(self, **kwargs):
        """创建记录"""
        instance = self.model_class(**kwargs)
        db.session.add(instance)
        db.session.commit()
        return instance
    
    def get_by_id(self, id):
        """根据ID获取记录"""
        return self.model_class.query.get(id)
    
    def update(self, id, **kwargs):
        """更新记录"""
        instance = self.get_by_id(id)
        if instance:
            for key, value in kwargs.items():
                setattr(instance, key, value)
            db.session.commit()
        return instance
    
    def delete(self, id):
        """删除记录"""
        instance = self.get_by_id(id)
        if instance:
            db.session.delete(instance)
            db.session.commit()
        return instance
```

## 网络通信协议

### WebSocket消息格式

#### 消息结构
```json
{
    "type": "message_type",
    "sender_id": "sender_device_id",
    "target_id": "target_device_id",
    "timestamp": 1642780800,
    "data": {
        // 具体消息内容
    }
}
```

#### 消息类型定义
```python
MESSAGE_TYPES = {
    # 设备管理
    'device_register': '设备注册',
    'device_heartbeat': '设备心跳',
    'device_status_update': '设备状态更新',
    
    # 分组管理
    'group_create': '创建小组',
    'group_update': '更新小组',
    'student_assign': '学生分组',
    
    # 视频流控制
    'stream_start': '开始推流',
    'stream_stop': '停止推流',
    'stream_subscribe': '订阅流',
    
    # 白板协作
    'whiteboard_operation': '白板操作',
    'whiteboard_sync': '白板同步',
    
    # 互动答题
    'question_publish': '发布题目',
    'answer_submit': '提交答案',
    'result_broadcast': '广播结果'
}
```

### UDP广播协议

#### 设备发现消息
```json
{
    "type": "device_discovery",
    "device_id": "teacher_001",
    "device_type": "teacher",
    "device_name": "教师端-001",
    "ip_address": "*************",
    "port": 5000,
    "capabilities": ["screen_share", "whiteboard", "file_transfer"],
    "timestamp": 1642780800
}
```

## 安全机制

### 认证授权体系

#### JWT令牌结构
```python
def generate_jwt_token(user_id, device_id, permissions):
    payload = {
        'user_id': user_id,
        'device_id': device_id,
        'permissions': permissions,
        'iat': datetime.utcnow(),
        'exp': datetime.utcnow() + timedelta(hours=24)
    }
    
    return jwt.encode(payload, app.config['SECRET_KEY'], algorithm='HS256')
```

#### 权限控制装饰器
```python
def require_permission(permission):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            token = request.headers.get('Authorization')
            if not token:
                return jsonify({'error': 'Token missing'}), 401
            
            try:
                payload = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
                if permission not in payload.get('permissions', []):
                    return jsonify({'error': 'Permission denied'}), 403
            except jwt.InvalidTokenError:
                return jsonify({'error': 'Invalid token'}), 401
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
```

### 数据加密

#### AES加密实现
```python
class EncryptionService:
    def __init__(self, key):
        self.key = key.encode() if isinstance(key, str) else key
    
    def encrypt(self, plaintext):
        """AES-GCM加密"""
        cipher = AES.new(self.key, AES.MODE_GCM)
        ciphertext, tag = cipher.encrypt_and_digest(plaintext.encode())
        
        return {
            'ciphertext': base64.b64encode(ciphertext).decode(),
            'nonce': base64.b64encode(cipher.nonce).decode(),
            'tag': base64.b64encode(tag).decode()
        }
    
    def decrypt(self, encrypted_data):
        """AES-GCM解密"""
        cipher = AES.new(
            self.key, 
            AES.MODE_GCM, 
            nonce=base64.b64decode(encrypted_data['nonce'])
        )
        
        plaintext = cipher.decrypt_and_verify(
            base64.b64decode(encrypted_data['ciphertext']),
            base64.b64decode(encrypted_data['tag'])
        )
        
        return plaintext.decode()
```

## 性能优化

### 视频流优化

#### 编码参数优化
```python
FFMPEG_ENCODING_PARAMS = {
    'video_codec': 'libx264',
    'preset': 'ultrafast',
    'tune': 'zerolatency',
    'crf': '23',
    'maxrate': '2000k',
    'bufsize': '4000k',
    'keyint': '60',
    'profile': 'baseline'
}
```

#### 自适应码率
```python
class AdaptiveBitrateController:
    def __init__(self):
        self.bitrate_levels = [500, 1000, 1500, 2000, 3000]  # kbps
        self.current_level = 2
    
    def adjust_bitrate(self, network_condition):
        """根据网络状况调整码率"""
        if network_condition['packet_loss'] > 0.05:
            self.current_level = max(0, self.current_level - 1)
        elif network_condition['rtt'] < 50 and network_condition['bandwidth'] > 5000:
            self.current_level = min(4, self.current_level + 1)
        
        return self.bitrate_levels[self.current_level]
```

### 数据库优化

#### 连接池配置
```python
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

engine = create_engine(
    'sqlite:///classroom.db',
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600
)
```

#### 查询优化
```python
# 使用索引优化查询
class Device(db.Model):
    __tablename__ = 'devices'
    
    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.String(50), unique=True, nullable=False, index=True)
    device_type = db.Column(db.String(20), nullable=False, index=True)
    status = db.Column(db.String(20), default='offline', index=True)
    
    # 复合索引
    __table_args__ = (
        db.Index('idx_device_type_status', 'device_type', 'status'),
    )
```

## 部署架构

### 单机部署架构
```
┌─────────────────────────────────────────────────────────────┐
│                    统信UOS服务器                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Flask     │  │  MediaMTX   │  │   SQLite    │         │
│  │   后端服务   │  │  流媒体服务  │  │   数据库     │         │
│  │   :5000     │  │   :1935     │  │             │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                    局域网络层                                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   教师端     │  │   小组端     │  │   学生端     │         │
│  │  PyQt5应用  │  │  PyQt5应用  │  │   Web浏览器  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 分布式部署架构
```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   负载均衡器     │  │   应用服务器     │  │   数据库服务器   │
│    Nginx        │  │    Flask        │  │   PostgreSQL    │
│    :80/:443     │  │    :5000        │  │    :5432        │
└─────────────────┘  └─────────────────┘  └─────────────────┘
         │                     │                     │
         └─────────────────────┼─────────────────────┘
                               │
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   流媒体服务器   │  │   缓存服务器     │  │   文件存储服务   │
│   MediaMTX      │  │    Redis        │  │     MinIO       │
│   :1935/:8888   │  │    :6379        │  │    :9000        │
└─────────────────┘  └─────────────────┘  └─────────────────┘
```

### 容器化部署

#### Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    ffmpeg \
    xvfb \
    x11-utils \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制应用代码
COPY . .

# 设置环境变量
ENV FLASK_APP=backend/app.py
ENV FLASK_ENV=production

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "backend.app:app"]
```

#### Docker Compose配置
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - DATABASE_URL=******************************/classroom
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
      - mediamtx

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=classroom
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

  mediamtx:
    image: aler9/rtsp-simple-server
    ports:
      - "1935:1935"
      - "8888:8888"
    volumes:
      - ./mediamtx.yml:/rtsp-simple-server.yml

volumes:
  postgres_data:
```

## 监控和运维

### 系统监控

#### 性能指标收集
```python
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'cpu_usage': 0,
            'memory_usage': 0,
            'network_io': 0,
            'disk_io': 0,
            'active_connections': 0,
            'stream_count': 0
        }
    
    def collect_metrics(self):
        """收集系统性能指标"""
        import psutil
        
        self.metrics['cpu_usage'] = psutil.cpu_percent()
        self.metrics['memory_usage'] = psutil.virtual_memory().percent
        self.metrics['network_io'] = psutil.net_io_counters()
        self.metrics['disk_io'] = psutil.disk_io_counters()
        
        return self.metrics
```

#### 日志管理
```python
import logging
from logging.handlers import RotatingFileHandler

def setup_logging(app):
    """配置日志系统"""
    if not app.debug:
        file_handler = RotatingFileHandler(
            'logs/smart_classroom.log',
            maxBytes=10240000,
            backupCount=10
        )
        
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('Smart Classroom System startup')
```

---

**文档版本**: v1.0.0  
**最后更新**: 2025年7月21日  
**维护团队**: 智慧课堂系统开发团队