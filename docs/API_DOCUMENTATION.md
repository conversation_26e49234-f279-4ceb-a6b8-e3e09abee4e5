# 智慧课堂系统 API 文档

## 目录
1. [API概述](#API概述)
2. [认证机制](#认证机制)
3. [设备管理API](#设备管理API)
4. [课堂管理API](#课堂管理API)
5. [分组管理API](#分组管理API)
6. [视频流API](#视频流API)
7. [互动答题API](#互动答题API)
8. [文件管理API](#文件管理API)
9. [白板协作API](#白板协作API)
10. [WebSocket事件](#WebSocket事件)
11. [错误码说明](#错误码说明)

## API概述

### 基础信息
- **Base URL**: `http://localhost:5000/api/v1`
- **协议**: HTTP/HTTPS + WebSocket
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式
```json
{
    "success": true,
    "data": {},
    "message": "操作成功",
    "timestamp": 1642780800,
    "request_id": "req_123456789"
}
```

### 错误响应格式
```json
{
    "success": false,
    "error": {
        "code": "INVALID_PARAMETER",
        "message": "参数无效",
        "details": "device_id不能为空"
    },
    "timestamp": 1642780800,
    "request_id": "req_123456789"
}
```

## 认证机制

### JWT Token认证

#### 获取Token
```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "username": "teacher001",
    "password": "password123",
    "device_id": "teacher_device_001"
}
```

**响应**:
```json
{
    "success": true,
    "data": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expires_in": 86400,
        "user_info": {
            "user_id": "teacher001",
            "role": "teacher",
            "permissions": ["device_manage", "group_manage", "stream_control"]
        }
    }
}
```

#### 刷新Token
```http
POST /api/v1/auth/refresh
Authorization: Bearer <refresh_token>
```

#### 使用Token
所有需要认证的API请求都需要在Header中包含Token:
```http
Authorization: Bearer <access_token>
```

## 设备管理API

### 1. 注册设备

```http
POST /api/v1/devices/register
Authorization: Bearer <token>
Content-Type: application/json

{
    "device_id": "group_device_001",
    "device_type": "group",
    "device_name": "小组设备-001",
    "ip_address": "*************",
    "mac_address": "00:11:22:33:44:55",
    "capabilities": ["screen_share", "whiteboard", "video_play"]
}
```

**响应**:
```json
{
    "success": true,
    "data": {
        "device_id": "group_device_001",
        "registration_time": "2025-01-21T10:30:00Z",
        "status": "registered"
    }
}
```

### 2. 获取设备列表

```http
GET /api/v1/devices?type=group&status=online
Authorization: Bearer <token>
```

**响应**:
```json
{
    "success": true,
    "data": {
              "d
evices": [
            {
                "device_id": "group_device_001",
                "device_type": "group",
                "device_name": "小组设备-001",
                "ip_address": "*************",
                "status": "online",
                "last_heartbeat": "2025-01-21T10:35:00Z",
                "capabilities": ["screen_share", "whiteboard", "video_play"],
                "group_id": "group_001"
            },
            {
                "device_id": "group_device_002",
                "device_type": "group",
                "device_name": "小组设备-002",
                "ip_address": "*************",
                "status": "online",
                "last_heartbeat": "2025-01-21T10:34:30Z",
                "capabilities": ["screen_share", "whiteboard", "video_play"],
                "group_id": "group_002"
            }
        ],
        "total": 2,
        "page": 1,
        "per_page": 10
    }
}
```

### 3. 更新设备状态

```http
PUT /api/v1/devices/{device_id}/status
Authorization: Bearer <token>
Content-Type: application/json

{
    "status": "online",
    "heartbeat": "2025-01-21T10:35:00Z"
}
```

### 4. 删除设备

```http
DELETE /api/v1/devices/{device_id}
Authorization: Bearer <token>
```

## 课堂管理API

### 1. 创建课堂

```http
POST /api/v1/classrooms
Authorization: Bearer <token>
Content-Type: application/json

{
    "classroom_name": "数学课-三年级A班",
    "teacher_id": "teacher001",
    "subject": "数学",
    "grade": "三年级",
    "class_name": "A班",
    "start_time": "2025-01-21T14:00:00Z",
    "duration": 45,
    "settings": {
        "allow_group_interaction": true,
        "enable_whiteboard": true,
        "enable_screen_share": true,
        "max_groups": 8
    }
}
```

**响应**:
```json
{
    "success": true,
    "data": {
        "classroom_id": "classroom_001",
        "classroom_name": "数学课-三年级A班",
        "status": "created",
        "join_code": "ABC123",
        "created_time": "2025-01-21T13:30:00Z"
    }
}
```

### 2. 开始课堂

```http
POST /api/v1/classrooms/{classroom_id}/start
Authorization: Bearer <token>
```

### 3. 结束课堂

```http
POST /api/v1/classrooms/{classroom_id}/end
Authorization: Bearer <token>
```

### 4. 获取课堂信息

```http
GET /api/v1/classrooms/{classroom_id}
Authorization: Bearer <token>
```

**响应**:
```json
{
    "success": true,
    "data": {
        "classroom_id": "classroom_001",
        "classroom_name": "数学课-三年级A班",
        "teacher_id": "teacher001",
        "status": "active",
        "start_time": "2025-01-21T14:00:00Z",
        "current_groups": 6,
        "total_students": 24,
        "settings": {
            "allow_group_interaction": true,
            "enable_whiteboard": true,
            "enable_screen_share": true
        }
    }
}
```

## 分组管理API

### 1. 创建分组

```http
POST /api/v1/classrooms/{classroom_id}/groups
Authorization: Bearer <token>
Content-Type: application/json

{
    "group_name": "第一组",
    "device_id": "group_device_001",
    "max_members": 4,
    "group_leader": "student001"
}
```

**响应**:
```json
{
    "success": true,
    "data": {
        "group_id": "group_001",
        "group_name": "第一组",
        "device_id": "group_device_001",
        "status": "created",
        "members": [],
        "created_time": "2025-01-21T14:05:00Z"
    }
}
```

### 2. 添加学生到分组

```http
POST /api/v1/groups/{group_id}/members
Authorization: Bearer <token>
Content-Type: application/json

{
    "student_ids": ["student001", "student002", "student003"]
}
```

### 3. 获取分组列表

```http
GET /api/v1/classrooms/{classroom_id}/groups
Authorization: Bearer <token>
```

### 4. 分组投屏控制

```http
POST /api/v1/groups/{group_id}/screen-share
Authorization: Bearer <token>
Content-Type: application/json

{
    "action": "start",
    "source_device": "teacher_device_001",
    "target_groups": ["group_001", "group_002"]
}
```

## 视频流API

### 1. 开始视频流

```http
POST /api/v1/streams/start
Authorization: Bearer <token>
Content-Type: application/json

{
    "stream_type": "teacher_to_groups",
    "source_device": "teacher_device_001",
    "target_devices": ["group_device_001", "group_device_002"],
    "quality": "720p",
    "audio_enabled": true
}
```

**响应**:
```json
{
    "success": true,
    "data": {
        "stream_id": "stream_001",
        "rtmp_url": "rtmp://localhost:1935/live/stream_001",
        "hls_url": "http://localhost:8080/hls/stream_001.m3u8",
        "status": "starting"
    }
}
```

### 2. 停止视频流

```http
POST /api/v1/streams/{stream_id}/stop
Authorization: Bearer <token>
```

### 3. 获取流状态

```http
GET /api/v1/streams/{stream_id}/status
Authorization: Bearer <token>
```

## 互动答题API

### 1. 创建答题活动

```http
POST /api/v1/classrooms/{classroom_id}/quiz
Authorization: Bearer <token>
Content-Type: application/json

{
    "title": "数学练习题",
    "questions": [
        {
            "question_id": "q001",
            "question_text": "2 + 3 = ?",
            "question_type": "multiple_choice",
            "options": ["4", "5", "6", "7"],
            "correct_answer": "5",
            "points": 10
        }
    ],
    "time_limit": 300,
    "allow_group_discussion": true
}
```

### 2. 开始答题

```http
POST /api/v1/quiz/{quiz_id}/start
Authorization: Bearer <token>
```

### 3. 提交答案

```http
POST /api/v1/quiz/{quiz_id}/submit
Authorization: Bearer <token>
Content-Type: application/json

{
    "group_id": "group_001",
    "answers": [
        {
            "question_id": "q001",
            "answer": "5",
            "submit_time": "2025-01-21T14:15:30Z"
        }
    ]
}
```

### 4. 获取答题结果

```http
GET /api/v1/quiz/{quiz_id}/results
Authorization: Bearer <token>
```

## 文件管理API

### 1. 上传文件

```http
POST /api/v1/files/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
    "file": <binary_data>,
    "file_type": "document",
    "classroom_id": "classroom_001",
    "description": "课件PPT"
}
```

### 2. 获取文件列表

```http
GET /api/v1/files?classroom_id=classroom_001&type=document
Authorization: Bearer <token>
```

### 3. 下载文件

```http
GET /api/v1/files/{file_id}/download
Authorization: Bearer <token>
```

### 4. 分享文件到分组

```http
POST /api/v1/files/{file_id}/share
Authorization: Bearer <token>
Content-Type: application/json

{
    "target_groups": ["group_001", "group_002"],
    "permissions": ["view", "download"]
}
```

## 白板协作API

### 1. 创建白板会话

```http
POST /api/v1/whiteboards
Authorization: Bearer <token>
Content-Type: application/json

{
    "classroom_id": "classroom_001",
    "whiteboard_name": "数学练习白板",
    "participants": ["group_001", "group_002"],
    "permissions": {
        "teacher": ["read", "write", "admin"],
        "groups": ["read", "write"]
    }
}
```

### 2. 获取白板内容

```http
GET /api/v1/whiteboards/{whiteboard_id}/content
Authorization: Bearer <token>
```

### 3. 保存白板内容

```http
POST /api/v1/whiteboards/{whiteboard_id}/save
Authorization: Bearer <token>
Content-Type: application/json

{
    "content": {
        "elements": [
            {
                "type": "line",
                "points": [[100, 100], [200, 200]],
                "color": "#000000",
                "width": 2
            }
        ]
    }
}
```

## WebSocket事件

### 连接WebSocket

```javascript
const ws = new WebSocket('ws://localhost:5000/ws');
ws.onopen = function() {
    // 发送认证信息
    ws.send(JSON.stringify({
        type: 'auth',
        token: 'your_jwt_token',
        device_id: 'your_device_id'
    }));
};
```

### 事件类型

#### 1. 设备状态变化
```json
{
    "type": "device_status_changed",
    "data": {
        "device_id": "group_device_001",
        "status": "online",
        "timestamp": "2025-01-21T14:20:00Z"
    }
}
```

#### 2. 分组消息
```json
{
    "type": "group_message",
    "data": {
        "group_id": "group_001",
        "sender": "student001",
        "message": "我们完成了练习题",
        "timestamp": "2025-01-21T14:25:00Z"
    }
}
```

#### 3. 投屏控制
```json
{
    "type": "screen_share_control",
    "data": {
        "action": "start",
        "source_device": "teacher_device_001",
        "target_device": "group_device_001",
        "stream_url": "rtmp://localhost:1935/live/stream_001"
    }
}
```

#### 4. 白板同步
```json
{
    "type": "whiteboard_update",
    "data": {
        "whiteboard_id": "wb_001",
        "operation": "draw",
        "element": {
            "type": "line",
            "points": [[150, 150], [250, 250]],
            "color": "#ff0000",
            "width": 3
        },
        "user_id": "teacher001"
    }
}
```

#### 5. 答题状态更新
```json
{
    "type": "quiz_status_update",
    "data": {
        "quiz_id": "quiz_001",
        "status": "started",
        "remaining_time": 295,
        "submitted_groups": ["group_001"]
    }
}
```

## 错误码说明

### 通用错误码
- `INVALID_PARAMETER`: 参数无效
- `UNAUTHORIZED`: 未授权访问
- `FORBIDDEN`: 权限不足
- `NOT_FOUND`: 资源不存在
- `INTERNAL_ERROR`: 服务器内部错误

### 设备相关错误码
- `DEVICE_NOT_FOUND`: 设备不存在
- `DEVICE_OFFLINE`: 设备离线
- `DEVICE_BUSY`: 设备忙碌中
- `DEVICE_REGISTRATION_FAILED`: 设备注册失败

### 课堂相关错误码
- `CLASSROOM_NOT_FOUND`: 课堂不存在
- `CLASSROOM_NOT_ACTIVE`: 课堂未激活
- `CLASSROOM_FULL`: 课堂人数已满
- `INVALID_JOIN_CODE`: 无效的加入码

### 分组相关错误码
- `GROUP_NOT_FOUND`: 分组不存在
- `GROUP_FULL`: 分组人数已满
- `STUDENT_ALREADY_IN_GROUP`: 学生已在其他分组中

### 流媒体相关错误码
- `STREAM_NOT_FOUND`: 流不存在
- `STREAM_START_FAILED`: 流启动失败
- `STREAM_QUALITY_NOT_SUPPORTED`: 不支持的流质量

### 文件相关错误码
- `FILE_NOT_FOUND`: 文件不存在
- `FILE_TOO_LARGE`: 文件过大
- `UNSUPPORTED_FILE_TYPE`: 不支持的文件类型
- `UPLOAD_FAILED`: 上传失败

---

## 使用示例

### 完整的课堂流程示例

```javascript
// 1. 教师登录
const loginResponse = await fetch('/api/v1/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        username: 'teacher001',
        password: 'password123',
        device_id: 'teacher_device_001'
    })
});
const { access_token } = await loginResponse.json();

// 2. 创建课堂
const classroomResponse = await fetch('/api/v1/classrooms', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${access_token}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        classroom_name: '数学课-三年级A班',
        teacher_id: 'teacher001',
        subject: '数学'
    })
});
const { classroom_id } = await classroomResponse.json();

// 3. 创建分组
const groupResponse = await fetch(`/api/v1/classrooms/${classroom_id}/groups`, {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${access_token}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        group_name: '第一组',
        device_id: 'group_device_001'
    })
});

// 4. 开始投屏
await fetch('/api/v1/streams/start', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${access_token}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        stream_type: 'teacher_to_groups',
        source_device: 'teacher_device_001',
        target_devices: ['group_device_001']
    })
});
```

---

**文档版本**: v1.0  
**最后更新**: 2025-01-21  
**维护者**: 智慧课堂开发团队