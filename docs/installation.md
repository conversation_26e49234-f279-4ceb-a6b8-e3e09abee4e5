# 智慧课堂系统安装指南

## 系统要求

### 硬件要求
- CPU: 4核心以上
- 内存: 8GB以上
- 存储: 50GB可用空间
- 网络: 千兆以太网
- 显卡: 支持硬件加速的显卡（推荐）

### 软件要求
- 操作系统: 统信UOS 20 或更高版本
- Python: 3.8 或更高版本
- FFmpeg: 4.0 或更高版本

## 安装步骤

### 1. 系统准备

更新系统包：
```bash
sudo apt update && sudo apt upgrade -y
```

安装基础依赖：
```bash
sudo apt install -y git curl wget build-essential
```

### 2. 获取源码

```bash
git clone <repository-url>
cd smart_classroom
```

### 3. 自动安装

运行自动安装脚本：
```bash
chmod +x scripts/install_dependencies.sh
./scripts/install_dependencies.sh
```

### 4. 手动安装（可选）

如果自动安装失败，可以手动安装：

#### 安装Python依赖
```bash
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

#### 安装MediaMTX
```bash
# 下载MediaMTX
wget https://github.com/bluenviron/mediamtx/releases/download/v1.2.1/mediamtx_v1.2.1_linux_amd64.tar.gz
tar -xzf mediamtx_v1.2.1_linux_amd64.tar.gz
sudo mv mediamtx /usr/local/bin/
sudo chmod +x /usr/local/bin/mediamtx
```

#### 安装系统工具
```bash
sudo apt install -y ffmpeg xdotool
```

### 5. 验证安装

运行测试：
```bash
source venv/bin/activate
python -m pytest tests/
```

启动服务：
```bash
./scripts/start_services.sh
```

检查服务状态：
```bash
curl http://localhost:5000/api/health
```

## 配置说明

### 网络配置
- 确保防火墙允许以下端口：
  - 5000: Flask后端服务
  - 8888: UDP设备发现
  - 1935: RTMP视频流
  - 8889: HTTP视频流

### 权限配置
- 确保用户有权限访问摄像头和麦克风
- 确保用户有权限使用xdotool进行屏幕控制

## 故障排除

### 常见问题

1. **PyQt5安装失败**
   ```bash
   sudo apt install python3-pyqt5 python3-pyqt5-dev
   ```

2. **MediaMTX启动失败**
   - 检查端口是否被占用
   - 检查权限设置

3. **视频功能异常**
   - 检查FFmpeg安装
   - 检查显卡驱动

4. **网络连接问题**
   - 检查防火墙设置
   - 检查网络配置

### 日志查看
```bash
# 查看后端日志
tail -f logs/backend.log

# 查看MediaMTX日志
tail -f logs/mediamtx.log
```

## 卸载

停止服务：
```bash
./scripts/stop_services.sh
```

删除文件：
```bash
rm -rf smart_classroom
sudo rm -f /usr/local/bin/mediamtx
```