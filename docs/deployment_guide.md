# 智慧课堂系统部署指南

## 概述

本文档详细介绍智慧课堂系统的部署、配置和维护方法。系统采用分布式架构，支持教师端、小组端和学生端的协同工作。

## 系统要求

### 硬件要求

| 组件 | 最低要求 | 推荐配置 |
|------|----------|----------|
| CPU | 4核心 2.0GHz | 8核心 3.0GHz |
| 内存 | 8GB | 16GB |
| 存储 | 50GB可用空间 | 100GB SSD |
| 网络 | 千兆以太网 | 千兆以太网 + WiFi 6 |
| 显卡 | 集成显卡 | 独立显卡 |

### 软件要求

| 软件 | 版本要求 | 说明 |
|------|----------|------|
| 操作系统 | 统信UOS 20+ / Ubuntu 20.04+ | 推荐统信UOS |
| Python | 3.8+ | 支持3.8-3.11 |
| 数据库 | SQLite 3.31+ | 内置支持 |
| Web服务器 | Nginx 1.18+ | 反向代理 |
| 流媒体 | MediaMTX 1.0+ | 视频流处理 |

### 网络要求

- **带宽**: 每个终端至少10Mbps上行带宽
- **延迟**: 局域网延迟 < 10ms
- **端口**: 需要开放以下端口
  - 80: HTTP Web服务
  - 443: HTTPS Web服务（可选）
  - 5000: Flask后端API
  - 1935: RTMP流媒体
  - 8889: WebRTC
  - 8888: UDP设备发现

## 快速部署

### 方法一：使用部署脚本（推荐）

```bash
# 1. 下载项目代码
git clone https://github.com/smartclassroom/smart-classroom-system.git
cd smart-classroom-system/smart_classroom

# 2. 运行部署脚本
sudo ./scripts/deploy.sh

# 3. 等待部署完成
# 脚本会自动完成所有配置步骤
```

### 方法二：使用Python安装器

```bash
# 1. 运行Python安装器
sudo python3 ./scripts/install.py

# 2. 按照提示完成安装
# 支持交互式配置选项
```

### 方法三：Docker部署（开发中）

```bash
# 使用Docker Compose部署
docker-compose up -d
```

## 详细部署步骤

### 1. 环境准备

#### 1.1 更新系统

```bash
# 统信UOS
sudo apt update && sudo apt upgrade -y

# 安装基础工具
sudo apt install -y curl wget git vim htop
```

#### 1.2 安装Python环境

```bash
# 安装Python和相关工具
sudo apt install -y python3 python3-pip python3-venv python3-dev

# 验证Python版本
python3 --version  # 应该 >= 3.8
```

#### 1.3 安装系统依赖

```bash
# GUI相关依赖
sudo apt install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libxcb-xinerama0 \
    libxcb-cursor0 \
    libxkbcommon-x11-0

# 多媒体处理
sudo apt install -y ffmpeg x11-utils xdotool

# Web服务器
sudo apt install -y nginx

# 数据库
sudo apt install -y sqlite3
```

### 2. 安装MediaMTX流媒体服务器

```bash
# 下载MediaMTX
wget https://github.com/bluenviron/mediamtx/releases/latest/download/mediamtx_v1.0.0_linux_amd64.tar.gz

# 解压并安装
tar -xzf mediamtx_v1.0.0_linux_amd64.tar.gz
sudo mv mediamtx /usr/local/bin/
sudo chmod +x /usr/local/bin/mediamtx

# 验证安装
mediamtx --version
```

### 3. 创建系统用户和目录

```bash
# 创建系统用户
sudo useradd -r -s /bin/false -d /var/lib/smart-classroom smartclass

# 创建目录结构
sudo mkdir -p /opt/smart-classroom
sudo mkdir -p /var/log/smart-classroom
sudo mkdir -p /var/lib/smart-classroom

# 设置权限
sudo chown -R smartclass:smartclass /var/log/smart-classroom
sudo chown -R smartclass:smartclass /var/lib/smart-classroom
```

### 4. 部署应用代码

```bash
# 复制项目文件
sudo cp -r . /opt/smart-classroom/
sudo chown -R smartclass:smartclass /opt/smart-classroom

# 创建Python虚拟环境
cd /opt/smart-classroom
sudo -u smartclass python3 -m venv venv

# 激活虚拟环境并安装依赖
sudo -u smartclass venv/bin/pip install --upgrade pip
sudo -u smartclass venv/bin/pip install -r requirements.txt
sudo -u smartclass venv/bin/pip install -e .
```

### 5. 配置文件设置

#### 5.1 MediaMTX配置

创建 `/opt/smart-classroom/config/mediamtx.yml`:

```yaml
# MediaMTX配置文件
logLevel: info
logDestinations: [stdout]
logFile: /var/log/smart-classroom/mediamtx.log

# API配置
api: yes
apiAddress: 127.0.0.1:9997

# RTMP配置
rtmp: yes
rtmpAddress: :1935

# WebRTC配置
webrtc: yes
webrtcAddress: :8889

# 路径配置
paths:
  all:
    source: publisher
    publishUser: ""
    publishPass: ""
    readUser: ""
    readPass: ""
```

#### 5.2 应用配置

创建 `/opt/smart-classroom/config/production.yml`:

```yaml
# 生产环境配置
app:
  name: "智慧课堂系统"
  version: "1.0.0"
  debug: false
  host: "0.0.0.0"
  port: 5000

database:
  url: "sqlite:///var/lib/smart-classroom/smart_classroom.db"
  echo: false

logging:
  level: "INFO"
  file: "/var/log/smart-classroom/app.log"
  max_bytes: 10485760  # 10MB
  backup_count: 5

mediamtx:
  rtmp_url: "rtmp://localhost:1935"
  api_url: "http://localhost:9997"

security:
  secret_key: "your-secret-key-here"
  jwt_secret: "your-jwt-secret-here"
  password_salt: "your-password-salt-here"
```

### 6. 系统服务配置

#### 6.1 后端服务

创建 `/etc/systemd/system/smart-classroom-backend.service`:

```ini
[Unit]
Description=Smart Classroom Backend Service
After=network.target

[Service]
Type=simple
User=smartclass
WorkingDirectory=/opt/smart-classroom
Environment=PATH=/opt/smart-classroom/venv/bin
ExecStart=/opt/smart-classroom/venv/bin/python -m backend.app
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

#### 6.2 MediaMTX服务

创建 `/etc/systemd/system/mediamtx.service`:

```ini
[Unit]
Description=MediaMTX Streaming Server
After=network.target

[Service]
Type=simple
User=smartclass
ExecStart=/usr/local/bin/mediamtx /opt/smart-classroom/config/mediamtx.yml
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

#### 6.3 启用服务

```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable smart-classroom-backend
sudo systemctl enable mediamtx

# 启动服务
sudo systemctl start smart-classroom-backend
sudo systemctl start mediamtx
```

### 7. Nginx配置

创建 `/etc/nginx/sites-available/smart-classroom`:

```nginx
server {
    listen 80;
    server_name _;
    
    # 静态文件
    location /static/ {
        alias /opt/smart-classroom/web_client/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket代理
    location /socket.io/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 默认页面
    location / {
        root /opt/smart-classroom/web_client;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
}
```

启用Nginx配置:

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/smart-classroom /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### 8. 数据库初始化

```bash
# 初始化数据库
cd /opt/smart-classroom
sudo -u smartclass venv/bin/python init_db.py
```

### 9. 防火墙配置

```bash
# 开放必要端口
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 1935/tcp
sudo ufw allow 8889/tcp
sudo ufw allow 8888/udp

# 启用防火墙
sudo ufw enable
```

## 验证部署

### 1. 检查服务状态

```bash
# 检查所有服务状态
sudo systemctl status smart-classroom-backend
sudo systemctl status mediamtx
sudo systemctl status nginx

# 检查端口监听
sudo netstat -tlnp | grep -E ':(80|5000|1935|8889)'
```

### 2. 测试Web访问

```bash
# 测试本地访问
curl -I http://localhost

# 测试API接口
curl http://localhost/api/health
```

### 3. 启动客户端

```bash
# 教师端
/opt/smart-classroom/venv/bin/python /opt/smart-classroom/teacher_client/main.py

# 小组端
/opt/smart-classroom/venv/bin/python /opt/smart-classroom/group_client/main.py
```

## 性能优化

### 1. 系统优化

```bash
# 调整系统参数
echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_rmem = 4096 87380 134217728' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_wmem = 4096 65536 134217728' >> /etc/sysctl.conf

# 应用配置
sudo sysctl -p
```

### 2. 数据库优化

```sql
-- SQLite优化设置
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = memory;
```

### 3. Nginx优化

```nginx
# 在http块中添加
worker_processes auto;
worker_connections 1024;

# 启用gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

# 缓存设置
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 查看详细日志
   sudo journalctl -u smart-classroom-backend -f
   sudo journalctl -u mediamtx -f
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   sudo lsof -i :5000
   sudo lsof -i :1935
   ```

3. **权限问题**
   ```bash
   # 修复权限
   sudo chown -R smartclass:smartclass /opt/smart-classroom
   sudo chown -R smartclass:smartclass /var/lib/smart-classroom
   ```

4. **数据库连接失败**
   ```bash
   # 检查数据库文件
   ls -la /var/lib/smart-classroom/
   sudo -u smartclass sqlite3 /var/lib/smart-classroom/smart_classroom.db ".tables"
   ```

### 日志位置

- 应用日志: `/var/log/smart-classroom/app.log`
- MediaMTX日志: `/var/log/smart-classroom/mediamtx.log`
- Nginx日志: `/var/log/nginx/access.log`, `/var/log/nginx/error.log`
- 系统日志: `journalctl -u service-name`

## 安全配置

### 1. SSL/TLS配置

```bash
# 安装Let's Encrypt证书
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 2. 防火墙规则

```bash
# 限制SSH访问
sudo ufw limit ssh

# 只允许特定IP访问管理接口
sudo ufw allow from ***********/24 to any port 9997
```

### 3. 用户权限

```bash
# 创建管理员用户
sudo adduser admin
sudo usermod -aG sudo admin

# 禁用root登录
sudo passwd -l root
```

## 备份和恢复

### 1. 数据备份

```bash
#!/bin/bash
# 备份脚本
BACKUP_DIR="/backup/smart-classroom"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份数据库
cp /var/lib/smart-classroom/smart_classroom.db $BACKUP_DIR/db_$DATE.db

# 备份配置文件
tar -czf $BACKUP_DIR/config_$DATE.tar.gz /opt/smart-classroom/config/

# 备份日志
tar -czf $BACKUP_DIR/logs_$DATE.tar.gz /var/log/smart-classroom/
```

### 2. 自动备份

```bash
# 添加到crontab
echo "0 2 * * * /opt/smart-classroom/scripts/backup.sh" | sudo crontab -
```

## 监控和维护

### 1. 系统监控

```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 监控脚本
#!/bin/bash
echo "=== 系统状态 ==="
uptime
free -h
df -h

echo "=== 服务状态 ==="
systemctl status smart-classroom-backend --no-pager
systemctl status mediamtx --no-pager
systemctl status nginx --no-pager
```

### 2. 日志轮转

```bash
# 配置logrotate
sudo tee /etc/logrotate.d/smart-classroom > /dev/null <<EOF
/var/log/smart-classroom/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 smartclass smartclass
    postrotate
        systemctl reload smart-classroom-backend
    endscript
}
EOF
```

## 升级和更新

### 1. 应用升级

```bash
#!/bin/bash
# 升级脚本
cd /opt/smart-classroom

# 停止服务
sudo systemctl stop smart-classroom-backend

# 备份当前版本
sudo cp -r /opt/smart-classroom /opt/smart-classroom.backup

# 更新代码
git pull origin main

# 更新依赖
sudo -u smartclass venv/bin/pip install -r requirements.txt

# 数据库迁移
sudo -u smartclass venv/bin/python migrate_db.py

# 重启服务
sudo systemctl start smart-classroom-backend
```

### 2. 系统更新

```bash
# 定期系统更新
sudo apt update && sudo apt upgrade -y
sudo apt autoremove -y
```

## 联系支持

如果遇到部署问题，请联系技术支持：

- 📧 邮箱: <EMAIL>
- 📞 电话: 400-123-4567
- 🌐 文档: https://docs.smartclassroom.com
- 💬 社区: https://community.smartclassroom.com