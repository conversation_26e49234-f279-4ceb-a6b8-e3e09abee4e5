# 智慧课堂系统 (Smart Classroom System)

基于统信UOS的多端协作教学平台，支持教师端、小组端和学生端的实时交互。

## 项目结构

```
smart_classroom/
├── backend/               # Flask后端服务
│   ├── api/              # API接口层
│   ├── models/           # 数据模型层
│   ├── services/         # 业务服务层
│   └── static/           # 静态文件
├── teacher_client/        # 教师端PyQt5应用
├── group_client/         # 小组端PyQt5应用  
├── web_client/           # 学生端Web界面
├── shared/               # 共享组件和工具
├── tests/                # 测试文件
│   ├── test_security_system.py           # 安全系统测试
│   ├── validate_security_implementation.py # 安全实现验证
│   └── README.md         # 测试说明文档
├── docs/                 # 项目文档
│   ├── SECURITY_ENHANCEMENT.md          # 安全增强文档
│   ├── SECURITY_IMPLEMENTATION_SUMMARY.md # 安全实施总结
│   └── ...               # 其他文档
├── scripts/              # 部署和工具脚本
├── config/               # 配置文件
├── requirements.txt      # Python依赖
└── setup.py             # 项目安装配置
```

## 技术栈

- **前端应用**: PyQt5 (教师端、小组端)
- **Web前端**: HTML5 + JavaScript + WebSocket (学生端)
- **后端服务**: Flask + SQLAlchemy + Flask-SocketIO
- **数据库**: SQLite
- **视频服务**: MediaMTX + FFmpeg
- **协同白板**: Excalidraw
- **安全加密**: AES-256-GCM + PBKDF2-HMAC-SHA256
- **身份认证**: JWT + 多因素认证

## 核心功能

### 🎯 教学功能
- 多屏研讨式教学
- 实时屏幕共享与广播
- 协同白板与批注
- 互动答题与反馈
- 小组管理与分组
- 文件分发与资源管理

### 🔒 安全功能
- **细粒度权限控制**: 4级用户角色，17种权限类型
- **多种认证方式**: 密码、二维码、课表关联登录
- **数据加密保护**: AES-256-GCM端到端加密
- **设备访问控制**: 时间、IP、操作限制
- **审计日志系统**: 完整的操作审计追踪
- **安全监控面板**: 实时安全状态监控

### 📊 管理功能
- 课堂报告与数据统计
- 学生考勤管理
- 设备连接与管理
- 系统监控与诊断

## 开发环境要求

- Python 3.8+
- 统信UOS操作系统
- PyQt5
- FFmpeg
- MediaMTX

## 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd smart_classroom

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量
```bash
# 设置安全密钥
export SMART_CLASSROOM_MASTER_KEY=<base64_encoded_key>
export ENCRYPTION_KEY=<encryption_key>
export SECRET_KEY=<jwt_secret_key>
```

### 3. 启动服务
```bash
# 启动后端服务
python backend/app.py

# 启动教师端
python teacher_client/main.py

# 启动小组端
python group_client/main.py
```

### 4. 访问系统
- **后端API**: http://localhost:5000
- **安全监控面板**: http://localhost:5000/security-dashboard
- **系统监控面板**: http://localhost:5000/system-dashboard

## 测试

### 运行安全测试
```bash
# 验证安全实现
python tests/validate_security_implementation.py

# 运行安全功能测试
python tests/test_security_system.py

# 使用pytest运行所有测试
pytest tests/
```

### 测试覆盖
- ✅ 用户认证与权限管理
- ✅ 数据加密与解密
- ✅ 设备访问控制
- ✅ 审计日志记录
- ✅ API安全防护
- ✅ 会话管理机制

## 安全特性

### 认证安全
- 多种登录方式支持
- 强密码策略enforcement
- 账户锁定机制
- 会话超时控制

### 数据安全
- AES-256-GCM加密算法
- 密钥管理与轮换
- 敏感数据自动加密
- 数据完整性验证

### 访问控制
- 基于角色的权限控制(RBAC)
- 细粒度权限管理
- 设备访问限制
- 时间和IP限制

### 审计监控
- 完整操作日志记录
- 实时安全监控
- 异常行为检测
- 安全扫描工具

## 文档

- [安全增强文档](docs/SECURITY_ENHANCEMENT.md)
- [安全实施总结](docs/SECURITY_IMPLEMENTATION_SUMMARY.md)
- [测试说明文档](tests/README.md)
- [API文档](backend/api/api_docs.md)

## 部署

### 生产环境部署
```bash
# 设置生产环境变量
export FLASK_ENV=production
export DATABASE_URL=<production_database_url>

# 启动服务
gunicorn -w 4 -b 0.0.0.0:5000 backend.app:app
```

### Docker部署
```bash
# 构建镜像
docker build -t smart-classroom .

# 运行容器
docker run -p 5000:5000 smart-classroom
```

## 贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目维护者: 智慧课堂系统开发团队
- 邮箱: <EMAIL>
- 项目地址: https://github.com/your-org/smart-classroom

---

**版本**: v1.0.0  
**最后更新**: 2025年7月21日