#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小组端主窗口
"""

import os
import sys
import json
import requests
import socket
import logging
from datetime import datetime

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTabWidget, QSplitter, QGridLayout, QFrame, QStatusBar, QToolBar, 
    QAction, QMenu, QMessageBox, QDialog, QFileDialog, QComboBox
)
from PyQt5.QtCore import Qt, QSize, QUrl, QTimer, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QIcon, QPixmap
from PyQt5.QtWebEngineWidgets import QWebEngineView

from shared.config import get_config
from shared.device_discovery_client import DeviceDiscoveryClient
from group_client.ui.video_panel import VideoPanel
from group_client.ui.whiteboard_panel import WhiteboardPanel
from group_client.ui.file_panel import FilePanel
from group_client.ui.recording_panel import RecordingPanel

# 获取配置
config = get_config()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('group_client')

class MainWindow(QMainWindow):
    """小组端主窗口类"""
    
    def __init__(self):
        super().__init__()
        
        # 初始化属性
        self.device_id = None
        self.group_id = None
        self.group_name = None
        self.api_base_url = f"http://{config.BACKEND_HOST}:{config.BACKEND_PORT}"
        self.device_discovery = None
        self.heartbeat_timer = QTimer(self)
        self.heartbeat_timer.timeout.connect(self.send_heartbeat)
        
        # 初始化UI
        self.init_ui()
        
        # 初始化设备发现
        self.init_device_discovery()
        
        # 启动心跳
        self.heartbeat_timer.start(30000)  # 30秒
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("智慧课堂系统 - 小组端")
        self.setGeometry(100, 100, 1280, 800)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建顶部信息栏
        info_frame = QFrame()
        info_frame.setFrameShape(QFrame.StyledPanel)
        info_frame.setMaximumHeight(60)
        info_layout = QHBoxLayout(info_frame)
        
        # 添加小组信息
        self.group_label = QLabel("小组: 未连接")
        self.group_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        info_layout.addWidget(self.group_label)
        
        # 添加状态信息
        self.status_label = QLabel("状态: 等待连接")
        info_layout.addWidget(self.status_label)
        
        # 添加模式选择
        mode_label = QLabel("模式:")
        info_layout.addWidget(mode_label)
        
        self.mode_combo = QComboBox()
        self.mode_combo.addItem("授课研讨", "teaching")
        self.mode_combo.addItem("自主研讨", "independent")
        self.mode_combo.currentIndexChanged.connect(self.change_mode)
        info_layout.addWidget(self.mode_combo)
        
        # 添加控制按钮
        self.connect_btn = QPushButton("连接服务器")
        self.connect_btn.clicked.connect(self.connect_to_server)
        info_layout.addWidget(self.connect_btn)
        
        main_layout.addWidget(info_frame)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 创建视频面板
        self.video_panel = VideoPanel(self)
        self.tab_widget.addTab(self.video_panel, "视频显示")
        
        # 创建白板面板
        self.whiteboard_panel = WhiteboardPanel(self)
        self.tab_widget.addTab(self.whiteboard_panel, "协同白板")
        
        # 创建文件面板
        self.file_panel = FilePanel(self)
        self.tab_widget.addTab(self.file_panel, "文件管理")
        
        # 创建录制面板
        self.recording_panel = RecordingPanel(self)
        self.tab_widget.addTab(self.recording_panel, "视频录制")
        
        main_layout.addWidget(self.tab_widget)
        
        # 创建状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
        
        # 创建工具栏
        self.create_toolbars()
    
    def create_toolbars(self):
        """创建工具栏"""
        # 主工具栏
        main_toolbar = QToolBar("主工具栏")
        main_toolbar.setIconSize(QSize(32, 32))
        self.addToolBar(Qt.TopToolBarArea, main_toolbar)
        
        # 刷新按钮
        refresh_action = QAction("刷新", self)
        refresh_action.triggered.connect(self.refresh_all)
        main_toolbar.addAction(refresh_action)
        
        main_toolbar.addSeparator()
        
        # 投屏按钮
        screen_share_action = QAction("投屏", self)
        screen_share_action.triggered.connect(self.start_screen_share)
        main_toolbar.addAction(screen_share_action)
        
        # 停止投屏按钮
        stop_share_action = QAction("停止投屏", self)
        stop_share_action.triggered.connect(self.stop_screen_share)
        main_toolbar.addAction(stop_share_action)
        
        main_toolbar.addSeparator()
        
        # 录制按钮
        record_action = QAction("开始录制", self)
        record_action.triggered.connect(self.start_recording)
        main_toolbar.addAction(record_action)
        
        # 停止录制按钮
        stop_record_action = QAction("停止录制", self)
        stop_record_action.triggered.connect(self.stop_recording)
        main_toolbar.addAction(stop_record_action)
    
    def init_device_discovery(self):
        """初始化设备发现"""
        try:
            # 生成设备ID和名称
            hostname = socket.gethostname()
            self.device_id = f"group_{hostname}_{os.getpid()}"
            
            # 创建设备发现客户端
            self.device_discovery = DeviceDiscoveryClient(
                device_id=self.device_id,
                device_type="group",
                device_name=f"小组端-{hostname}",
                port=8080,
                capabilities=["screen_share", "whiteboard", "file_sharing", "recording"]
            )
            
            # 启动设备发现
            success = self.device_discovery.start()
            if success:
                self.status_label.setText("状态: 设备发现已启动")
                logger.info("设备发现已启动")
            else:
                self.status_label.setText("状态: 设备发现启动失败")
                logger.error("设备发现启动失败")
        
        except Exception as e:
            self.status_label.setText("状态: 设备发现初始化错误")
            logger.error(f"初始化设备发现时出错: {str(e)}")
            self.show_error(f"初始化设备发现时出错: {str(e)}")
    
    def connect_to_server(self):
        """连接到服务器"""
        try:
            # 获取可用的小组
            response = requests.get(f"{self.api_base_url}/groups")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    groups = data.get('groups', [])
                    
                    if not groups:
                        self.show_error("没有可用的小组")
                        return
                    
                    # 显示小组选择对话框
                    self.show_group_selection_dialog(groups)
                else:
                    self.show_error(f"获取小组列表失败: {data.get('message')}")
            else:
                self.show_error(f"连接服务器失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.show_error(f"连接服务器时出错: {str(e)}")
    
    def show_group_selection_dialog(self, groups):
        """显示小组选择对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle("选择小组")
        dialog.setMinimumWidth(300)
        
        layout = QVBoxLayout(dialog)
        layout.addWidget(QLabel("请选择要加入的小组:"))
        
        combo = QComboBox()
        for group in groups:
            combo.addItem(group.get('name', '未命名小组'), group.get('id'))
        
        layout.addWidget(combo)
        
        buttons = QHBoxLayout()
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(dialog.accept)
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(dialog.reject)
        
        buttons.addWidget(ok_btn)
        buttons.addWidget(cancel_btn)
        layout.addLayout(buttons)
        
        if dialog.exec_() == QDialog.Accepted:
            group_id = combo.currentData()
            group_name = combo.currentText()
            self.join_group(group_id, group_name)
    
    def join_group(self, group_id, group_name):
        """加入小组"""
        try:
            # 发送加入小组请求
            response = requests.post(
                f"{self.api_base_url}/groups/{group_id}/join",
                json={"device_id": self.device_id}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.group_id = group_id
                    self.group_name = group_name
                    self.group_label.setText(f"小组: {group_name}")
                    self.status_label.setText("状态: 已连接")
                    self.connect_btn.setText("重新连接")
                    
                    # 通知各面板
                    self.video_panel.on_group_joined(group_id)
                    self.whiteboard_panel.on_group_joined(group_id)
                    self.file_panel.on_group_joined(group_id)
                    self.recording_panel.on_group_joined(group_id)
                    
                    self.status_bar.showMessage(f"已加入小组: {group_name}", 3000)
                    logger.info(f"已加入小组: {group_id} ({group_name})")
                else:
                    self.show_error(f"加入小组失败: {data.get('message')}")
            else:
                self.show_error(f"加入小组失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.show_error(f"加入小组时出错: {str(e)}")
    
    def change_mode(self, index):
        """切换模式"""
        mode = self.mode_combo.currentData()
        logger.info(f"切换到模式: {mode}")
        
        # 通知各面板
        self.video_panel.on_mode_changed(mode)
        self.whiteboard_panel.on_mode_changed(mode)
        self.file_panel.on_mode_changed(mode)
        self.recording_panel.on_mode_changed(mode)
        
        self.status_bar.showMessage(f"已切换到{self.mode_combo.currentText()}模式", 3000)
    
    def refresh_all(self):
        """刷新所有面板"""
        self.video_panel.refresh()
        self.whiteboard_panel.refresh()
        self.file_panel.refresh()
        self.recording_panel.refresh()
        
        self.status_bar.showMessage("已刷新所有面板", 3000)
    
    def start_screen_share(self):
        """开始屏幕共享"""
        if not self.group_id:
            self.show_error("请先加入小组")
            return
        
        try:
            # 发送开始屏幕捕获请求
            response = requests.post(
                f"{self.api_base_url}/video/screen/start",
                json={"device_id": self.device_id}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    stream_id = data.get('stream', {}).get('stream_id')
                    self.status_bar.showMessage(f"屏幕共享已启动: {stream_id}", 3000)
                    logger.info(f"屏幕共享已启动: {stream_id}")
                else:
                    self.show_error(f"开始屏幕共享失败: {data.get('message')}")
            else:
                self.show_error(f"开始屏幕共享失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.show_error(f"开始屏幕共享时出错: {str(e)}")
    
    def stop_screen_share(self):
        """停止屏幕共享"""
        if not self.group_id:
            self.show_error("请先加入小组")
            return
        
        try:
            # 获取当前设备的屏幕流
            response = requests.get(f"{self.api_base_url}/video/streams/source/{self.device_id}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    streams = data.get('streams', [])
                    screen_streams = [s for s in streams if s.get('stream_type') == 'screen']
                    
                    if not screen_streams:
                        self.status_bar.showMessage("没有正在进行的屏幕共享", 3000)
                        return
                    
                    # 停止所有屏幕流
                    for stream in screen_streams:
                        stream_id = stream.get('stream_id')
                        self.stop_stream(stream_id)
                else:
                    self.show_error(f"获取流列表失败: {data.get('message')}")
            else:
                self.show_error(f"获取流列表失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.show_error(f"停止屏幕共享时出错: {str(e)}")
    
    def stop_stream(self, stream_id):
        """停止流"""
        try:
            response = requests.post(f"{self.api_base_url}/video/streams/{stream_id}/stop")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.status_bar.showMessage(f"流已停止: {stream_id}", 3000)
                    logger.info(f"流已停止: {stream_id}")
                else:
                    self.show_error(f"停止流失败: {data.get('message')}")
            else:
                self.show_error(f"停止流失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.show_error(f"停止流时出错: {str(e)}")
    
    def start_recording(self):
        """开始录制"""
        self.recording_panel.start_recording()
    
    def stop_recording(self):
        """停止录制"""
        self.recording_panel.stop_recording()
    
    def send_heartbeat(self):
        """发送心跳"""
        if self.group_id:
            try:
                response = requests.post(
                    f"{self.api_base_url}/devices/heartbeat",
                    json={
                        "device_id": self.device_id,
                        "group_id": self.group_id,
                        "timestamp": datetime.now().isoformat()
                    }
                )
                
                if response.status_code != 200:
                    logger.warning(f"心跳发送失败: HTTP {response.status_code}")
            
            except Exception as e:
                logger.error(f"发送心跳时出错: {str(e)}")
    
    def show_error(self, message):
        """显示错误消息"""
        QMessageBox.critical(self, "错误", message)
        logger.error(message)
    
    def closeEvent(self, event):
        """关闭事件处理"""
        # 停止设备发现
        if self.device_discovery:
            self.device_discovery.stop()
        
        # 停止心跳
        self.heartbeat_timer.stop()
        
        # 停止所有流
        if self.group_id:
            try:
                requests.post(
                    f"{self.api_base_url}/groups/{self.group_id}/leave",
                    json={"device_id": self.device_id}
                )
            except:
                pass
        
        event.accept()