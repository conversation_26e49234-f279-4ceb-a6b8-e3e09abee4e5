#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小组端视频面板
"""

import os
import sys
import json
import requests
import logging
import threading
import time
from typing import Dict, List

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QGridLayout,
    QScrollArea, QFrame, QSplitter, QComboBox, QMessageBox, QMenu, QAction,
    QDialog, QDialogButtonBox, QListWidget, QListWidgetItem, QCheckBox
)
from PyQt5.QtCore import Qt, QSize, QUrl, QTimer, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QIcon, QPixmap
from PyQt5.QtMultimedia import QMediaContent, QMediaPlayer
from PyQt5.QtMultimediaWidgets import QVideoWidget
from PyQt5.QtWebSockets import QWebSocket

from shared.config import get_config

# 获取配置
config = get_config()

# 配置日志
logger = logging.getLogger('video_panel')

class VideoStream(QWidget):
    """视频流显示组件"""
    
    def __init__(self, stream_id, source_id, stream_type, url, parent=None):
        super().__init__(parent)
        self.stream_id = stream_id
        self.source_id = source_id
        self.stream_type = stream_type
        self.url = url
        self.parent = parent
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建视频播放器
        self.video_widget = QVideoWidget()
        self.video_widget.setMinimumSize(320, 240)
        
        self.media_player = QMediaPlayer(self)
        self.media_player.setVideoOutput(self.video_widget)
        
        # 设置视频源
        self.media_player.setMedia(QMediaContent(QUrl(self.url)))
        
        # 添加视频控件
        layout.addWidget(self.video_widget)
        
        # 创建控制栏
        control_layout = QHBoxLayout()
        
        # 添加标题标签
        title = f"{self.source_id} ({self.stream_type})"
        title_label = QLabel(title)
        control_layout.addWidget(title_label)
        
        # 添加控制按钮
        control_layout.addStretch()
        
        # 停止按钮
        stop_btn = QPushButton("停止")
        stop_btn.clicked.connect(self.stop_stream)
        control_layout.addWidget(stop_btn)
        
        # 全屏按钮
        fullscreen_btn = QPushButton("全屏")
        fullscreen_btn.clicked.connect(self.toggle_fullscreen)
        control_layout.addWidget(fullscreen_btn)
        
        # 添加控制栏到布局
        layout.addLayout(control_layout)
        
        # 开始播放
        self.media_player.play()
    
    def stop_stream(self):
        """停止流"""
        if self.parent:
            self.parent.stop_stream(self.stream_id)
    
    def toggle_fullscreen(self):
        """切换全屏显示"""
        if self.video_widget.isFullScreen():
            self.video_widget.setFullScreen(False)
        else:
            self.video_widget.setFullScreen(True)
    
    def closeEvent(self, event):
        """关闭事件处理"""
        # 停止播放器
        self.media_player.stop()
        event.accept()

class VideoPanel(QWidget):
    """视频面板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.streams = {}  # 存储流信息
        self.stream_widgets = {}  # 存储流组件
        self.current_layout = "grid"  # 当前布局模式：grid或single
        self.current_stream = None  # 当前选中的流
        self.group_id = None  # 当前小组ID
        self.mode = "teaching"  # 当前模式：teaching或independent
        self.websocket = None  # WebSocket连接
        
        # 初始化UI
        self.init_ui()
        
        # 初始化WebSocket
        self.init_websocket()
    
    def init_ui(self):
        """初始化UI"""
        # 创建主布局
        layout = QVBoxLayout(self)
        
        # 创建控制栏
        control_layout = QHBoxLayout()
        
        # 添加布局选择下拉框
        layout_label = QLabel("布局:")
        control_layout.addWidget(layout_label)
        
        self.layout_combo = QComboBox()
        self.layout_combo.addItem("网格布局", "grid")
        self.layout_combo.addItem("单视图", "single")
        self.layout_combo.currentIndexChanged.connect(self.change_layout)
        control_layout.addWidget(self.layout_combo)
        
        # 添加刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh)
        control_layout.addWidget(refresh_btn)
        
        # 添加控制栏到主布局
        layout.addLayout(control_layout)
        
        # 创建滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setFrameShape(QFrame.NoFrame)
        
        # 创建内容区域
        self.content_widget = QWidget()
        self.grid_layout = QGridLayout(self.content_widget)
        self.grid_layout.setContentsMargins(0, 0, 0, 0)
        self.grid_layout.setSpacing(10)
        
        self.scroll_area.setWidget(self.content_widget)
        layout.addWidget(self.scroll_area)
        
        # 创建状态标签
        self.status_label = QLabel("视频流: 0")
        layout.addWidget(self.status_label)
    
    def init_websocket(self):
        """初始化WebSocket连接"""
        self.websocket = QWebSocket()
        self.websocket.connected.connect(self.on_websocket_connected)
        self.websocket.disconnected.connect(self.on_websocket_disconnected)
        self.websocket.textMessageReceived.connect(self.on_websocket_message)
        
        # 连接到WebSocket服务器
        ws_url = f"ws://{config.BACKEND_HOST}:{config.BACKEND_PORT}/ws"
        self.websocket.open(QUrl(ws_url))
    
    def on_websocket_connected(self):
        """WebSocket连接成功"""
        logger.info("WebSocket已连接")
        
        # 如果已加入小组，发送加入小组房间消息
        if self.group_id:
            self.join_group_room()
    
    def on_websocket_disconnected(self):
        """WebSocket断开连接"""
        logger.info("WebSocket已断开")
        
        # 尝试重新连接
        QTimer.singleShot(5000, self.init_websocket)
    
    def on_websocket_message(self, message):
        """处理WebSocket消息"""
        try:
            data = json.loads(message)
            event_type = data.get('type')
            
            if event_type == 'stream_started':
                # 新流可用
                self.handle_stream_started(data)
            
            elif event_type == 'stream_stopped':
                # 流已停止
                self.handle_stream_stopped(data)
            
            elif event_type == 'stream_broadcast':
                # 广播流
                self.handle_stream_broadcast(data)
        
        except json.JSONDecodeError:
            logger.warning(f"收到无效的JSON数据: {message}")
        except Exception as e:
            logger.error(f"处理WebSocket消息时出错: {str(e)}")
    
    def handle_stream_started(self, data):
        """处理新流可用事件"""
        stream_id = data.get('stream_id')
        source_id = data.get('source_id')
        stream_type = data.get('stream_type')
        http_url = data.get('http_url')
        
        # 添加流
        self.add_stream({
            'stream_id': stream_id,
            'source_id': source_id,
            'stream_type': stream_type,
            'http_url': http_url
        })
    
    def handle_stream_stopped(self, data):
        """处理流停止事件"""
        stream_id = data.get('stream_id')
        
        # 移除流
        self.remove_stream(stream_id)
    
    def handle_stream_broadcast(self, data):
        """处理广播流事件"""
        stream_id = data.get('stream_id')
        source_id = data.get('source_id')
        stream_type = data.get('stream_type')
        http_url = data.get('http_url')
        target_group = data.get('target_group')
        
        # 检查是否是发给当前小组的广播
        if target_group == self.group_id:
            # 添加流
            self.add_stream({
                'stream_id': stream_id,
                'source_id': source_id,
                'stream_type': stream_type,
                'http_url': http_url
            })
    
    def join_group_room(self):
        """加入小组WebSocket房间"""
        if self.websocket and self.websocket.state() == QWebSocket.ConnectedState and self.group_id:
            # 发送加入小组房间消息
            message = json.dumps({
                'type': 'join_group',
                'group_id': self.group_id
            })
            self.websocket.sendTextMessage(message)
            logger.info(f"已加入小组房间: {self.group_id}")
    
    def on_group_joined(self, group_id):
        """处理加入小组事件"""
        self.group_id = group_id
        
        # 加入WebSocket小组房间
        self.join_group_room()
        
        # 刷新流列表
        self.refresh()
    
    def on_mode_changed(self, mode):
        """处理模式切换事件"""
        self.mode = mode
        logger.info(f"视频面板切换到模式: {mode}")
    
    def refresh(self):
        """刷新视频流列表"""
        if not self.group_id:
            return
        
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 发送请求获取流列表
            response = requests.get(f"{api_base_url}/video/streams")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.update_streams(data.get('streams', []))
                else:
                    self.parent.show_error(f"获取视频流列表失败: {data.get('message')}")
            else:
                self.parent.show_error(f"获取视频流列表失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.parent.show_error(f"刷新视频流列表时出错: {str(e)}")
    
    def update_streams(self, streams):
        """更新视频流列表"""
        # 记录当前流ID
        current_stream_ids = set(self.streams.keys())
        new_stream_ids = set()
        
        # 添加或更新流
        for stream in streams:
            stream_id = stream.get('stream_id')
            new_stream_ids.add(stream_id)
            
            if stream_id not in self.streams:
                # 新流
                self.add_stream(stream)
            else:
                # 更新流
                self.streams[stream_id] = stream
        
        # 移除不存在的流
        for stream_id in current_stream_ids - new_stream_ids:
            self.remove_stream(stream_id)
        
        # 更新状态标签
        self.status_label.setText(f"视频流: {len(self.streams)}")
    
    def add_stream(self, stream):
        """添加视频流"""
        stream_id = stream.get('stream_id')
        source_id = stream.get('source_id')
        stream_type = stream.get('stream_type')
        http_url = stream.get('http_url')
        
        # 存储流信息
        self.streams[stream_id] = stream
        
        # 创建视频流组件
        stream_widget = VideoStream(stream_id, source_id, stream_type, http_url, self)
        self.stream_widgets[stream_id] = stream_widget
        
        # 添加到布局
        self.update_layout()
        
        # 更新状态标签
        self.status_label.setText(f"视频流: {len(self.streams)}")
        
        # 通知服务器开始观看
        self.start_viewing(stream_id)
    
    def remove_stream(self, stream_id):
        """移除视频流"""
        if stream_id in self.streams:
            # 移除流信息
            del self.streams[stream_id]
            
            # 移除视频流组件
            if stream_id in self.stream_widgets:
                widget = self.stream_widgets[stream_id]
                widget.close()
                widget.deleteLater()
                del self.stream_widgets[stream_id]
            
            # 更新布局
            self.update_layout()
            
            # 更新状态标签
            self.status_label.setText(f"视频流: {len(self.streams)}")
            
            # 通知服务器停止观看
            self.stop_viewing(stream_id)
    
    def change_layout(self, index):
        """切换布局模式"""
        self.current_layout = self.layout_combo.currentData()
        self.update_layout()
    
    def update_layout(self):
        """更新布局"""
        # 清空布局
        while self.grid_layout.count():
            item = self.grid_layout.takeAt(0)
            if item.widget():
                item.widget().setParent(None)
        
        if self.current_layout == "grid":
            # 网格布局
            col_count = 2  # 每行2个视频
            row = 0
            col = 0
            
            for stream_id, widget in self.stream_widgets.items():
                self.grid_layout.addWidget(widget, row, col)
                
                col += 1
                if col >= col_count:
                    col = 0
                    row += 1
        
        elif self.current_layout == "single" and self.stream_widgets:
            # 单视图布局
            if self.current_stream and self.current_stream in self.stream_widgets:
                # 显示当前选中的流
                self.grid_layout.addWidget(self.stream_widgets[self.current_stream], 0, 0)
            else:
                # 显示第一个流
                stream_id = next(iter(self.stream_widgets))
                self.current_stream = stream_id
                self.grid_layout.addWidget(self.stream_widgets[stream_id], 0, 0)
    
    def stop_stream(self, stream_id):
        """停止流"""
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 发送请求停止流
            response = requests.post(f"{api_base_url}/video/streams/{stream_id}/stop")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.parent.status_bar.showMessage(f"流已停止: {stream_id}", 3000)
                else:
                    self.parent.show_error(f"停止流失败: {data.get('message')}")
            else:
                self.parent.show_error(f"停止流失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.parent.show_error(f"停止流时出错: {str(e)}")
    
    def start_viewing(self, stream_id):
        """通知服务器开始观看流"""
        if self.websocket and self.websocket.state() == QWebSocket.ConnectedState:
            message = json.dumps({
                'type': 'start_viewing',
                'stream_id': stream_id,
                'viewer_id': self.parent.device_id
            })
            self.websocket.sendTextMessage(message)
    
    def stop_viewing(self, stream_id):
        """通知服务器停止观看流"""
        if self.websocket and self.websocket.state() == QWebSocket.ConnectedState:
            message = json.dumps({
                'type': 'stop_viewing',
                'stream_id': stream_id,
                'viewer_id': self.parent.device_id
            })
            self.websocket.sendTextMessage(message)