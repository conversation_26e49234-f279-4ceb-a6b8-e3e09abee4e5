#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 小组端多屏显示组件
"""

import sys
import json
import time
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QSplitter,
    QStackedWidget, QProgressBar
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot, QRect
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor, QPen, QBrush

class TimerDisplayWidget(QWidget):
    """计时器显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.timer_data = None
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # 计时器标题
        self.title_label = QLabel("计时器")
        self.title_label.setFont(QFont("Arial", 16, QFont.Bold))
        self.title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.title_label)
        
        # 时间显示
        self.time_label = QLabel("00:00")
        self.time_label.setFont(QFont("Arial", 48, QFont.Bold))
        self.time_label.setAlignment(Qt.AlignCenter)
        self.time_label.setStyleSheet("""
            QLabel {
                background-color: black;
                color: #00FF00;
                border: 3px solid #333333;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        layout.addWidget(self.time_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid grey;
                border-radius: 5px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #05B8CC;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("等待开始")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFont(QFont("Arial", 12))
        layout.addWidget(self.status_label)
        
        # 设置定时器更新显示
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(1000)  # 每秒更新
    
    def set_timer_data(self, timer_data):
        """设置计时器数据"""
        self.timer_data = timer_data
        if timer_data:
            self.title_label.setText(timer_data.get('title', '计时器'))
            self.progress_bar.setMaximum(timer_data.get('duration', 0))
            self.update_display()
    
    def update_display(self):
        """更新显示"""
        if not self.timer_data:
            return
        
        remaining_time = self.timer_data.get('remaining_time', 0)
        duration = self.timer_data.get('duration', 0)
        is_active = self.timer_data.get('is_active', False)
        is_paused = self.timer_data.get('is_paused', False)
        
        # 更新时间显示
        minutes = remaining_time // 60
        seconds = remaining_time % 60
        self.time_label.setText(f"{minutes:02d}:{seconds:02d}")
        
        # 更新进度条
        elapsed_time = duration - remaining_time
        self.progress_bar.setValue(elapsed_time)
        
        # 更新状态和颜色
        if not is_active:
            self.status_label.setText("已停止")
            self.time_label.setStyleSheet("""
                QLabel {
                    background-color: black;
                    color: #666666;
                    border: 3px solid #333333;
                    border-radius: 10px;
                    padding: 20px;
                }
            """)
        elif is_paused:
            self.status_label.setText("已暂停")
            self.time_label.setStyleSheet("""
                QLabel {
                    background-color: black;
                    color: #FFFF00;
                    border: 3px solid #333333;
                    border-radius: 10px;
                    padding: 20px;
                }
            """)
        elif remaining_time <= 60:  # 最后一分钟显示红色
            self.status_label.setText("即将结束")
            self.time_label.setStyleSheet("""
                QLabel {
                    background-color: black;
                    color: #FF0000;
                    border: 3px solid #333333;
                    border-radius: 10px;
                    padding: 20px;
                }
            """)
        else:
            self.status_label.setText("进行中")
            self.time_label.setStyleSheet("""
                QLabel {
                    background-color: black;
                    color: #00FF00;
                    border: 3px solid #333333;
                    border-radius: 10px;
                    padding: 20px;
                }
            """)

class DiscussionTopicWidget(QWidget):
    """讨论主题显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.topic_data = None
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # 主题标题
        self.title_label = QLabel("讨论主题")
        self.title_label.setFont(QFont("Arial", 18, QFont.Bold))
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet("""
            QLabel {
                background-color: #2E86AB;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(self.title_label)
        
        # 主题内容
        self.content_label = QLabel("等待主题分发...")
        self.content_label.setFont(QFont("Arial", 14))
        self.content_label.setWordWrap(True)
        self.content_label.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        self.content_label.setStyleSheet("""
            QLabel {
                background-color: white;
                border: 2px solid #E0E0E0;
                border-radius: 8px;
                padding: 20px;
                margin: 10px;
            }
        """)
        layout.addWidget(self.content_label)
        
        # 图片显示区域
        self.image_label = QLabel()
        self.image_label.setMinimumHeight(200)
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #CCCCCC;
                border-radius: 8px;
                margin: 10px;
            }
        """)
        self.image_label.hide()  # 默认隐藏
        layout.addWidget(self.image_label)
        
        # 时间戳
        self.timestamp_label = QLabel("")
        self.timestamp_label.setFont(QFont("Arial", 10))
        self.timestamp_label.setAlignment(Qt.AlignRight)
        self.timestamp_label.setStyleSheet("color: #666666; margin: 5px;")
        layout.addWidget(self.timestamp_label)
    
    def set_topic_data(self, topic_data):
        """设置讨论主题数据"""
        self.topic_data = topic_data
        if topic_data:
            self.title_label.setText(topic_data.get('title', '讨论主题'))
            self.content_label.setText(topic_data.get('content', ''))
            
            # 显示图片（如果有）
            if topic_data.get('image_data'):
                # 这里应该处理Base64图片数据的显示
                self.image_label.setText("图片内容")
                self.image_label.show()
            else:
                self.image_label.hide()
            
            # 显示时间戳
            created_time = topic_data.get('created_time')
            if created_time:
                self.timestamp_label.setText(f"发布时间: {created_time}")

class MultiScreenDisplayWidget(QWidget):
    """多屏显示主组件"""
    
    # 信号定义
    display_mode_changed = pyqtSignal(str)
    layout_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.group_id = None
        self.classroom_id = None
        self.current_mode = "independent"
        self.current_layout = "single"
        self.content_sources = []
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("多屏显示系统")
        self.setMinimumSize(1024, 768)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)
        
        # 状态栏
        self.status_bar = self.create_status_bar()
        main_layout.addWidget(self.status_bar)
        
        # 主显示区域
        self.display_area = QStackedWidget()
        main_layout.addWidget(self.display_area)
        
        # 创建不同的显示模式界面
        self.single_display = self.create_single_display()
        self.dual_display = self.create_dual_display()
        self.quad_display = self.create_quad_display()
        self.grid_display = self.create_grid_display()
        
        self.display_area.addWidget(self.single_display)
        self.display_area.addWidget(self.dual_display)
        self.display_area.addWidget(self.quad_display)
        self.display_area.addWidget(self.grid_display)
        
        # 计时器和主题显示
        self.timer_widget = TimerDisplayWidget()
        self.topic_widget = DiscussionTopicWidget()
        
        # 底部工具栏
        self.toolbar = self.create_toolbar()
        main_layout.addWidget(self.toolbar)
    
    def create_status_bar(self):
        """创建状态栏"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_frame.setMaximumHeight(40)
        
        layout = QHBoxLayout()
        status_frame.setLayout(layout)
        
        # 显示模式标签
        self.mode_label = QLabel("模式: 独立显示")
        layout.addWidget(self.mode_label)
        
        # 布局标签
        self.layout_label = QLabel("布局: 单屏")
        layout.addWidget(self.layout_label)
        
        # 连接状态
        self.connection_label = QLabel("连接: 正常")
        self.connection_label.setStyleSheet("color: green")
        layout.addWidget(self.connection_label)
        
        # 时间显示
        self.time_label = QLabel()
        self.update_time()
        layout.addWidget(self.time_label)
        
        # 设置定时器更新时间
        time_timer = QTimer()
        time_timer.timeout.connect(self.update_time)
        time_timer.start(1000)
        
        layout.addStretch()
        
        return status_frame
    
    def create_single_display(self):
        """创建单屏显示"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # 主内容区域
        self.single_content = QLabel("等待内容...")
        self.single_content.setAlignment(Qt.AlignCenter)
        self.single_content.setStyleSheet("""
            QLabel {
                background-color: #F5F5F5;
                border: 2px solid #DDDDDD;
                border-radius: 10px;
                font-size: 18px;
                color: #666666;
            }
        """)
        layout.addWidget(self.single_content)
        
        return widget
    
    def create_dual_display(self):
        """创建双屏显示"""
        widget = QWidget()
        layout = QHBoxLayout()
        widget.setLayout(layout)
        
        # 左侧内容
        self.dual_left_content = QLabel("左侧内容")
        self.dual_left_content.setAlignment(Qt.AlignCenter)
        self.dual_left_content.setStyleSheet("""
            QLabel {
                background-color: #E3F2FD;
                border: 2px solid #2196F3;
                border-radius: 10px;
                font-size: 16px;
                margin: 5px;
            }
        """)
        layout.addWidget(self.dual_left_content)
        
        # 右侧内容
        self.dual_right_content = QLabel("右侧内容")
        self.dual_right_content.setAlignment(Qt.AlignCenter)
        self.dual_right_content.setStyleSheet("""
            QLabel {
                background-color: #E8F5E8;
                border: 2px solid #4CAF50;
                border-radius: 10px;
                font-size: 16px;
                margin: 5px;
            }
        """)
        layout.addWidget(self.dual_right_content)
        
        return widget
    
    def create_quad_display(self):
        """创建四分屏显示"""
        widget = QWidget()
        layout = QGridLayout()
        widget.setLayout(layout)
        
        # 四个区域
        self.quad_contents = []
        colors = ["#FFEBEE", "#E3F2FD", "#E8F5E8", "#FFF3E0"]
        borders = ["#F44336", "#2196F3", "#4CAF50", "#FF9800"]
        
        for i in range(4):
            content = QLabel(f"区域 {i+1}")
            content.setAlignment(Qt.AlignCenter)
            content.setStyleSheet(f"""
                QLabel {{
                    background-color: {colors[i]};
                    border: 2px solid {borders[i]};
                    border-radius: 10px;
                    font-size: 14px;
                    margin: 2px;
                }}
            """)
            self.quad_contents.append(content)
            layout.addWidget(content, i // 2, i % 2)
        
        return widget
    
    def create_grid_display(self):
        """创建网格显示"""
        widget = QWidget()
        layout = QGridLayout()
        widget.setLayout(layout)
        
        # 3x3网格
        self.grid_contents = []
        for i in range(9):
            content = QLabel(f"网格 {i+1}")
            content.setAlignment(Qt.AlignCenter)
            content.setStyleSheet("""
                QLabel {
                    background-color: #FAFAFA;
                    border: 1px solid #CCCCCC;
                    border-radius: 5px;
                    font-size: 12px;
                    margin: 1px;
                }
            """)
            self.grid_contents.append(content)
            layout.addWidget(content, i // 3, i % 3)
        
        return widget
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QFrame()
        toolbar.setFrameStyle(QFrame.StyledPanel)
        toolbar.setMaximumHeight(60)
        
        layout = QHBoxLayout()
        toolbar.setLayout(layout)
        
        # 显示计时器按钮
        self.show_timer_btn = QPushButton("显示计时器")
        self.show_timer_btn.clicked.connect(self.toggle_timer_display)
        layout.addWidget(self.show_timer_btn)
        
        # 显示主题按钮
        self.show_topic_btn = QPushButton("显示主题")
        self.show_topic_btn.clicked.connect(self.toggle_topic_display)
        layout.addWidget(self.show_topic_btn)
        
        # 全屏按钮
        self.fullscreen_btn = QPushButton("全屏")
        self.fullscreen_btn.clicked.connect(self.toggle_fullscreen)
        layout.addWidget(self.fullscreen_btn)
        
        layout.addStretch()
        
        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_display)
        layout.addWidget(self.refresh_btn)
        
        return toolbar
    
    def set_group_info(self, group_id, classroom_id):
        """设置小组信息"""
        self.group_id = group_id
        self.classroom_id = classroom_id
    
    def apply_display_config(self, config_data):
        """应用显示配置"""
        mode = config_data.get('mode', 'independent')
        layout = config_data.get('layout', 'single')
        content_sources = config_data.get('content_sources', [])
        sync_enabled = config_data.get('sync_enabled', True)
        
        self.current_mode = mode
        self.current_layout = layout
        self.content_sources = content_sources
        
        # 更新状态栏
        self.mode_label.setText(f"模式: {self.get_mode_display_name(mode)}")
        self.layout_label.setText(f"布局: {self.get_layout_display_name(layout)}")
        
        # 切换显示布局
        self.switch_layout(layout)
        
        # 更新内容
        self.update_content_display(content_sources)
        
        # 发送信号
        self.display_mode_changed.emit(mode)
        self.layout_changed.emit(layout)
    
    def switch_layout(self, layout_type):
        """切换显示布局"""
        layout_map = {
            'single': 0,
            'dual': 1,
            'quad': 2,
            'grid_2x2': 2,
            'grid_3x3': 3
        }
        
        index = layout_map.get(layout_type, 0)
        self.display_area.setCurrentIndex(index)
    
    def update_content_display(self, content_sources):
        """更新内容显示"""
        if self.current_layout == 'single':
            content = content_sources[0] if content_sources else "无内容"
            self.single_content.setText(f"显示内容: {content}")
        
        elif self.current_layout == 'dual':
            left_content = content_sources[0] if len(content_sources) > 0 else "无内容"
            right_content = content_sources[1] if len(content_sources) > 1 else "无内容"
            self.dual_left_content.setText(f"左侧: {left_content}")
            self.dual_right_content.setText(f"右侧: {right_content}")
        
        elif self.current_layout in ['quad', 'grid_2x2']:
            for i, content_widget in enumerate(self.quad_contents):
                content = content_sources[i] if i < len(content_sources) else "无内容"
                content_widget.setText(f"区域 {i+1}: {content}")
        
        elif self.current_layout == 'grid_3x3':
            for i, content_widget in enumerate(self.grid_contents):
                content = content_sources[i] if i < len(content_sources) else "无内容"
                content_widget.setText(f"网格 {i+1}: {content}")
    
    def show_timer(self, timer_data):
        """显示计时器"""
        self.timer_widget.set_timer_data(timer_data)
        self.timer_widget.show()
        self.timer_widget.raise_()
    
    def hide_timer(self):
        """隐藏计时器"""
        self.timer_widget.hide()
    
    def show_discussion_topic(self, topic_data):
        """显示讨论主题"""
        self.topic_widget.set_topic_data(topic_data)
        self.topic_widget.show()
        self.topic_widget.raise_()
    
    def hide_discussion_topic(self):
        """隐藏讨论主题"""
        self.topic_widget.hide()
    
    def toggle_timer_display(self):
        """切换计时器显示"""
        if self.timer_widget.isVisible():
            self.hide_timer()
            self.show_timer_btn.setText("显示计时器")
        else:
            self.timer_widget.show()
            self.show_timer_btn.setText("隐藏计时器")
    
    def toggle_topic_display(self):
        """切换主题显示"""
        if self.topic_widget.isVisible():
            self.hide_discussion_topic()
            self.show_topic_btn.setText("显示主题")
        else:
            self.topic_widget.show()
            self.show_topic_btn.setText("隐藏主题")
    
    def toggle_fullscreen(self):
        """切换全屏"""
        if self.isFullScreen():
            self.showNormal()
            self.fullscreen_btn.setText("全屏")
        else:
            self.showFullScreen()
            self.fullscreen_btn.setText("退出全屏")
    
    def refresh_display(self):
        """刷新显示"""
        # 重新应用当前配置
        if hasattr(self, 'current_config'):
            self.apply_display_config(self.current_config)
    
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(f"时间: {current_time}")
    
    def get_mode_display_name(self, mode):
        """获取模式显示名称"""
        mode_names = {
            'broadcast': '广播模式',
            'independent': '独立显示',
            'group_broadcast': '小组广播',
            'comparison': '对比显示',
            'dual_screen': '双屏联动'
        }
        return mode_names.get(mode, mode)
    
    def get_layout_display_name(self, layout):
        """获取布局显示名称"""
        layout_names = {
            'single': '单屏',
            'dual': '双屏',
            'quad': '四分屏',
            'grid_2x2': '2x2网格',
            'grid_3x3': '3x3网格',
            'pip': '画中画'
        }
        return layout_names.get(layout, layout)
    
    def update_connection_status(self, is_connected):
        """更新连接状态"""
        if is_connected:
            self.connection_label.setText("连接: 正常")
            self.connection_label.setStyleSheet("color: green")
        else:
            self.connection_label.setText("连接: 断开")
            self.connection_label.setStyleSheet("color: red")