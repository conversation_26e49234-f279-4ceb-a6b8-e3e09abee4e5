#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小组端文件面板
"""

import os
import sys
import json
import requests
import logging
import time
from datetime import datetime

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QGridLayout,
    QScrollArea, QFrame, QSplitter, QComboBox, QMessageBox, QMenu, QAction,
    QToolBar, QFileDialog, QListWidget, QListWidgetItem, QProgressBar,
    QTabWidget, QTreeWidget, QTreeWidgetItem, QHeaderView
)
from PyQt5.QtCore import Qt, QSize, QUrl, QTimer, pyqtSignal, pyqtSlot, QThread, QMimeData
from PyQt5.QtGui import QIcon, QPixmap, QDrag
from PyQt5.QtWebEngineWidgets import QWebEngineView

from shared.config import get_config

# 获取配置
config = get_config()

# 配置日志
logger = logging.getLogger('file_panel')

class FileDownloadThread(QThread):
    """文件下载线程"""
    
    progress_signal = pyqtSignal(int)
    finished_signal = pyqtSignal(bool, str)
    
    def __init__(self, url, save_path):
        super().__init__()
        self.url = url
        self.save_path = save_path
        self.is_running = True
    
    def run(self):
        """运行线程"""
        try:
            # 发送请求
            response = requests.get(self.url, stream=True)
            
            if response.status_code != 200:
                self.finished_signal.emit(False, f"下载失败: HTTP {response.status_code}")
                return
            
            # 获取文件大小
            total_size = int(response.headers.get('content-length', 0))
            
            # 创建目录
            os.makedirs(os.path.dirname(self.save_path), exist_ok=True)
            
            # 下载文件
            downloaded_size = 0
            with open(self.save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if not self.is_running:
                        break
                    
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 更新进度
                        if total_size > 0:
                            progress = int(downloaded_size * 100 / total_size)
                            self.progress_signal.emit(progress)
            
            if not self.is_running:
                # 取消下载
                os.remove(self.save_path)
                self.finished_signal.emit(False, "下载已取消")
            else:
                self.finished_signal.emit(True, self.save_path)
        
        except Exception as e:
            self.finished_signal.emit(False, str(e))
    
    def stop(self):
        """停止线程"""
        self.is_running = False

class FileUploadThread(QThread):
    """文件上传线程"""
    
    progress_signal = pyqtSignal(int)
    finished_signal = pyqtSignal(bool, str, dict)
    
    def __init__(self, url, file_path, metadata):
        super().__init__()
        self.url = url
        self.file_path = file_path
        self.metadata = metadata
        self.is_running = True
    
    def run(self):
        """运行线程"""
        try:
            # 准备文件
            file_name = os.path.basename(self.file_path)
            file_size = os.path.getsize(self.file_path)
            
            # 准备表单数据
            files = {'file': (file_name, open(self.file_path, 'rb'))}
            data = {'metadata': json.dumps(self.metadata)}
            
            # 发送请求
            response = requests.post(
                self.url,
                files=files,
                data=data
            )
            
            if response.status_code != 200:
                self.finished_signal.emit(False, f"上传失败: HTTP {response.status_code}", {})
                return
            
            # 解析响应
            result = response.json()
            
            if result.get('success'):
                self.finished_signal.emit(True, "上传成功", result.get('file', {}))
            else:
                self.finished_signal.emit(False, result.get('message', "上传失败"), {})
        
        except Exception as e:
            self.finished_signal.emit(False, str(e), {})
    
    def stop(self):
        """停止线程"""
        self.is_running = False

class FileListItem(QListWidgetItem):
    """文件列表项"""
    
    def __init__(self, file_info):
        super().__init__()
        self.file_info = file_info
        self.setText(file_info.get('name', '未命名文件'))
        self.setToolTip(f"类型: {file_info.get('type', '未知')}\n"
                        f"大小: {self.format_size(file_info.get('size', 0))}\n"
                        f"上传时间: {file_info.get('upload_time', '未知')}")
    
    def format_size(self, size):
        """格式化文件大小"""
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.2f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.2f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.2f} GB"

class FilePanel(QWidget):
    """文件面板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.group_id = None
        self.mode = "teaching"
        self.download_threads = {}
        self.upload_threads = {}
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 创建主布局
        layout = QVBoxLayout(self)
        
        # 创建工具栏
        toolbar = QToolBar()
        toolbar.setIconSize(QSize(24, 24))
        
        # 添加上传按钮
        self.upload_btn = QPushButton("上传文件")
        self.upload_btn.clicked.connect(self.upload_file)
        toolbar.addWidget(self.upload_btn)
        
        # 添加下载按钮
        self.download_btn = QPushButton("下载选中")
        self.download_btn.clicked.connect(self.download_selected)
        toolbar.addWidget(self.download_btn)
        
        # 添加刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh)
        toolbar.addWidget(self.refresh_btn)
        
        # 添加工具栏到布局
        layout.addWidget(toolbar)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 创建文件列表
        self.file_list = QListWidget()
        self.file_list.setSelectionMode(QListWidget.ExtendedSelection)
        self.file_list.itemDoubleClicked.connect(self.preview_file)
        self.file_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.file_list.customContextMenuRequested.connect(self.show_context_menu)
        
        # 添加文件列表到选项卡
        self.tab_widget.addTab(self.file_list, "所有文件")
        
        # 创建分类视图
        self.category_tree = QTreeWidget()
        self.category_tree.setHeaderLabels(["类型", "文件数"])
        self.category_tree.header().setSectionResizeMode(QHeaderView.ResizeToContents)
        
        # 添加分类视图到选项卡
        self.tab_widget.addTab(self.category_tree, "按类型分类")
        
        # 添加选项卡到布局
        layout.addWidget(self.tab_widget)
        
        # 创建预览区域
        self.preview_widget = QWidget()
        preview_layout = QVBoxLayout(self.preview_widget)
        
        self.preview_label = QLabel("选择文件进行预览")
        self.preview_label.setAlignment(Qt.AlignCenter)
        preview_layout.addWidget(self.preview_label)
        
        self.preview_web = QWebEngineView()
        self.preview_web.setVisible(False)
        preview_layout.addWidget(self.preview_web)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        splitter.addWidget(self.tab_widget)
        splitter.addWidget(self.preview_widget)
        splitter.setSizes([300, 200])
        
        # 添加分割器到布局
        layout.addWidget(splitter)
        
        # 创建状态栏
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        self.count_label = QLabel("文件数: 0")
        status_layout.addWidget(self.count_label)
        
        # 添加状态栏到布局
        layout.addLayout(status_layout)
    
    def on_group_joined(self, group_id):
        """处理加入小组事件"""
        self.group_id = group_id
        self.refresh()
    
    def on_mode_changed(self, mode):
        """处理模式切换事件"""
        self.mode = mode
        logger.info(f"文件面板切换到模式: {mode}")
    
    def refresh(self):
        """刷新文件列表"""
        if not self.group_id:
            return
        
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 发送请求获取文件列表
            response = requests.get(f"{api_base_url}/files?group_id={self.group_id}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.update_file_list(data.get('files', []))
                else:
                    self.parent.show_error(f"获取文件列表失败: {data.get('message')}")
            else:
                self.parent.show_error(f"获取文件列表失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.parent.show_error(f"刷新文件列表时出错: {str(e)}")
    
    def update_file_list(self, files):
        """更新文件列表"""
        # 清空列表
        self.file_list.clear()
        
        # 添加文件
        for file_info in files:
            item = FileListItem(file_info)
            self.file_list.addItem(item)
        
        # 更新计数
        self.count_label.setText(f"文件数: {len(files)}")
        
        # 更新分类视图
        self.update_category_tree(files)
    
    def update_category_tree(self, files):
        """更新分类视图"""
        # 清空树
        self.category_tree.clear()
        
        # 按类型分组
        categories = {}
        for file_info in files:
            file_type = file_info.get('type', '未知')
            if file_type not in categories:
                categories[file_type] = []
            categories[file_type].append(file_info)
        
        # 添加分类
        for category, category_files in categories.items():
            # 创建分类项
            category_item = QTreeWidgetItem(self.category_tree)
            category_item.setText(0, category)
            category_item.setText(1, str(len(category_files)))
            
            # 添加文件
            for file_info in category_files:
                file_item = QTreeWidgetItem(category_item)
                file_item.setText(0, file_info.get('name', '未命名文件'))
                file_item.setData(0, Qt.UserRole, file_info)
        
        # 展开所有项
        self.category_tree.expandAll()
    
    def upload_file(self):
        """上传文件"""
        if not self.group_id:
            self.parent.show_error("请先加入小组")
            return
        
        # 选择文件
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择要上传的文件", "",
            "所有文件 (*);;图片文件 (*.jpg *.jpeg *.png *.gif);;文档文件 (*.pdf *.doc *.docx *.ppt *.pptx *.xls *.xlsx);;视频文件 (*.mp4 *.avi *.mov);;音频文件 (*.mp3 *.wav)"
        )
        
        if not file_path:
            return
        
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 准备元数据
            file_name = os.path.basename(file_path)
            file_ext = os.path.splitext(file_name)[1].lower().lstrip('.')
            
            # 确定文件类型
            file_type = "其他"
            if file_ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp']:
                file_type = "图片"
            elif file_ext in ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx']:
                file_type = "文档"
            elif file_ext in ['mp4', 'avi', 'mov', 'wmv']:
                file_type = "视频"
            elif file_ext in ['mp3', 'wav', 'aac', 'ogg']:
                file_type = "音频"
            
            metadata = {
                'name': file_name,
                'type': file_type,
                'group_id': self.group_id,
                'uploader_id': self.parent.device_id,
                'uploader_name': self.parent.device_discovery.device_name
            }
            
            # 创建进度对话框
            progress_dialog = QMessageBox(self)
            progress_dialog.setWindowTitle("上传文件")
            progress_dialog.setText(f"正在上传: {file_name}")
            
            progress_bar = QProgressBar(progress_dialog)
            progress_bar.setRange(0, 100)
            progress_bar.setValue(0)
            
            progress_dialog.setStandardButtons(QMessageBox.Cancel)
            progress_dialog.setDefaultButton(QMessageBox.Cancel)
            
            # 创建上传线程
            upload_thread = FileUploadThread(
                f"{api_base_url}/files/upload",
                file_path,
                metadata
            )
            
            # 连接信号
            upload_thread.progress_signal.connect(progress_bar.setValue)
            upload_thread.finished_signal.connect(
                lambda success, message, file_info: self.handle_upload_finished(
                    success, message, file_info, progress_dialog, upload_thread
                )
            )
            
            # 存储线程
            self.upload_threads[file_path] = upload_thread
            
            # 启动线程
            upload_thread.start()
            
            # 显示进度对话框
            if progress_dialog.exec_() == QMessageBox.Cancel:
                # 取消上传
                upload_thread.stop()
        
        except Exception as e:
            self.parent.show_error(f"上传文件时出错: {str(e)}")
    
    def handle_upload_finished(self, success, message, file_info, dialog, thread):
        """处理上传完成"""
        # 关闭对话框
        dialog.accept()
        
        # 清理线程
        thread_key = next((k for k, v in self.upload_threads.items() if v == thread), None)
        if thread_key:
            del self.upload_threads[thread_key]
        
        if success:
            self.status_label.setText(f"上传成功: {file_info.get('name')}")
            logger.info(f"文件上传成功: {file_info.get('name')}")
            
            # 刷新文件列表
            self.refresh()
        else:
            self.parent.show_error(f"上传文件失败: {message}")
    
    def download_selected(self):
        """下载选中的文件"""
        # 获取选中的项
        selected_items = self.file_list.selectedItems()
        
        if not selected_items:
            self.parent.show_error("请先选择要下载的文件")
            return
        
        # 选择保存目录
        save_dir = QFileDialog.getExistingDirectory(self, "选择保存目录")
        
        if not save_dir:
            return
        
        # 下载选中的文件
        for item in selected_items:
            self.download_file(item.file_info, save_dir)
    
    def download_file(self, file_info, save_dir):
        """下载文件"""
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 准备下载URL和保存路径
            file_id = file_info.get('id')
            file_name = file_info.get('name')
            download_url = f"{api_base_url}/files/{file_id}/download"
            save_path = os.path.join(save_dir, file_name)
            
            # 创建进度对话框
            progress_dialog = QMessageBox(self)
            progress_dialog.setWindowTitle("下载文件")
            progress_dialog.setText(f"正在下载: {file_name}")
            
            progress_bar = QProgressBar(progress_dialog)
            progress_bar.setRange(0, 100)
            progress_bar.setValue(0)
            
            progress_dialog.setStandardButtons(QMessageBox.Cancel)
            progress_dialog.setDefaultButton(QMessageBox.Cancel)
            
            # 创建下载线程
            download_thread = FileDownloadThread(download_url, save_path)
            
            # 连接信号
            download_thread.progress_signal.connect(progress_bar.setValue)
            download_thread.finished_signal.connect(
                lambda success, message: self.handle_download_finished(
                    success, message, progress_dialog, download_thread
                )
            )
            
            # 存储线程
            self.download_threads[file_id] = download_thread
            
            # 启动线程
            download_thread.start()
            
            # 显示进度对话框
            if progress_dialog.exec_() == QMessageBox.Cancel:
                # 取消下载
                download_thread.stop()
        
        except Exception as e:
            self.parent.show_error(f"下载文件时出错: {str(e)}")
    
    def handle_download_finished(self, success, message, dialog, thread):
        """处理下载完成"""
        # 关闭对话框
        dialog.accept()
        
        # 清理线程
        thread_key = next((k for k, v in self.download_threads.items() if v == thread), None)
        if thread_key:
            del self.download_threads[thread_key]
        
        if success:
            self.status_label.setText(f"下载成功: {message}")
            logger.info(f"文件下载成功: {message}")
        else:
            self.parent.show_error(f"下载文件失败: {message}")
    
    def preview_file(self, item):
        """预览文件"""
        file_info = item.file_info
        file_id = file_info.get('id')
        file_name = file_info.get('name')
        file_type = file_info.get('type')
        
        # 获取API基础URL
        api_base_url = self.parent.api_base_url
        
        # 准备预览URL
        preview_url = f"{api_base_url}/files/{file_id}/preview"
        
        # 显示预览
        self.preview_label.setVisible(False)
        self.preview_web.setVisible(True)
        self.preview_web.load(QUrl(preview_url))
        
        self.status_label.setText(f"正在预览: {file_name}")
    
    def show_context_menu(self, position):
        """显示上下文菜单"""
        # 获取选中的项
        selected_items = self.file_list.selectedItems()
        
        if not selected_items:
            return
        
        # 创建菜单
        menu = QMenu(self)
        
        # 添加预览操作
        preview_action = QAction("预览", self)
        preview_action.triggered.connect(lambda: self.preview_file(selected_items[0]))
        menu.addAction(preview_action)
        
        # 添加下载操作
        download_action = QAction("下载", self)
        download_action.triggered.connect(self.download_selected)
        menu.addAction(download_action)
        
        # 添加分享操作
        share_action = QAction("分享到白板", self)
        share_action.triggered.connect(lambda: self.share_to_whiteboard(selected_items[0].file_info))
        menu.addAction(share_action)
        
        # 显示菜单
        menu.exec_(self.file_list.mapToGlobal(position))
    
    def share_to_whiteboard(self, file_info):
        """分享文件到白板"""
        # 获取API基础URL
        api_base_url = self.parent.api_base_url
        
        # 准备预览URL
        file_id = file_info.get('id')
        preview_url = f"{api_base_url}/files/{file_id}/preview"
        
        # 切换到白板面板
        self.parent.tab_widget.setCurrentWidget(self.parent.whiteboard_panel)
        
        # 通知白板面板加载文件
        # 这里需要实现白板面板的加载文件功能
        self.status_label.setText(f"已分享文件到白板: {file_info.get('name')}")
        
        # 使用JavaScript将文件添加到白板
        js = f"""
        if (window.excalidrawApp) {{
            // 添加图片到白板
            window.excalidrawApp.addImage('{preview_url}');
        }}
        """
        
        self.parent.whiteboard_panel.web_view.page().runJavaScript(js)