#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小组端白板面板
"""

import os
import sys
import json
import requests
import logging
import time
from datetime import datetime

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QGridLayout,
    QScrollArea, QFrame, QSplitter, QComboBox, QMessageBox, QMenu, QAction,
    QToolBar, QColorDialog, QSlider, QSpinBox, QFileDialog
)
from PyQt5.QtCore import Qt, QSize, QUrl, QTimer, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QIcon, QPixmap, QColor
from PyQt5.QtWebEngineWidgets import QWebEngineView, QWebEngineSettings, QWebEngineProfile
from PyQt5.QtWebChannel import QWebChannel

from shared.config import get_config

# 获取配置
config = get_config()

# 配置日志
logger = logging.getLogger('whiteboard_panel')

class WhiteboardPanel(QWidget):
    """白板面板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.group_id = None
        self.mode = "teaching"
        self.current_color = "#000000"
        self.current_tool = "pen"
        self.current_size = 2
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 创建主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建工具栏
        toolbar = QToolBar()
        toolbar.setIconSize(QSize(24, 24))
        
        # 添加工具选择
        tool_label = QLabel("工具:")
        toolbar.addWidget(tool_label)
        
        self.tool_combo = QComboBox()
        self.tool_combo.addItem("画笔", "pen")
        self.tool_combo.addItem("荧光笔", "highlighter")
        self.tool_combo.addItem("橡皮擦", "eraser")
        self.tool_combo.addItem("文本", "text")
        self.tool_combo.addItem("箭头", "arrow")
        self.tool_combo.addItem("矩形", "rectangle")
        self.tool_combo.addItem("椭圆", "ellipse")
        self.tool_combo.currentIndexChanged.connect(self.change_tool)
        toolbar.addWidget(self.tool_combo)
        
        toolbar.addSeparator()
        
        # 添加颜色选择
        color_label = QLabel("颜色:")
        toolbar.addWidget(color_label)
        
        self.color_btn = QPushButton()
        self.color_btn.setFixedSize(24, 24)
        self.color_btn.setStyleSheet(f"background-color: {self.current_color};")
        self.color_btn.clicked.connect(self.select_color)
        toolbar.addWidget(self.color_btn)
        
        # 添加常用颜色按钮
        common_colors = ["#000000", "#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF", "#00FFFF"]
        for color in common_colors:
            btn = QPushButton()
            btn.setFixedSize(16, 16)
            btn.setStyleSheet(f"background-color: {color};")
            btn.clicked.connect(lambda checked, c=color: self.set_color(c))
            toolbar.addWidget(btn)
        
        toolbar.addSeparator()
        
        # 添加笔刷大小
        size_label = QLabel("粗细:")
        toolbar.addWidget(size_label)
        
        self.size_spin = QSpinBox()
        self.size_spin.setRange(1, 20)
        self.size_spin.setValue(self.current_size)
        self.size_spin.valueChanged.connect(self.change_size)
        toolbar.addWidget(self.size_spin)
        
        toolbar.addSeparator()
        
        # 添加操作按钮
        self.undo_btn = QPushButton("撤销")
        self.undo_btn.clicked.connect(self.undo)
        toolbar.addWidget(self.undo_btn)
        
        self.redo_btn = QPushButton("重做")
        self.redo_btn.clicked.connect(self.redo)
        toolbar.addWidget(self.redo_btn)
        
        self.clear_btn = QPushButton("清空")
        self.clear_btn.clicked.connect(self.clear)
        toolbar.addWidget(self.clear_btn)
        
        toolbar.addSeparator()
        
        # 添加底色选择
        bg_label = QLabel("底色:")
        toolbar.addWidget(bg_label)
        
        self.bg_combo = QComboBox()
        self.bg_combo.addItem("白色", "#FFFFFF")
        self.bg_combo.addItem("黑色", "#000000")
        self.bg_combo.addItem("灰色", "#CCCCCC")
        self.bg_combo.addItem("浅黄", "#FFFFCC")
        self.bg_combo.addItem("浅蓝", "#CCFFFF")
        self.bg_combo.currentIndexChanged.connect(self.change_background)
        toolbar.addWidget(self.bg_combo)
        
        toolbar.addSeparator()
        
        # 添加白板管理按钮
        self.manage_btn = QPushButton("管理")
        self.manage_btn.clicked.connect(self.show_whiteboard_manager)
        toolbar.addWidget(self.manage_btn)
        
        # 添加保存按钮
        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_whiteboard)
        toolbar.addWidget(self.save_btn)
        
        # 添加工具栏到布局
        layout.addWidget(toolbar)
        
        # 创建WebView
        self.web_view = QWebEngineView()
        
        # 配置WebView
        settings = self.web_view.settings()
        settings.setAttribute(QWebEngineSettings.JavascriptEnabled, True)
        settings.setAttribute(QWebEngineSettings.LocalStorageEnabled, True)
        settings.setAttribute(QWebEngineSettings.WebGLEnabled, True)
        
        # 创建WebChannel
        self.channel = QWebChannel()
        self.web_view.page().setWebChannel(self.channel)
        
        # 加载Excalidraw
        self.load_excalidraw()
        
        # 添加WebView到布局
        layout.addWidget(self.web_view)
        
        # 创建状态栏
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        self.room_label = QLabel("房间: 未连接")
        status_layout.addWidget(self.room_label)
        
        # 添加状态栏到布局
        layout.addLayout(status_layout)
    
    def load_excalidraw(self):
        """加载白板"""
        # 检查本地白板文件是否存在
        whiteboard_path = os.path.join(os.path.dirname(__file__), "../../../backend/static/whiteboard.html")
        
        if os.path.exists(whiteboard_path):
            # 构建URL参数
            params = []
            if self.group_id:
                params.append(f"whiteboard_id=group_{self.group_id}")
            params.append(f"user_id=group_client_{os.getpid()}")
            
            url_params = "&".join(params)
            file_url = QUrl.fromLocalFile(whiteboard_path)
            if url_params:
                file_url.setQuery(url_params)
            
            self.web_view.load(file_url)
            logger.info(f"从本地加载白板: {whiteboard_path}")
        else:
            # 加载在线版本（备用）
            self.web_view.load(QUrl("https://excalidraw.com/"))
            logger.info("从在线加载Excalidraw")
        
        # 连接加载完成信号
        self.web_view.loadFinished.connect(self.on_excalidraw_loaded)
    
    def on_excalidraw_loaded(self, success):
        """Excalidraw加载完成"""
        if success:
            logger.info("Excalidraw加载成功")
            self.status_label.setText("Excalidraw已加载")
            
            # 初始化Excalidraw
            self.init_excalidraw()
        else:
            logger.error("Excalidraw加载失败")
            self.status_label.setText("Excalidraw加载失败")
    
    def init_excalidraw(self):
        """初始化Excalidraw"""
        # 注入自定义JavaScript以控制Excalidraw
        js = """
        // 存储Excalidraw App实例
        window.excalidrawApp = null;
        
        // 等待Excalidraw加载完成
        function waitForExcalidraw() {
            if (window.ExcalidrawLib && window.ExcalidrawLib.default) {
                initExcalidraw();
            } else {
                setTimeout(waitForExcalidraw, 100);
            }
        }
        
        // 初始化Excalidraw
        function initExcalidraw() {
            try {
                const container = document.getElementById("root");
                if (!container) return;
                
                window.excalidrawApp = window.ExcalidrawLib.default.initializeApp(container, {
                    name: "智慧课堂白板",
                    isCollaborating: true,
                    theme: "light"
                });
                
                console.log("Excalidraw初始化成功");
                
                // 通知Qt
                if (window.qt && window.qt.webChannelTransport) {
                    console.log("通知Qt Excalidraw已初始化");
                    window.excalidrawInitialized = true;
                }
            } catch (error) {
                console.error("初始化Excalidraw时出错:", error);
            }
        }
        
        // 设置工具
        window.setTool = function(tool) {
            if (!window.excalidrawApp) return;
            window.excalidrawApp.setActiveTool(tool);
        };
        
        // 设置颜色
        window.setColor = function(color) {
            if (!window.excalidrawApp) return;
            window.excalidrawApp.setAppOptions({ currentItemStrokeColor: color });
        };
        
        // 设置笔刷大小
        window.setSize = function(size) {
            if (!window.excalidrawApp) return;
            window.excalidrawApp.setAppOptions({ currentItemStrokeWidth: size });
        };
        
        // 设置背景颜色
        window.setBackground = function(color) {
            if (!window.excalidrawApp) return;
            window.excalidrawApp.setAppOptions({ viewBackgroundColor: color });
        };
        
        // 撤销
        window.undo = function() {
            if (!window.excalidrawApp) return;
            window.excalidrawApp.undo();
        };
        
        // 重做
        window.redo = function() {
            if (!window.excalidrawApp) return;
            window.excalidrawApp.redo();
        };
        
        // 清空
        window.clear = function() {
            if (!window.excalidrawApp) return;
            window.excalidrawApp.resetScene();
        };
        
        // 保存为图片
        window.saveAsImage = function() {
            if (!window.excalidrawApp) return null;
            return window.excalidrawApp.getSceneElements();
        };
        
        // 加入协作房间
        window.joinRoom = function(roomId) {
            if (!window.excalidrawApp) return;
            window.excalidrawApp.updateScene({ roomId: roomId });
        };
        
        // 开始监听
        waitForExcalidraw();
        """
        
        self.web_view.page().runJavaScript(js)
    
    def on_group_joined(self, group_id):
        """处理加入小组事件"""
        self.group_id = group_id
        self.room_label.setText(f"房间: {group_id}")
        
        # 加入协作房间
        self.join_room(group_id)
    
    def on_mode_changed(self, mode):
        """处理模式切换事件"""
        self.mode = mode
        logger.info(f"白板面板切换到模式: {mode}")
    
    def refresh(self):
        """刷新白板"""
        # 重新加载Excalidraw
        self.load_excalidraw()
    
    def change_tool(self, index):
        """切换工具"""
        self.current_tool = self.tool_combo.currentData()
        logger.info(f"切换工具: {self.current_tool}")
        
        # 调用JavaScript设置工具
        js = f"window.setTool('{self.current_tool}');"
        self.web_view.page().runJavaScript(js)
    
    def select_color(self):
        """选择颜色"""
        color = QColorDialog.getColor(QColor(self.current_color), self, "选择颜色")
        if color.isValid():
            self.set_color(color.name())
    
    def set_color(self, color):
        """设置颜色"""
        self.current_color = color
        self.color_btn.setStyleSheet(f"background-color: {color};")
        logger.info(f"设置颜色: {color}")
        
        # 调用JavaScript设置颜色
        js = f"window.setColor('{color}');"
        self.web_view.page().runJavaScript(js)
    
    def change_size(self, size):
        """更改笔刷大小"""
        self.current_size = size
        logger.info(f"设置笔刷大小: {size}")
        
        # 调用JavaScript设置笔刷大小
        js = f"window.setSize({size});"
        self.web_view.page().runJavaScript(js)
    
    def change_background(self, index):
        """更改背景颜色"""
        color = self.bg_combo.currentData()
        logger.info(f"设置背景颜色: {color}")
        
        # 调用JavaScript设置背景颜色
        js = f"window.setBackground('{color}');"
        self.web_view.page().runJavaScript(js)
    
    def undo(self):
        """撤销"""
        logger.info("撤销")
        
        # 调用JavaScript撤销
        js = "window.undo();"
        self.web_view.page().runJavaScript(js)
    
    def redo(self):
        """重做"""
        logger.info("重做")
        
        # 调用JavaScript重做
        js = "window.redo();"
        self.web_view.page().runJavaScript(js)
    
    def clear(self):
        """清空"""
        # 确认对话框
        reply = QMessageBox.question(
            self, "确认清空", "确定要清空白板内容吗？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            logger.info("清空白板")
            
            # 调用JavaScript清空
            js = "window.clear();"
            self.web_view.page().runJavaScript(js)
    
    def save_whiteboard(self):
        """保存白板内容"""
        # 选择保存路径
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存白板", "", "PNG图片 (*.png);;JPEG图片 (*.jpg);;SVG矢量图 (*.svg)"
        )
        
        if not file_path:
            return
        
        # 获取白板内容
        js = "window.saveAsImage();"
        self.web_view.page().runJavaScript(js, self.handle_save_result)
    
    def handle_save_result(self, result):
        """处理保存结果"""
        if result:
            try:
                # 将结果保存到文件
                # 这里需要根据Excalidraw的API进行适配
                # 暂时使用截图方式
                self.web_view.grab().save(file_path)
                self.status_label.setText(f"已保存到: {file_path}")
                logger.info(f"白板已保存到: {file_path}")
            except Exception as e:
                self.parent.show_error(f"保存白板时出错: {str(e)}")
        else:
            self.parent.show_error("获取白板内容失败")
    
    def join_room(self, room_id):
        """加入协作房间"""
        logger.info(f"加入协作房间: {room_id}")
        
        # 调用JavaScript加入房间
        js = f"window.joinRoom('{room_id}');"
        self.web_view.page().runJavaScript(js)
    
    def show_whiteboard_manager(self):
        """显示白板管理器"""
        from .whiteboard_manager import WhiteboardManagerDialog
        
        dialog = WhiteboardManagerDialog(self, self.group_id)
        if dialog.exec_() == dialog.Accepted:
            # 如果选择了新的白板，重新加载
            selected_whiteboard = dialog.get_selected_whiteboard()
            if selected_whiteboard:
                self.load_whiteboard(selected_whiteboard['whiteboard_id'])
    
    def load_whiteboard(self, whiteboard_id):
        """加载指定的白板"""
        # 重新构建URL参数
        params = [f"whiteboard_id={whiteboard_id}"]
        params.append(f"user_id=group_client_{os.getpid()}")
        
        url_params = "&".join(params)
        whiteboard_path = os.path.join(os.path.dirname(__file__), "../../../backend/static/whiteboard.html")
        
        if os.path.exists(whiteboard_path):
            file_url = QUrl.fromLocalFile(whiteboard_path)
            file_url.setQuery(url_params)
            self.web_view.load(file_url)
            logger.info(f"加载白板: {whiteboard_id}")
        
    def create_new_whiteboard(self, name):
        """创建新白板"""
        try:
            api_base_url = f"http://{config.BACKEND_HOST}:{config.BACKEND_PORT}"
            response = requests.post(
                f"{api_base_url}/api/whiteboard/create",
                json={
                    "name": name,
                    "created_by": f"group_client_{os.getpid()}",
                    "group_id": self.group_id
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    whiteboard_data = data.get('whiteboard')
                    self.status_label.setText(f"创建白板成功: {name}")
                    logger.info(f"创建白板成功: {whiteboard_data['whiteboard_id']}")
                    return True, whiteboard_data
                else:
                    self.status_label.setText(f"创建白板失败: {data.get('message')}")
                    return False, None
            else:
                self.status_label.setText(f"创建白板失败: HTTP {response.status_code}")
                return False, None
                
        except Exception as e:
            logger.error(f"创建白板时出错: {str(e)}")
            self.status_label.setText(f"创建白板时出错: {str(e)}")
            return False, None