#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
白板管理器对话框
"""

import os
import sys
import json
import requests
import logging
from datetime import datetime

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QListWidget,
    QListWidgetItem, QInputDialog, QMessageBox, QSplitter, QTextEdit,
    QGroupBox, QGridLayout, QScrollArea, QFrame
)
from PyQt5.QtCore import Qt, QSize, QTimer, pyqtSignal
from PyQt5.QtGui import QIcon, QPixmap, QFont

from shared.config import get_config

# 获取配置
config = get_config()

# 配置日志
logger = logging.getLogger('whiteboard_manager')

class WhiteboardManagerDialog(QDialog):
    """白板管理器对话框"""
    
    def __init__(self, parent=None, group_id=None):
        super().__init__(parent)
        self.parent = parent
        self.group_id = group_id
        self.selected_whiteboard = None
        self.whiteboards = []
        self.api_base_url = f"http://{config.BACKEND_HOST}:{config.BACKEND_PORT}"
        
        self.init_ui()
        self.load_whiteboards()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("白板管理器")
        self.setMinimumSize(800, 600)
        self.setModal(True)
        
        # 创建主布局
        layout = QVBoxLayout(self)
        
        # 创建标题
        title_label = QLabel("白板管理器")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：白板列表
        left_widget = QGroupBox("白板列表")
        left_layout = QVBoxLayout(left_widget)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.new_btn = QPushButton("新建白板")
        self.new_btn.clicked.connect(self.create_new_whiteboard)
        button_layout.addWidget(self.new_btn)
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_whiteboards)
        button_layout.addWidget(self.refresh_btn)
        
        button_layout.addStretch()
        
        self.delete_btn = QPushButton("删除")
        self.delete_btn.clicked.connect(self.delete_whiteboard)
        self.delete_btn.setEnabled(False)
        button_layout.addWidget(self.delete_btn)
        
        left_layout.addLayout(button_layout)
        
        # 白板列表
        self.whiteboard_list = QListWidget()
        self.whiteboard_list.itemClicked.connect(self.on_whiteboard_selected)
        self.whiteboard_list.itemDoubleClicked.connect(self.on_whiteboard_double_clicked)
        left_layout.addWidget(self.whiteboard_list)
        
        splitter.addWidget(left_widget)
        
        # 右侧：白板详情
        right_widget = QGroupBox("白板详情")
        right_layout = QVBoxLayout(right_widget)
        
        # 详情显示区域
        self.details_scroll = QScrollArea()
        self.details_widget = QWidget()
        self.details_layout = QVBoxLayout(self.details_widget)
        
        # 基本信息
        info_group = QGroupBox("基本信息")
        info_layout = QGridLayout(info_group)
        
        self.name_label = QLabel("名称: -")
        info_layout.addWidget(self.name_label, 0, 0)
        
        self.created_by_label = QLabel("创建者: -")
        info_layout.addWidget(self.created_by_label, 1, 0)
        
        self.created_at_label = QLabel("创建时间: -")
        info_layout.addWidget(self.created_at_label, 2, 0)
        
        self.updated_at_label = QLabel("更新时间: -")
        info_layout.addWidget(self.updated_at_label, 3, 0)
        
        self.version_label = QLabel("当前版本: -")
        info_layout.addWidget(self.version_label, 4, 0)
        
        self.details_layout.addWidget(info_group)
        
        # 版本历史
        version_group = QGroupBox("版本历史")
        version_layout = QVBoxLayout(version_group)
        
        self.version_list = QListWidget()
        self.version_list.setMaximumHeight(200)
        version_layout.addWidget(self.version_list)
        
        self.details_layout.addWidget(version_group)
        
        # 缩略图预览
        preview_group = QGroupBox("预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_label = QLabel("暂无预览")
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setMinimumHeight(200)
        self.preview_label.setStyleSheet("border: 1px solid #ccc; background-color: #f9f9f9;")
        preview_layout.addWidget(self.preview_label)
        
        self.details_layout.addWidget(preview_group)
        
        self.details_layout.addStretch()
        self.details_scroll.setWidget(self.details_widget)
        right_layout.addWidget(self.details_scroll)
        
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([400, 400])
        layout.addWidget(splitter)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.select_btn = QPushButton("选择并打开")
        self.select_btn.clicked.connect(self.select_whiteboard)
        self.select_btn.setEnabled(False)
        button_layout.addWidget(self.select_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def load_whiteboards(self):
        """加载白板列表"""
        try:
            # 构建请求参数
            params = {}
            if self.group_id:
                params['group_id'] = self.group_id
            
            response = requests.get(
                f"{self.api_base_url}/api/whiteboard/list",
                params=params
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.whiteboards = data.get('whiteboards', [])
                    self.update_whiteboard_list()
                else:
                    self.show_error(f"加载白板列表失败: {data.get('message')}")
            else:
                self.show_error(f"加载白板列表失败: HTTP {response.status_code}")
                
        except Exception as e:
            logger.error(f"加载白板列表时出错: {str(e)}")
            self.show_error(f"加载白板列表时出错: {str(e)}")
    
    def update_whiteboard_list(self):
        """更新白板列表显示"""
        self.whiteboard_list.clear()
        
        for whiteboard in self.whiteboards:
            item = QListWidgetItem()
            
            # 设置显示文本
            name = whiteboard.get('name', '未命名白板')
            created_at = whiteboard.get('created_at', '')
            if created_at:
                try:
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    created_at = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    pass
            
            item.setText(f"{name}\n创建时间: {created_at}")
            item.setData(Qt.UserRole, whiteboard)
            
            # 设置图标（如果有缩略图）
            if whiteboard.get('thumbnail'):
                # 这里可以加载缩略图
                pass
            
            self.whiteboard_list.addItem(item)
    
    def on_whiteboard_selected(self, item):
        """白板被选中"""
        whiteboard = item.data(Qt.UserRole)
        if whiteboard:
            self.selected_whiteboard = whiteboard
            self.update_whiteboard_details(whiteboard)
            self.select_btn.setEnabled(True)
            self.delete_btn.setEnabled(True)
    
    def on_whiteboard_double_clicked(self, item):
        """白板被双击"""
        self.on_whiteboard_selected(item)
        self.select_whiteboard()
    
    def update_whiteboard_details(self, whiteboard):
        """更新白板详情显示"""
        # 更新基本信息
        self.name_label.setText(f"名称: {whiteboard.get('name', '-')}")
        self.created_by_label.setText(f"创建者: {whiteboard.get('created_by', '-')}")
        
        created_at = whiteboard.get('created_at', '')
        if created_at:
            try:
                dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                created_at = dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass
        self.created_at_label.setText(f"创建时间: {created_at}")
        
        updated_at = whiteboard.get('updated_at', '')
        if updated_at:
            try:
                dt = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                updated_at = dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass
        self.updated_at_label.setText(f"更新时间: {updated_at}")
        
        self.version_label.setText(f"当前版本: {whiteboard.get('current_version', 1)}")
        
        # 加载版本历史
        self.load_whiteboard_versions(whiteboard.get('whiteboard_id'))
        
        # 更新预览
        if whiteboard.get('thumbnail'):
            # 这里可以显示缩略图
            self.preview_label.setText("预览图片")
        else:
            self.preview_label.setText("暂无预览")
    
    def load_whiteboard_versions(self, whiteboard_id):
        """加载白板版本历史"""
        try:
            response = requests.get(
                f"{self.api_base_url}/api/whiteboard/{whiteboard_id}/versions",
                params={'limit': 10}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    versions = data.get('versions', [])
                    self.update_version_list(versions)
                else:
                    logger.warning(f"加载版本历史失败: {data.get('message')}")
            else:
                logger.warning(f"加载版本历史失败: HTTP {response.status_code}")
                
        except Exception as e:
            logger.error(f"加载版本历史时出错: {str(e)}")
    
    def update_version_list(self, versions):
        """更新版本列表显示"""
        self.version_list.clear()
        
        for version in versions:
            item = QListWidgetItem()
            
            created_at = version.get('created_at', '')
            if created_at:
                try:
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    created_at = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    pass
            
            text = f"版本 {version.get('version_number', 1)} - {created_at}"
            if version.get('comment'):
                text += f"\n{version.get('comment')}"
            
            item.setText(text)
            item.setData(Qt.UserRole, version)
            
            self.version_list.addItem(item)
    
    def create_new_whiteboard(self):
        """创建新白板"""
        name, ok = QInputDialog.getText(self, "新建白板", "请输入白板名称:")
        
        if ok and name.strip():
            try:
                response = requests.post(
                    f"{self.api_base_url}/api/whiteboard/create",
                    json={
                        "name": name.strip(),
                        "created_by": f"group_client_{os.getpid()}",
                        "group_id": self.group_id
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        QMessageBox.information(self, "成功", f"白板 '{name}' 创建成功！")
                        self.load_whiteboards()  # 刷新列表
                    else:
                        self.show_error(f"创建白板失败: {data.get('message')}")
                else:
                    self.show_error(f"创建白板失败: HTTP {response.status_code}")
                    
            except Exception as e:
                logger.error(f"创建白板时出错: {str(e)}")
                self.show_error(f"创建白板时出错: {str(e)}")
    
    def delete_whiteboard(self):
        """删除白板"""
        if not self.selected_whiteboard:
            return
        
        name = self.selected_whiteboard.get('name', '未命名白板')
        reply = QMessageBox.question(
            self, "确认删除", f"确定要删除白板 '{name}' 吗？\n此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                whiteboard_id = self.selected_whiteboard.get('whiteboard_id')
                response = requests.delete(
                    f"{self.api_base_url}/api/whiteboard/{whiteboard_id}",
                    json={"deleted_by": f"group_client_{os.getpid()}"}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        QMessageBox.information(self, "成功", f"白板 '{name}' 已删除！")
                        self.load_whiteboards()  # 刷新列表
                        self.selected_whiteboard = None
                        self.select_btn.setEnabled(False)
                        self.delete_btn.setEnabled(False)
                    else:
                        self.show_error(f"删除白板失败: {data.get('message')}")
                else:
                    self.show_error(f"删除白板失败: HTTP {response.status_code}")
                    
            except Exception as e:
                logger.error(f"删除白板时出错: {str(e)}")
                self.show_error(f"删除白板时出错: {str(e)}")
    
    def select_whiteboard(self):
        """选择白板"""
        if self.selected_whiteboard:
            self.accept()
    
    def get_selected_whiteboard(self):
        """获取选中的白板"""
        return self.selected_whiteboard
    
    def show_error(self, message):
        """显示错误消息"""
        QMessageBox.critical(self, "错误", message)
        logger.error(message)