#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小组端录制面板
"""

import os
import sys
import json
import requests
import logging
import time
import subprocess
import threading
from datetime import datetime

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QGridLayout,
    QScrollArea, QFrame, QSplitter, QComboBox, QMessageBox, QMenu, QAction,
    QToolBar, QFileDialog, QListWidget, QListWidgetItem, QProgressBar,
    QTabWidget, QTreeWidget, QTreeWidgetItem, QHeaderView, QSpinBox
)
from PyQt5.QtCore import Qt, QSize, QUrl, QTimer, pyqtSignal, pyqtSlot, QThread
from PyQt5.QtGui import QIcon, QPixmap
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtMultimediaWidgets import QVideoWidget

from shared.config import get_config

# 获取配置
config = get_config()

# 配置日志
logger = logging.getLogger('recording_panel')

class RecordingThread(QThread):
    """录制线程"""
    
    update_signal = pyqtSignal(int, str)  # 时间, 状态
    finished_signal = pyqtSignal(bool, str, str)  # 成功, 消息, 文件路径
    
    def __init__(self, output_path, device=None, resolution="1920x1080", fps=30):
        super().__init__()
        self.output_path = output_path
        self.device = device
        self.resolution = resolution
        self.fps = fps
        self.is_running = True
        self.process = None
        self.start_time = 0
    
    def run(self):
        """运行线程"""
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(self.output_path), exist_ok=True)
            
            # 构建FFmpeg命令
            if self.device:
                # 使用摄像头
                command = [
                    "ffmpeg",
                    "-f", "v4l2",
                    "-i", self.device,
                    "-c:v", "libx264",
                    "-preset", "ultrafast",
                    "-crf", "22",
                    "-pix_fmt", "yuv420p",
                    "-r", str(self.fps),
                    "-y",
                    self.output_path
                ]
            else:
                # 录制屏幕
                command = [
                    "ffmpeg",
                    "-f", "x11grab",
                    "-s", self.resolution,
                    "-r", str(self.fps),
                    "-i", ":0.0",
                    "-c:v", "libx264",
                    "-preset", "ultrafast",
                    "-crf", "22",
                    "-pix_fmt", "yuv420p",
                    "-y",
                    self.output_path
                ]
            
            logger.info(f"启动录制: {' '.join(command)}")
            
            # 启动FFmpeg进程
            self.process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # 记录开始时间
            self.start_time = time.time()
            
            # 更新状态
            self.update_signal.emit(0, "正在录制...")
            
            # 监控录制状态
            while self.is_running and self.process.poll() is None:
                # 计算录制时间
                elapsed = int(time.time() - self.start_time)
                
                # 更新状态
                self.update_signal.emit(elapsed, self.format_time(elapsed))
                
                # 等待一秒
                time.sleep(1)
            
            # 检查进程状态
            if self.process.poll() is None:
                # 进程仍在运行，发送终止信号
                self.process.terminate()
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.process.kill()
            
            # 检查是否正常结束
            if self.is_running:
                # 正常结束
                elapsed = int(time.time() - self.start_time)
                self.finished_signal.emit(True, f"录制完成，时长: {self.format_time(elapsed)}", self.output_path)
            else:
                # 被取消
                self.finished_signal.emit(False, "录制已取消", "")
        
        except Exception as e:
            logger.error(f"录制时出错: {str(e)}")
            self.finished_signal.emit(False, f"录制时出错: {str(e)}", "")
    
    def stop(self):
        """停止录制"""
        self.is_running = False
    
    def format_time(self, seconds):
        """格式化时间"""
        minutes, seconds = divmod(seconds, 60)
        hours, minutes = divmod(minutes, 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

class RecordingItem(QListWidgetItem):
    """录制列表项"""
    
    def __init__(self, file_path, record_time, record_date):
        super().__init__()
        self.file_path = file_path
        self.record_time = record_time
        self.record_date = record_date
        
        # 设置显示文本
        file_name = os.path.basename(file_path)
        self.setText(file_name)
        
        # 设置提示文本
        self.setToolTip(f"文件: {file_name}\n"
                        f"时长: {record_time}\n"
                        f"日期: {record_date}")

class RecordingPanel(QWidget):
    """录制面板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.group_id = None
        self.mode = "teaching"
        self.recording_thread = None
        self.recordings = []
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 创建主布局
        layout = QVBoxLayout(self)
        
        # 创建控制面板
        control_panel = QFrame()
        control_panel.setFrameShape(QFrame.StyledPanel)
        control_layout = QVBoxLayout(control_panel)
        
        # 添加录制设置
        settings_layout = QHBoxLayout()
        
        # 添加录制类型选择
        type_label = QLabel("录制类型:")
        settings_layout.addWidget(type_label)
        
        self.type_combo = QComboBox()
        self.type_combo.addItem("屏幕录制", "screen")
        self.type_combo.addItem("摄像头录制", "camera")
        settings_layout.addWidget(self.type_combo)
        
        # 添加分辨率选择
        resolution_label = QLabel("分辨率:")
        settings_layout.addWidget(resolution_label)
        
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItem("1920x1080", "1920x1080")
        self.resolution_combo.addItem("1280x720", "1280x720")
        self.resolution_combo.addItem("800x600", "800x600")
        settings_layout.addWidget(self.resolution_combo)
        
        # 添加帧率选择
        fps_label = QLabel("帧率:")
        settings_layout.addWidget(fps_label)
        
        self.fps_spin = QSpinBox()
        self.fps_spin.setRange(15, 60)
        self.fps_spin.setValue(30)
        settings_layout.addWidget(self.fps_spin)
        
        # 添加设置布局到控制面板
        control_layout.addLayout(settings_layout)
        
        # 添加录制控制
        control_buttons = QHBoxLayout()
        
        self.start_btn = QPushButton("开始录制")
        self.start_btn.clicked.connect(self.start_recording)
        control_buttons.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止录制")
        self.stop_btn.clicked.connect(self.stop_recording)
        self.stop_btn.setEnabled(False)
        control_buttons.addWidget(self.stop_btn)
        
        self.upload_btn = QPushButton("上传录制")
        self.upload_btn.clicked.connect(self.upload_recording)
        control_buttons.addWidget(self.upload_btn)
        
        # 添加控制按钮到控制面板
        control_layout.addLayout(control_buttons)
        
        # 添加录制状态
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        self.time_label = QLabel("00:00:00")
        status_layout.addWidget(self.time_label)
        
        # 添加状态布局到控制面板
        control_layout.addLayout(status_layout)
        
        # 添加控制面板到主布局
        layout.addWidget(control_panel)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        
        # 创建录制列表
        self.recording_list = QListWidget()
        self.recording_list.setSelectionMode(QListWidget.SingleSelection)
        self.recording_list.itemDoubleClicked.connect(self.play_recording)
        self.recording_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.recording_list.customContextMenuRequested.connect(self.show_context_menu)
        
        # 添加录制列表到分割器
        splitter.addWidget(self.recording_list)
        
        # 创建播放器
        player_widget = QWidget()
        player_layout = QVBoxLayout(player_widget)
        
        self.video_widget = QVideoWidget()
        player_layout.addWidget(self.video_widget)
        
        self.media_player = QMediaPlayer(self)
        self.media_player.setVideoOutput(self.video_widget)
        
        player_controls = QHBoxLayout()
        
        self.play_btn = QPushButton("播放")
        self.play_btn.clicked.connect(self.toggle_playback)
        player_controls.addWidget(self.play_btn)
        
        self.stop_play_btn = QPushButton("停止")
        self.stop_play_btn.clicked.connect(self.stop_playback)
        player_controls.addWidget(self.stop_play_btn)
        
        player_layout.addLayout(player_controls)
        
        # 添加播放器到分割器
        splitter.addWidget(player_widget)
        
        # 设置分割器初始大小
        splitter.setSizes([200, 300])
        
        # 添加分割器到主布局
        layout.addWidget(splitter)
        
        # 加载录制列表
        self.load_recordings()
    
    def on_group_joined(self, group_id):
        """处理加入小组事件"""
        self.group_id = group_id
    
    def on_mode_changed(self, mode):
        """处理模式切换事件"""
        self.mode = mode
        logger.info(f"录制面板切换到模式: {mode}")
    
    def refresh(self):
        """刷新录制列表"""
        self.load_recordings()
    
    def load_recordings(self):
        """加载录制列表"""
        try:
            # 清空列表
            self.recording_list.clear()
            self.recordings = []
            
            # 获取录制目录
            recordings_dir = os.path.join(os.path.expanduser("~"), "smart_classroom_recordings")
            
            # 检查目录是否存在
            if not os.path.exists(recordings_dir):
                os.makedirs(recordings_dir)
                return
            
            # 遍历目录
            for file_name in os.listdir(recordings_dir):
                if file_name.endswith((".mp4", ".avi", ".mkv")):
                    file_path = os.path.join(recordings_dir, file_name)
                    
                    # 获取文件信息
                    file_stat = os.stat(file_path)
                    file_size = file_stat.st_size
                    file_mtime = datetime.fromtimestamp(file_stat.st_mtime)
                    
                    # 获取视频时长
                    duration = self.get_video_duration(file_path)
                    
                    # 添加到列表
                    item = RecordingItem(
                        file_path,
                        duration,
                        file_mtime.strftime("%Y-%m-%d %H:%M:%S")
                    )
                    self.recording_list.addItem(item)
                    self.recordings.append({
                        'path': file_path,
                        'name': file_name,
                        'size': file_size,
                        'date': file_mtime.strftime("%Y-%m-%d %H:%M:%S"),
                        'duration': duration
                    })
        
        except Exception as e:
            self.parent.show_error(f"加载录制列表时出错: {str(e)}")
    
    def get_video_duration(self, file_path):
        """获取视频时长"""
        try:
            # 使用FFmpeg获取视频时长
            command = [
                "ffprobe",
                "-v", "error",
                "-show_entries", "format=duration",
                "-of", "default=noprint_wrappers=1:nokey=1",
                file_path
            ]
            
            result = subprocess.run(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            if result.returncode == 0:
                duration_seconds = float(result.stdout.strip())
                return self.format_time(int(duration_seconds))
            else:
                return "未知"
        
        except Exception as e:
            logger.error(f"获取视频时长时出错: {str(e)}")
            return "未知"
    
    def format_time(self, seconds):
        """格式化时间"""
        minutes, seconds = divmod(seconds, 60)
        hours, minutes = divmod(minutes, 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def start_recording(self):
        """开始录制"""
        try:
            # 获取录制设置
            record_type = self.type_combo.currentData()
            resolution = self.resolution_combo.currentData()
            fps = self.fps_spin.value()
            
            # 准备输出路径
            recordings_dir = os.path.join(os.path.expanduser("~"), "smart_classroom_recordings")
            os.makedirs(recordings_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            group_suffix = f"_group_{self.group_id}" if self.group_id else ""
            output_path = os.path.join(recordings_dir, f"recording_{timestamp}{group_suffix}.mp4")
            
            # 创建录制线程
            if record_type == "camera":
                # 摄像头录制
                self.recording_thread = RecordingThread(
                    output_path,
                    device="/dev/video0",  # 默认摄像头设备
                    resolution=resolution,
                    fps=fps
                )
            else:
                # 屏幕录制
                self.recording_thread = RecordingThread(
                    output_path,
                    device=None,
                    resolution=resolution,
                    fps=fps
                )
            
            # 连接信号
            self.recording_thread.update_signal.connect(self.update_recording_status)
            self.recording_thread.finished_signal.connect(self.handle_recording_finished)
            
            # 启动线程
            self.recording_thread.start()
            
            # 更新UI
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.status_label.setText("正在录制...")
            
            logger.info(f"开始录制: {output_path}")
        
        except Exception as e:
            self.parent.show_error(f"开始录制时出错: {str(e)}")
    
    def stop_recording(self):
        """停止录制"""
        if self.recording_thread and self.recording_thread.isRunning():
            # 停止录制线程
            self.recording_thread.stop()
            
            # 更新UI
            self.status_label.setText("正在停止录制...")
            self.stop_btn.setEnabled(False)
            
            logger.info("停止录制")
    
    def update_recording_status(self, elapsed, time_str):
        """更新录制状态"""
        self.time_label.setText(time_str)
    
    def handle_recording_finished(self, success, message, file_path):
        """处理录制完成"""
        # 更新UI
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.time_label.setText("00:00:00")
        
        if success:
            self.status_label.setText(message)
            logger.info(f"录制完成: {file_path}")
            
            # 刷新录制列表
            self.load_recordings()
        else:
            self.status_label.setText(f"录制失败: {message}")
            logger.error(f"录制失败: {message}")
    
    def play_recording(self, item):
        """播放录制"""
        file_path = item.file_path
        
        # 设置媒体源
        self.media_player.setMedia(QMediaContent(QUrl.fromLocalFile(file_path)))
        
        # 开始播放
        self.media_player.play()
        
        # 更新UI
        self.play_btn.setText("暂停")
        self.status_label.setText(f"正在播放: {os.path.basename(file_path)}")
    
    def toggle_playback(self):
        """切换播放/暂停"""
        if self.media_player.state() == QMediaPlayer.PlayingState:
            self.media_player.pause()
            self.play_btn.setText("播放")
        else:
            self.media_player.play()
            self.play_btn.setText("暂停")
    
    def stop_playback(self):
        """停止播放"""
        self.media_player.stop()
        self.play_btn.setText("播放")
    
    def upload_recording(self):
        """上传录制"""
        # 获取选中的项
        selected_items = self.recording_list.selectedItems()
        
        if not selected_items:
            self.parent.show_error("请先选择要上传的录制")
            return
        
        if not self.group_id:
            self.parent.show_error("请先加入小组")
            return
        
        # 获取选中的录制
        item = selected_items[0]
        file_path = item.file_path
        file_name = os.path.basename(file_path)
        
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 准备元数据
            metadata = {
                'name': file_name,
                'type': "视频",
                'group_id': self.group_id,
                'uploader_id': self.parent.device_id,
                'uploader_name': self.parent.device_discovery.device_name
            }
            
            # 创建进度对话框
            progress_dialog = QMessageBox(self)
            progress_dialog.setWindowTitle("上传录制")
            progress_dialog.setText(f"正在上传: {file_name}")
            
            progress_bar = QProgressBar(progress_dialog)
            progress_bar.setRange(0, 100)
            progress_bar.setValue(0)
            
            progress_dialog.setStandardButtons(QMessageBox.Cancel)
            progress_dialog.setDefaultButton(QMessageBox.Cancel)
            
            # 创建上传线程
            from group_client.ui.file_panel import FileUploadThread
            
            upload_thread = FileUploadThread(
                f"{api_base_url}/files/upload",
                file_path,
                metadata
            )
            
            # 连接信号
            upload_thread.progress_signal.connect(progress_bar.setValue)
            upload_thread.finished_signal.connect(
                lambda success, message, file_info: self.handle_upload_finished(
                    success, message, file_info, progress_dialog, upload_thread
                )
            )
            
            # 启动线程
            upload_thread.start()
            
            # 显示进度对话框
            if progress_dialog.exec_() == QMessageBox.Cancel:
                # 取消上传
                upload_thread.stop()
        
        except Exception as e:
            self.parent.show_error(f"上传录制时出错: {str(e)}")
    
    def handle_upload_finished(self, success, message, file_info, dialog, thread):
        """处理上传完成"""
        # 关闭对话框
        dialog.accept()
        
        if success:
            self.status_label.setText(f"上传成功: {file_info.get('name')}")
            logger.info(f"录制上传成功: {file_info.get('name')}")
        else:
            self.parent.show_error(f"上传录制失败: {message}")
    
    def show_context_menu(self, position):
        """显示上下文菜单"""
        # 获取选中的项
        selected_items = self.recording_list.selectedItems()
        
        if not selected_items:
            return
        
        # 创建菜单
        menu = QMenu(self)
        
        # 添加播放操作
        play_action = QAction("播放", self)
        play_action.triggered.connect(lambda: self.play_recording(selected_items[0]))
        menu.addAction(play_action)
        
        # 添加上传操作
        upload_action = QAction("上传", self)
        upload_action.triggered.connect(self.upload_recording)
        menu.addAction(upload_action)
        
        # 添加删除操作
        delete_action = QAction("删除", self)
        delete_action.triggered.connect(lambda: self.delete_recording(selected_items[0]))
        menu.addAction(delete_action)
        
        # 显示菜单
        menu.exec_(self.recording_list.mapToGlobal(position))
    
    def delete_recording(self, item):
        """删除录制"""
        # 确认对话框
        reply = QMessageBox.question(
            self, "确认删除", f"确定要删除录制 {os.path.basename(item.file_path)} 吗？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # 停止播放
                self.media_player.stop()
                
                # 删除文件
                os.remove(item.file_path)
                
                # 刷新列表
                self.load_recordings()
                
                self.status_label.setText(f"已删除: {os.path.basename(item.file_path)}")
                logger.info(f"已删除录制: {item.file_path}")
            
            except Exception as e:
                self.parent.show_error(f"删除录制时出错: {str(e)}")
    
    def closeEvent(self, event):
        """关闭事件处理"""
        # 停止录制
        if self.recording_thread and self.recording_thread.isRunning():
            self.recording_thread.stop()
        
        # 停止播放
        self.media_player.stop()
        
        event.accept()