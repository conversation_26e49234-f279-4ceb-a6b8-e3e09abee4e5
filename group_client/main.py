#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 小组端主程序
"""

import sys
import os
import logging
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap
from group_client.ui.main_window import MainWindow

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.expanduser("~"), "smart_classroom_group_client.log"))
    ]
)
logger = logging.getLogger('group_client_main')

def main():
    """小组端应用入口"""
    # 创建应用
    app = QApplication(sys.argv)
    app.setApplicationName("智慧课堂系统 - 小组端")
    app.setOrganizationName("智慧课堂")
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    
    # 显示启动画面
    splash_pixmap = QPixmap(os.path.join(os.path.dirname(__file__), "../backend/static/images/splash.png"))
    if not splash_pixmap.isNull():
        splash = QSplashScreen(splash_pixmap)
        splash.show()
        app.processEvents()
    else:
        splash = None
    
    # 创建主窗口
    window = MainWindow()
    
    # 延迟显示主窗口
    def show_main_window():
        window.show()
        if splash:
            splash.finish(window)
    
    QTimer.singleShot(1000, show_main_window)
    
    # 记录启动日志
    logger.info("小组端应用已启动")
    
    # 运行应用
    return app.exec_()

if __name__ == '__main__':
    try:
        sys.exit(main())
    except Exception as e:
        logger.critical(f"应用崩溃: {str(e)}", exc_info=True)
        raise