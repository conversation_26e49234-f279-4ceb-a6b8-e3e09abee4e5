# 智慧课堂系统依赖包

# PyQt5 - GUI框架
PyQt5>=5.15.0
PyQt5-sip>=12.0.0

# Flask - Web框架
Flask>=2.3.0
Flask-SQLAlchemy>=3.0.0
Flask-SocketIO>=5.3.0

# WebSocket支持
python-socketio>=5.8.0
python-engineio>=4.7.0

# 数据库
SQLAlchemy>=2.0.0

# 视频处理
python-vlc>=3.0.0
opencv-python>=4.8.0

# 网络通信
requests>=2.31.0
websocket-client>=1.6.0

# 工具库
Pillow>=9.0.0
numpy>=1.24.0
qrcode>=7.4.0

# 开发工具
pytest>=7.4.0
pytest-qt>=4.2.0
black>=23.9.0
flake8>=6.1.0

# 系统工具和监控
psutil>=5.9.0
PyYAML>=6.0.0

# 系统集成和性能优化新增依赖
# 用于系统监控和性能分析

# 错误处理和异常恢复增强新增依赖
schedule>=1.2.0  # 任务调度
dataclasses>=0.6  # 数据类支持（Python 3.7+已内置）

# 安全性和权限管理增强新增依赖
cryptography>=41.0.0  # 数据加密
PyJWT>=2.8.0  # JWT令牌处理
bcrypt>=4.0.0  # 密码哈希
passlib>=1.7.0  # 密码处理工具