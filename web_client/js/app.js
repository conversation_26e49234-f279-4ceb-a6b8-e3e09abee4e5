/**
 * 智慧课堂系统 - 学生端主应用
 */

class SmartClassroomClient {
    constructor() {
        // 状态变量
        this.socket = null;
        this.classroomId = null;
        this.studentId = null;
        this.studentName = null;
        this.deviceInfo = Utils.getDeviceInfo();
        this.currentQuestion = null;
        this.selectedAnswer = null;
        this.isSharing = false;
        this.shareType = null;
        this.mediaStream = null;
        
        // 初始化应用
        this.init();
    }
    
    /**
     * 初始化应用
     */
    init() {
        // 初始化WebSocket连接
        this.initSocket();
        
        // 绑定事件监听器
        this.bindEvents();
        
        // 初始化二维码
        this.initQRCode();
        
        // 检查是否已登录
        this.checkLoginStatus();
    }
    
    /**
     * 初始化WebSocket连接
     */
    initSocket() {
        // 连接到后端WebSocket服务
        this.socket = io('http://localhost:5000');
        
        this.socket.on('connect', () => {
            console.log('已连接到服务器');
        });
        
        this.socket.on('disconnect', () => {
            console.log('与服务器断开连接');
            Utils.showNotification('与服务器的连接已断开，正在尝试重新连接...', 'warning');
        });
        
        // 监听课堂事件
        this.socket.on('classroom_joined', (data) => {
            this.onClassroomJoined(data);
        });
        
        this.socket.on('new_question', (data) => {
            this.onNewQuestion(data);
        });
        
        this.socket.on('question_results', (data) => {
            this.onQuestionResults(data);
        });
        
        this.socket.on('new_file', (data) => {
            this.onNewFile(data);
        });
        
        this.socket.on('classroom_activity', (data) => {
            this.onClassroomActivity(data);
        });
        
        this.socket.on('error', (error) => {
            console.error('连接错误:', error);
            Utils.showNotification('连接失败，请检查网络连接', 'error');
        });
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 登录页面事件
        const joinBtn = document.getElementById('join-btn');
        const classroomCodeInput = document.getElementById('classroom-code');
        const studentNameInput = document.getElementById('student-name');
        
        joinBtn.addEventListener('click', () => {
            const code = classroomCodeInput.value.trim();
            const name = studentNameInput.value.trim();
            
            if (!code) {
                Utils.showNotification('请输入课堂码', 'warning');
                return;
            }
            
            if (!name) {
                Utils.showNotification('请输入您的姓名', 'warning');
                return;
            }
            
            this.joinClassroom(code, name);
        });
        
        // 支持回车键提交
        classroomCodeInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                studentNameInput.focus();
            }
        });
        
        studentNameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                joinBtn.click();
            }
        });
        
        // 课堂页面事件
        const leaveBtn = document.getElementById('leave-btn');
        leaveBtn.addEventListener('click', () => {
            this.leaveClassroom();
        });
        
        // 标签切换事件
        const tabButtons = document.querySelectorAll('.tab-button');
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.getAttribute('data-tab');
                this.switchTab(tabId);
            });
        });
        
        // 答题相关事件
        const submitAnswerBtn = document.getElementById('submit-answer');
        submitAnswerBtn.addEventListener('click', () => {
            this.submitAnswer();
        });
        
        // 投屏相关事件
        const shareScreenBtn = document.getElementById('share-screen');
        shareScreenBtn.addEventListener('click', () => {
            this.prepareScreenShare();
        });
        
        const shareFileBtn = document.getElementById('share-file');
        shareFileBtn.addEventListener('click', () => {
            this.prepareFileShare();
        });
        
        const shareCameraBtn = document.getElementById('share-camera');
        shareCameraBtn.addEventListener('click', () => {
            this.prepareCameraShare();
        });
        
        const startSharingBtn = document.getElementById('start-sharing');
        startSharingBtn.addEventListener('click', () => {
            this.startSharing();
        });
        
        const cancelSharingBtn = document.getElementById('cancel-sharing');
        cancelSharingBtn.addEventListener('click', () => {
            this.cancelSharing();
        });
        
        const stopSharingBtn = document.getElementById('stop-sharing');
        stopSharingBtn.addEventListener('click', () => {
            this.stopSharing();
        });
        
        const uploadFileBtn = document.getElementById('upload-file');
        uploadFileBtn.addEventListener('click', () => {
            this.uploadAndShareFile();
        });
        
        const cancelUploadBtn = document.getElementById('cancel-upload');
        cancelUploadBtn.addEventListener('click', () => {
            this.cancelFileUpload();
        });
        
        // 资料筛选事件
        const filterItems = document.querySelectorAll('.filter-item');
        filterItems.forEach(item => {
            item.addEventListener('click', () => {
                const filter = item.getAttribute('data-filter');
                this.filterMaterials(filter);
            });
        });
        
        // 资料搜索事件
        const searchInput = document.getElementById('search-materials');
        searchInput.addEventListener('input', Utils.debounce(() => {
            this.searchMaterials(searchInput.value);
        }, 300));
    }
    
    /**
     * 初始化二维码
     */
    initQRCode() {
        // 生成示例二维码
        const qrcodeElement = document.getElementById('qrcode');
        
        if (qrcodeElement) {
            QRCode.toCanvas(qrcodeElement, 'https://example.com/join', {
                width: 180,
                margin: 1,
                color: {
                    dark: '#2c3e50',
                    light: '#ffffff'
                }
            }, (error) => {
                if (error) console.error('二维码生成失败:', error);
            });
        }
    }
    
    /**
     * 检查登录状态
     */
    checkLoginStatus() {
        // 从本地存储中获取登录信息
        const savedClassroomId = localStorage.getItem('classroom_id');
        const savedStudentId = localStorage.getItem('student_id');
        const savedStudentName = localStorage.getItem('student_name');
        
        if (savedClassroomId && savedStudentId && savedStudentName) {
            // 尝试恢复会话
            this.classroomId = savedClassroomId;
            this.studentId = savedStudentId;
            this.studentName = savedStudentName;
            
            // 重新连接到课堂
            this.socket.emit('rejoin_classroom', {
                classroom_id: this.classroomId,
                student_id: this.studentId,
                student_name: this.studentName
            });
            
            // 显示课堂界面
            this.showClassroomInterface();
            
            // 加载课堂数据
            this.loadClassroomData();
        }
    }
    
    /**
     * 加入课堂
     * @param {string} classroomCode - 课堂码
     * @param {string} studentName - 学生姓名
     */
    joinClassroom(classroomCode, studentName) {
        // 显示加载状态
        document.getElementById('join-btn').textContent = '正在加入...';
        document.getElementById('join-btn').disabled = true;
        
        // 发送加入课堂请求
        api.joinClassroom(classroomCode, studentName, this.deviceInfo)
            .then(response => {
                if (response.success) {
                    // 保存课堂信息
                    this.classroomId = response.data.classroom_id;
                    this.studentId = response.data.id;
                    this.studentName = studentName;
                    
                    // 保存到本地存储
                    localStorage.setItem('classroom_id', this.classroomId);
                    localStorage.setItem('student_id', this.studentId);
                    localStorage.setItem('student_name', this.studentName);
                    
                    // 通过WebSocket加入课堂
                    this.socket.emit('join_classroom', {
                        classroom_id: this.classroomId,
                        student_id: this.studentId,
                        student_name: this.studentName
                    });
                    
                    // 显示课堂界面
                    this.showClassroomInterface();
                    
                    // 加载课堂数据
                    this.loadClassroomData();
                    
                    Utils.showNotification('成功加入课堂', 'success');
                } else {
                    Utils.showNotification(response.message || '加入课堂失败', 'error');
                }
            })
            .catch(error => {
                console.error('加入课堂错误:', error);
                Utils.showNotification('加入课堂失败: ' + error.message, 'error');
            })
            .finally(() => {
                // 恢复按钮状态
                document.getElementById('join-btn').textContent = '加入课堂';
                document.getElementById('join-btn').disabled = false;
            });
    }
    
    /**
     * 离开课堂
     */
    leaveClassroom() {
        if (confirm('确定要退出当前课堂吗？')) {
            // 停止所有共享
            if (this.isSharing) {
                this.stopSharing();
            }
            
            // 通知服务器离开课堂
            this.socket.emit('leave_classroom', {
                classroom_id: this.classroomId,
                student_id: this.studentId
            });
            
            // 清除本地存储
            localStorage.removeItem('classroom_id');
            localStorage.removeItem('student_id');
            localStorage.removeItem('student_name');
            
            // 重置状态
            this.classroomId = null;
            this.studentId = null;
            this.studentName = null;
            
            // 显示登录界面
            this.showLoginInterface();
            
            Utils.showNotification('已退出课堂', 'success');
        }
    }
    
    /**
     * 处理课堂加入事件
     * @param {Object} data - 课堂数据
     */
    onClassroomJoined(data) {
        console.log('成功加入课堂:', data);
        
        // 更新课堂信息
        document.getElementById('classroom-name').textContent = data.classroom_name || '课堂';
        document.getElementById('student-info').textContent = this.studentName;
    }
    
    /**
     * 显示课堂界面
     */
    showClassroomInterface() {
        document.getElementById('login-page').style.display = 'none';
        document.getElementById('classroom-page').style.display = 'block';
        
        // 更新课堂信息
        document.getElementById('classroom-name').textContent = '课堂';
        document.getElementById('student-info').textContent = this.studentName;
    }
    
    /**
     * 显示登录界面
     */
    showLoginInterface() {
        document.getElementById('login-page').style.display = 'block';
        document.getElementById('classroom-page').style.display = 'none';
    }
    
    /**
     * 切换标签页
     * @param {string} tabId - 标签ID
     */
    switchTab(tabId) {
        // 更新标签按钮状态
        const tabButtons = document.querySelectorAll('.tab-button');
        tabButtons.forEach(button => {
            if (button.getAttribute('data-tab') === tabId) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
        
        // 更新标签内容显示
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            if (content.id === tabId) {
                content.classList.add('active');
            } else {
                content.classList.remove('active');
            }
        });
        
        // 根据标签加载相应数据
        if (tabId === 'materials-tab') {
            this.loadMaterials();
        } else if (tabId === 'activity-tab') {
            this.loadActivities();
        }
    }
    
    /**
     * 加载课堂数据
     */
    loadClassroomData() {
        // 加载课堂状态
        api.getClassroomStatus(this.classroomId)
            .then(response => {
                if (response.success) {
                    console.log('课堂状态:', response.data);
                }
            })
            .catch(error => {
                console.error('获取课堂状态错误:', error);
            });
        
        // 加载当前题目
        this.loadCurrentQuestions();
        
        // 加载课堂资料
        this.loadMaterials();
        
        // 加载课堂动态
        this.loadActivities();
    }
    
    /**
     * 加载当前题目
     */
    loadCurrentQuestions() {
        api.getQuestions(this.classroomId)
            .then(response => {
                if (response.success && response.questions && response.questions.length > 0) {
                    // 获取最新的题目
                    const latestQuestion = response.questions[response.questions.length - 1];
                    
                    // 加载题目详情
                    this.loadQuestionDetails(latestQuestion.id);
                } else {
                    // 没有进行中的题目
                    document.getElementById('no-questions').style.display = 'block';
                    document.getElementById('question-area').style.display = 'none';
                }
            })
            .catch(error => {
                console.error('获取题目列表错误:', error);
                document.getElementById('no-questions').style.display = 'block';
                document.getElementById('question-area').style.display = 'none';
            });
    }
    
    /**
     * 加载题目详情
     * @param {string} questionId - 题目ID
     */
    loadQuestionDetails(questionId) {
        api.getQuestionDetails(questionId)
            .then(response => {
                if (response.success) {
                    this.displayQuestion(response.question);
                }
            })
            .catch(error => {
                console.error('获取题目详情错误:', error);
            });
    }
    
    /**
     * 显示题目
     * @param {Object} question - 题目数据
     */
    displayQuestion(question) {
        this.currentQuestion = question;
        this.selectedAnswer = null;
        
        // 显示题目区域
        document.getElementById('no-questions').style.display = 'none';
        document.getElementById('question-area').style.display = 'block';
        
        // 更新题目信息
        document.getElementById('question-type').textContent = this.getQuestionTypeText(question.question_type);
        document.getElementById('question-content').textContent = question.content;
        
        // 生成选项
        const optionsList = document.getElementById('options-list');
        optionsList.innerHTML = '';
        
        if (question.options && question.options.length > 0) {
            question.options.forEach((option, index) => {
                const optionItem = document.createElement('li');
                optionItem.className = 'option-item';
                optionItem.textContent = option;
                optionItem.dataset.value = option;
                
                optionItem.addEventListener('click', () => {
                    this.selectOption(optionItem, question.question_type);
                });
                
                optionsList.appendChild(optionItem);
            });
        } else if (question.question_type === 'text') {
            // 文本输入题
            const textInput = document.createElement('textarea');
            textInput.className = 'text-answer';
            textInput.placeholder = '请输入您的答案...';
            textInput.rows = 4;
            
            textInput.addEventListener('input', () => {
                this.selectedAnswer = textInput.value;
            });
            
            optionsList.appendChild(textInput);
        } else if (question.question_type === 'true_false') {
            // 判断题
            const trueOption = document.createElement('li');
            trueOption.className = 'option-item';
            trueOption.textContent = '正确';
            trueOption.dataset.value = 'true';
            
            const falseOption = document.createElement('li');
            falseOption.className = 'option-item';
            falseOption.textContent = '错误';
            falseOption.dataset.value = 'false';
            
            trueOption.addEventListener('click', () => {
                this.selectOption(trueOption, 'true_false');
            });
            
            falseOption.addEventListener('click', () => {
                this.selectOption(falseOption, 'true_false');
            });
            
            optionsList.appendChild(trueOption);
            optionsList.appendChild(falseOption);
        }
    }
    
    /**
     * 选择选项
     * @param {HTMLElement} optionElement - 选项元素
     * @param {string} questionType - 题目类型
     */
    selectOption(optionElement, questionType) {
        const optionsList = document.getElementById('options-list');
        
        if (questionType === 'single_choice' || questionType === 'true_false') {
            // 单选题或判断题
            const options = optionsList.querySelectorAll('.option-item');
            options.forEach(option => {
                option.classList.remove('selected');
            });
            
            optionElement.classList.add('selected');
            this.selectedAnswer = optionElement.dataset.value;
        } else if (questionType === 'multiple_choice') {
            // 多选题
            optionElement.classList.toggle('selected');
            
            // 收集所有选中的选项
            const selectedOptions = [];
            const options = optionsList.querySelectorAll('.option-item.selected');
            options.forEach(option => {
                selectedOptions.push(option.dataset.value);
            });
            
            this.selectedAnswer = selectedOptions;
        }
    }
    
    /**
     * 提交答案
     */
    submitAnswer() {
        if (!this.currentQuestion || !this.selectedAnswer) {
            Utils.showNotification('请选择或输入答案', 'warning');
            return;
        }
        
        // 显示提交中状态
        const submitBtn = document.getElementById('submit-answer');
        submitBtn.textContent = '提交中...';
        submitBtn.disabled = true;
        
        // 提交答案
        api.submitAnswer(this.currentQuestion.id, this.studentId, this.selectedAnswer)
            .then(response => {
                if (response.success) {
                    Utils.showNotification('答案提交成功', 'success');
                    
                    // 禁用选项
                    const options = document.querySelectorAll('.option-item');
                    options.forEach(option => {
                        option.style.pointerEvents = 'none';
                    });
                    
                    // 禁用文本输入
                    const textInput = document.querySelector('.text-answer');
                    if (textInput) {
                        textInput.disabled = true;
                    }
                    
                    // 更新按钮状态
                    submitBtn.textContent = '已提交';
                    submitBtn.disabled = true;
                } else {
                    Utils.showNotification(response.message || '答案提交失败', 'error');
                    submitBtn.textContent = '提交答案';
                    submitBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('提交答案错误:', error);
                Utils.showNotification('答案提交失败: ' + error.message, 'error');
                submitBtn.textContent = '提交答案';
                submitBtn.disabled = false;
            });
    }
    
    /**
     * 处理新题目事件
     * @param {Object} data - 题目数据
     */
    onNewQuestion(data) {
        // 切换到答题标签
        this.switchTab('answer-tab');
        
        // 加载题目详情
        this.loadQuestionDetails(data.question_id);
        
        // 显示通知
        Utils.showNotification('收到新题目，请及时作答', 'info');
    }
    
    /**
     * 处理题目结果事件
     * @param {Object} data - 结果数据
     */
    onQuestionResults(data) {
        console.log('题目结果:', data);
        // 可以在这里显示答题结果
    }
    
    /**
     * 准备屏幕共享
     */
    async prepareScreenShare() {
        try {
            // 请求屏幕共享权限
            const stream = await navigator.mediaDevices.getDisplayMedia({
                video: {
                    cursor: 'always'
                },
                audio: false
            });
            
            // 保存媒体流
            this.mediaStream = stream;
            this.shareType = 'screen';
            
            // 显示预览
            const previewVideo = document.getElementById('preview-video');
            previewVideo.srcObject = stream;
            
            // 显示预览容器
            document.getElementById('screen-preview-container').style.display = 'block';
            document.getElementById('file-preview').style.display = 'none';
            document.getElementById('preview-video').style.display = 'block';
            document.getElementById('file-upload-container').style.display = 'none';
            
            // 监听流结束事件
            stream.getVideoTracks()[0].addEventListener('ended', () => {
                this.cancelSharing();
            });
        } catch (error) {
            console.error('屏幕共享错误:', error);
            Utils.showNotification('无法启动屏幕共享: ' + error.message, 'error');
        }
    }
    
    /**
     * 准备文件共享
     */
    prepareFileShare() {
        // 显示文件上传容器
        document.getElementById('file-upload-container').style.display = 'block';
        document.getElementById('screen-preview-container').style.display = 'none';
        
        // 监听文件选择
        const fileInput = document.getElementById('file-input');
        fileInput.value = '';
        
        fileInput.addEventListener('change', () => {
            if (fileInput.files && fileInput.files[0]) {
                const file = fileInput.files[0];
                
                // 显示文件预览
                const filePreview = document.getElementById('file-preview');
                filePreview.innerHTML = '';
                
                if (file.type.startsWith('image/')) {
                    // 图片预览
                    const img = document.createElement('img');
                    img.src = URL.createObjectURL(file);
                    img.style.maxWidth = '100%';
                    img.style.maxHeight = '100%';
                    filePreview.appendChild(img);
                } else {
                    // 其他文件类型
                    const fileInfo = document.createElement('div');
                    fileInfo.className = 'file-info';
                    fileInfo.innerHTML = `
                        <i class="icon">${Utils.getFileTypeIcon(file.name.split('.').pop())}</i>
                        <p>${file.name}</p>
                        <p>${Utils.formatFileSize(file.size)}</p>
                    `;
                    filePreview.appendChild(fileInfo);
                }
                
                // 显示预览容器
                document.getElementById('screen-preview-container').style.display = 'block';
                document.getElementById('file-preview').style.display = 'block';
                document.getElementById('preview-video').style.display = 'none';
            }
        });
        
        // 触发文件选择对话框
        fileInput.click();
    }
    
    /**
     * 准备摄像头共享
     */
    async prepareCameraShare() {
        try {
            // 请求摄像头权限
            const stream = await navigator.mediaDevices.getUserMedia({
                video: true,
                audio: false
            });
            
            // 保存媒体流
            this.mediaStream = stream;
            this.shareType = 'camera';
            
            // 显示预览
            const previewVideo = document.getElementById('preview-video');
            previewVideo.srcObject = stream;
            
            // 显示预览容器
            document.getElementById('screen-preview-container').style.display = 'block';
            document.getElementById('file-preview').style.display = 'none';
            document.getElementById('preview-video').style.display = 'block';
            document.getElementById('file-upload-container').style.display = 'none';
        } catch (error) {
            console.error('摄像头共享错误:', error);
            Utils.showNotification('无法启动摄像头: ' + error.message, 'error');
        }
    }
    
    /**
     * 开始共享
     */
    startSharing() {
        if (this.shareType === 'screen' || this.shareType === 'camera') {
            // 通知服务器开始共享
            this.socket.emit('start_sharing', {
                classroom_id: this.classroomId,
                student_id: this.studentId,
                student_name: this.studentName,
                share_type: this.shareType
            });
            
            // 更新UI
            document.getElementById('screen-preview-container').style.display = 'none';
            document.getElementById('sharing-status').style.display = 'block';
            document.getElementById('sharing-type-info').textContent = 
                this.shareType === 'screen' ? '屏幕内容' : '摄像头画面';
            
            // 设置共享状态
            this.isSharing = true;
            
            Utils.showNotification('开始分享', 'success');
        }
    }
    
    /**
     * 取消共享
     */
    cancelSharing() {
        // 停止媒体流
        if (this.mediaStream) {
            this.mediaStream.getTracks().forEach(track => track.stop());
            this.mediaStream = null;
        }
        
        // 重置UI
        document.getElementById('screen-preview-container').style.display = 'none';
        document.getElementById('file-upload-container').style.display = 'none';
        
        // 重置状态
        this.shareType = null;
    }
    
    /**
     * 停止共享
     */
    stopSharing() {
        // 通知服务器停止共享
        this.socket.emit('stop_sharing', {
            classroom_id: this.classroomId,
            student_id: this.studentId
        });
        
        // 停止媒体流
        if (this.mediaStream) {
            this.mediaStream.getTracks().forEach(track => track.stop());
            this.mediaStream = null;
        }
        
        // 更新UI
        document.getElementById('sharing-status').style.display = 'none';
        
        // 重置状态
        this.isSharing = false;
        this.shareType = null;
        
        Utils.showNotification('已停止分享', 'info');
    }
    
    /**
     * 上传并分享文件
     */
    uploadAndShareFile() {
        const fileInput = document.getElementById('file-input');
        
        if (!fileInput.files || !fileInput.files[0]) {
            Utils.showNotification('请选择要分享的文件', 'warning');
            return;
        }
        
        const file = fileInput.files[0];
        
        // 显示上传中状态
        const uploadBtn = document.getElementById('upload-file');
        uploadBtn.textContent = '上传中...';
        uploadBtn.disabled = true;
        
        // 上传文件
        api.uploadFile(file, this.studentId, this.classroomId)
            .then(response => {
                if (response.success) {
                    // 通知服务器分享文件
                    this.socket.emit('share_file', {
                        classroom_id: this.classroomId,
                        student_id: this.studentId,
                        student_name: this.studentName,
                        file_id: response.file.id,
                        file_name: response.file.original_filename
                    });
                    
                    // 更新UI
                    document.getElementById('file-upload-container').style.display = 'none';
                    document.getElementById('screen-preview-container').style.display = 'none';
                    
                    Utils.showNotification('文件分享成功', 'success');
                } else {
                    Utils.showNotification(response.message || '文件上传失败', 'error');
                }
            })
            .catch(error => {
                console.error('文件上传错误:', error);
                Utils.showNotification('文件上传失败: ' + error.message, 'error');
            })
            .finally(() => {
                // 恢复按钮状态
                uploadBtn.textContent = '上传并分享';
                uploadBtn.disabled = false;
            });
    }
    
    /**
     * 取消文件上传
     */
    cancelFileUpload() {
        document.getElementById('file-upload-container').style.display = 'none';
        document.getElementById('screen-preview-container').style.display = 'none';
        document.getElementById('file-input').value = '';
    }
    
    /**
     * 加载课堂资料
     */
    loadMaterials() {
        // 显示加载状态
        document.getElementById('materials-loading').style.display = 'block';
        document.getElementById('materials-list').innerHTML = '';
        document.getElementById('no-materials').style.display = 'none';
        
        // 获取文件列表
        api.getFiles(this.classroomId)
            .then(response => {
                // 隐藏加载状态
                document.getElementById('materials-loading').style.display = 'none';
                
                if (response.success && response.files && response.files.length > 0) {
                    // 显示文件列表
                    this.displayMaterials(response.files);
                } else {
                    // 显示无资料提示
                    document.getElementById('no-materials').style.display = 'block';
                }
            })
            .catch(error => {
                console.error('获取文件列表错误:', error);
                document.getElementById('materials-loading').style.display = 'none';
                document.getElementById('no-materials').style.display = 'block';
            });
    }
    
    /**
     * 显示课堂资料
     * @param {Array} files - 文件列表
     */
    displayMaterials(files) {
        const materialsList = document.getElementById('materials-list');
        materialsList.innerHTML = '';
        
        if (files.length === 0) {
            document.getElementById('no-materials').style.display = 'block';
            return;
        }
        
        document.getElementById('no-materials').style.display = 'none';
        
        files.forEach(file => {
            const fileType = file.file_type || file.original_filename.split('.').pop();
            const fileCategory = Utils.getFileCategory(fileType);
            
            const materialCard = document.createElement('div');
            materialCard.className = 'material-card';
            materialCard.dataset.category = fileCategory;
            
            materialCard.innerHTML = `
                <div class="material-thumbnail">
                    <i class="icon">${Utils.getFileTypeIcon(fileType)}</i>
                </div>
                <div class="material-info">
                    <div class="material-title">${file.original_filename}</div>
                    <div class="material-meta">
                        <span>${Utils.formatFileSize(file.file_size)}</span>
                        <span>${Utils.formatDateTime(file.upload_time)}</span>
                    </div>
                </div>
            `;
            
            // 添加点击事件
            materialCard.addEventListener('click', () => {
                window.open(api.getFileDownloadUrl(file.id), '_blank');
            });
            
            materialsList.appendChild(materialCard);
        });
    }
    
    /**
     * 筛选课堂资料
     * @param {string} filter - 筛选类型
     */
    filterMaterials(filter) {
        // 更新筛选按钮状态
        const filterItems = document.querySelectorAll('.filter-item');
        filterItems.forEach(item => {
            if (item.getAttribute('data-filter') === filter) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
        
        // 筛选资料
        const materialCards = document.querySelectorAll('.material-card');
        materialCards.forEach(card => {
            if (filter === 'all' || card.dataset.category === filter) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }
    
    /**
     * 搜索课堂资料
     * @param {string} keyword - 搜索关键词
     */
    searchMaterials(keyword) {
        const materialCards = document.querySelectorAll('.material-card');
        const lowerKeyword = keyword.toLowerCase();
        
        materialCards.forEach(card => {
            const title = card.querySelector('.material-title').textContent.toLowerCase();
            
            if (title.includes(lowerKeyword)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }
    
    /**
     * 加载课堂动态
     */
    loadActivities() {
        // 显示加载状态
        document.getElementById('activities-loading').style.display = 'block';
        document.getElementById('activity-list').innerHTML = '';
        document.getElementById('no-activities').style.display = 'none';
        
        // 获取课堂活动
        api.getClassroomActivities(this.classroomId)
            .then(response => {
                // 隐藏加载状态
                document.getElementById('activities-loading').style.display = 'none';
                
                if (response.success && response.activities && response.activities.length > 0) {
                    // 显示活动列表
                    this.displayActivities(response.activities);
                } else {
                    // 显示无活动提示
                    document.getElementById('no-activities').style.display = 'block';
                }
            })
            .catch(error => {
                console.error('获取课堂活动错误:', error);
                document.getElementById('activities-loading').style.display = 'none';
                document.getElementById('no-activities').style.display = 'block';
                
                // 如果API不存在，显示模拟数据
                this.displayMockActivities();
            });
    }
    
    /**
     * 显示课堂动态
     * @param {Array} activities - 活动列表
     */
    displayActivities(activities) {
        const activityList = document.getElementById('activity-list');
        activityList.innerHTML = '';
        
        if (activities.length === 0) {
            document.getElementById('no-activities').style.display = 'block';
            return;
        }
        
        document.getElementById('no-activities').style.display = 'none';
        
        activities.forEach(activity => {
            const activityItem = document.createElement('li');
            activityItem.className = 'activity-item';
            
            activityItem.innerHTML = `
                <div class="activity-icon">
                    <i class="icon">${this.getActivityIcon(activity.type)}</i>
                </div>
                <div class="activity-content">
                    <div class="activity-text">${activity.content}</div>
                    <div class="activity-time">${Utils.formatDateTime(activity.timestamp)}</div>
                </div>
            `;
            
            activityList.appendChild(activityItem);
        });
    }
    
    /**
     * 显示模拟课堂动态
     */
    displayMockActivities() {
        const activityList = document.getElementById('activity-list');
        activityList.innerHTML = '';
        
        document.getElementById('no-activities').style.display = 'none';
        
        const mockActivities = [
            {
                type: 'question',
                content: '教师发布了新题目: "1+1=?"',
                timestamp: new Date()
            },
            {
                type: 'file',
                content: '教师分享了文件: "课程讲义.pdf"',
                timestamp: new Date(Date.now() - 5 * 60000)
            },
            {
                type: 'screen',
                content: '小组1开始屏幕共享',
                timestamp: new Date(Date.now() - 10 * 60000)
            },
            {
                type: 'message',
                content: '教师发送了消息: "请各小组准备展示"',
                timestamp: new Date(Date.now() - 15 * 60000)
            }
        ];
        
        mockActivities.forEach(activity => {
            const activityItem = document.createElement('li');
            activityItem.className = 'activity-item';
            
            activityItem.innerHTML = `
                <div class="activity-icon">
                    <i class="icon">${this.getActivityIcon(activity.type)}</i>
                </div>
                <div class="activity-content">
                    <div class="activity-text">${activity.content}</div>
                    <div class="activity-time">${Utils.formatDateTime(activity.timestamp)}</div>
                </div>
            `;
            
            activityList.appendChild(activityItem);
        });
    }
    
    /**
     * 处理新文件事件
     * @param {Object} data - 文件数据
     */
    onNewFile(data) {
        console.log('收到新文件:', data);
        
        // 如果当前在资料标签页，刷新资料列表
        if (document.getElementById('materials-tab').classList.contains('active')) {
            this.loadMaterials();
        }
        
        // 显示通知
        Utils.showNotification(`收到新文件: ${data.file_name}`, 'info');
    }
    
    /**
     * 处理课堂活动事件
     * @param {Object} data - 活动数据
     */
    onClassroomActivity(data) {
        console.log('课堂活动:', data);
        
        // 如果当前在动态标签页，刷新动态列表
        if (document.getElementById('activity-tab').classList.contains('active')) {
            this.loadActivities();
        }
    }
    
    /**
     * 获取题目类型文本
     * @param {string} type - 题目类型
     * @returns {string} 题目类型文本
     */
    getQuestionTypeText(type) {
        const typeMap = {
            'single_choice': '单选题',
            'multiple_choice': '多选题',
            'true_false': '判断题',
            'text': '文本题'
        };
        
        return typeMap[type] || '未知类型';
    }
    
    /**
     * 获取活动图标
     * @param {string} type - 活动类型
     * @returns {string} 图标名称
     */
    getActivityIcon(type) {
        const iconMap = {
            'question': 'help',
            'file': 'insert_drive_file',
            'screen': 'screen_share',
            'message': 'message',
            'join': 'person_add',
            'leave': 'exit_to_app'
        };
        
        return iconMap[type] || 'notifications';
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new SmartClassroomClient();
});