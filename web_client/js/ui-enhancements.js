/**
 * UI增强功能 - 主题切换、快捷键、手势操作、无障碍访问
 */

class UIEnhancements {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'default';
        this.keyboardShortcuts = new Map();
        this.gestureHandler = new GestureHandler();
        this.accessibilityHelper = new AccessibilityHelper();
        
        this.init();
    }
    
    init() {
        this.setupThemeSwitcher();
        this.setupKeyboardShortcuts();
        this.setupGestureHandling();
        this.setupAccessibility();
        this.setupResponsiveFeatures();
        
        // 应用保存的主题
        this.applyTheme(this.currentTheme);
    }
    
    setupThemeSwitcher() {
        // 创建主题切换器
        const themeSwitcher = document.createElement('div');
        themeSwitcher.className = 'theme-switcher';
        themeSwitcher.innerHTML = `
            <button data-theme="default" class="${this.currentTheme === 'default' ? 'active' : ''}">默认主题</button>
            <button data-theme="dark" class="${this.currentTheme === 'dark' ? 'active' : ''}">深色主题</button>
            <button data-theme="high-contrast" class="${this.currentTheme === 'high-contrast' ? 'active' : ''}">高对比度</button>
        `;
        
        document.body.appendChild(themeSwitcher);
        
        // 绑定主题切换事件
        themeSwitcher.addEventListener('click', (e) => {
            if (e.target.tagName === 'BUTTON') {
                const theme = e.target.dataset.theme;
                this.switchTheme(theme);
            }
        });
    }
    
    switchTheme(theme) {
        this.currentTheme = theme;
        this.applyTheme(theme);
        localStorage.setItem('theme', theme);
        
        // 更新按钮状态
        document.querySelectorAll('.theme-switcher button').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.theme === theme);
        });
        
        // 通知其他组件主题已更改
        window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
    }
    
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        
        // 特殊主题的额外处理
        if (theme === 'high-contrast') {
            this.accessibilityHelper.enableHighContrast();
        } else {
            this.accessibilityHelper.disableHighContrast();
        }
    }
    
    setupKeyboardShortcuts() {
        // 定义快捷键
        const shortcuts = {
            'Alt+1': () => this.switchTab('answer-tab'),
            'Alt+2': () => this.switchTab('screen-tab'),
            'Alt+3': () => this.switchTab('materials-tab'),
            'Alt+4': () => this.switchTab('activity-tab'),
            'Ctrl+Enter': () => this.submitCurrentAction(),
            'Escape': () => this.cancelCurrentAction(),
            'F1': () => this.showKeyboardShortcuts(),
            'Ctrl+Shift+T': () => this.switchTheme('dark'),
            'Ctrl+Shift+H': () => this.switchTheme('high-contrast'),
            'Ctrl+Shift+D': () => this.switchTheme('default')
        };
        
        // 注册快捷键
        Object.entries(shortcuts).forEach(([key, action]) => {
            this.registerShortcut(key, action);
        });
        
        // 监听键盘事件
        document.addEventListener('keydown', (e) => {
            const key = this.getKeyString(e);
            if (this.keyboardShortcuts.has(key)) {
                e.preventDefault();
                this.keyboardShortcuts.get(key)();
            }
        });
        
        // 创建快捷键帮助
        this.createKeyboardShortcutsHelp();
    }
    
    registerShortcut(keyString, action) {
        this.keyboardShortcuts.set(keyString, action);
    }
    
    getKeyString(event) {
        const parts = [];
        if (event.ctrlKey) parts.push('Ctrl');
        if (event.altKey) parts.push('Alt');
        if (event.shiftKey) parts.push('Shift');
        if (event.metaKey) parts.push('Meta');
        
        const key = event.key === ' ' ? 'Space' : event.key;
        parts.push(key);
        
        return parts.join('+');
    }
    
    switchTab(tabId) {
        const tabButton = document.querySelector(`[data-tab="${tabId}"]`);
        if (tabButton) {
            tabButton.click();
        }
    }
    
    submitCurrentAction() {
        // 查找当前活动的提交按钮
        const activeTab = document.querySelector('.tab-content.active');
        if (activeTab) {
            const submitBtn = activeTab.querySelector('.btn:not(.btn-danger)');
            if (submitBtn && !submitBtn.disabled) {
                submitBtn.click();
            }
        }
    }
    
    cancelCurrentAction() {
        // 查找当前活动的取消按钮
        const activeTab = document.querySelector('.tab-content.active');
        if (activeTab) {
            const cancelBtn = activeTab.querySelector('.btn-danger');
            if (cancelBtn) {
                cancelBtn.click();
            }
        }
    }
    
    createKeyboardShortcutsHelp() {
        const helpDiv = document.createElement('div');
        helpDiv.className = 'keyboard-shortcuts';
        helpDiv.innerHTML = `
            <h4>快捷键</h4>
            <ul>
                <li><span>切换到答题</span><span class="key">Alt+1</span></li>
                <li><span>切换到投屏</span><span class="key">Alt+2</span></li>
                <li><span>切换到资料</span><span class="key">Alt+3</span></li>
                <li><span>切换到动态</span><span class="key">Alt+4</span></li>
                <li><span>提交/确认</span><span class="key">Ctrl+Enter</span></li>
                <li><span>取消/返回</span><span class="key">Esc</span></li>
                <li><span>显示帮助</span><span class="key">F1</span></li>
                <li><span>深色主题</span><span class="key">Ctrl+Shift+T</span></li>
                <li><span>高对比度</span><span class="key">Ctrl+Shift+H</span></li>
            </ul>
        `;
        
        document.body.appendChild(helpDiv);
    }
    
    showKeyboardShortcuts() {
        const helpDiv = document.querySelector('.keyboard-shortcuts');
        if (helpDiv) {
            helpDiv.classList.toggle('show');
            
            // 3秒后自动隐藏
            setTimeout(() => {
                helpDiv.classList.remove('show');
            }, 3000);
        }
    }
    
    setupGestureHandling() {
        // 启用触摸事件
        document.addEventListener('touchstart', (e) => {
            this.gestureHandler.handleTouchStart(e);
        });
        
        document.addEventListener('touchmove', (e) => {
            this.gestureHandler.handleTouchMove(e);
        });
        
        document.addEventListener('touchend', (e) => {
            this.gestureHandler.handleTouchEnd(e);
        });
        
        // 监听手势事件
        window.addEventListener('gesture', (e) => {
            this.handleGesture(e.detail);
        });
    }
    
    handleGesture(gestureData) {
        switch (gestureData.type) {
            case 'swipe-left':
                this.switchToNextTab();
                break;
            case 'swipe-right':
                this.switchToPrevTab();
                break;
            case 'swipe-up':
                this.scrollToTop();
                break;
            case 'swipe-down':
                this.scrollToBottom();
                break;
            case 'pinch-in':
                this.decreaseFontSize();
                break;
            case 'pinch-out':
                this.increaseFontSize();
                break;
        }
    }
    
    switchToNextTab() {
        const tabs = document.querySelectorAll('.tab-button');
        const activeTab = document.querySelector('.tab-button.active');
        const currentIndex = Array.from(tabs).indexOf(activeTab);
        const nextIndex = (currentIndex + 1) % tabs.length;
        tabs[nextIndex].click();
    }
    
    switchToPrevTab() {
        const tabs = document.querySelectorAll('.tab-button');
        const activeTab = document.querySelector('.tab-button.active');
        const currentIndex = Array.from(tabs).indexOf(activeTab);
        const prevIndex = (currentIndex - 1 + tabs.length) % tabs.length;
        tabs[prevIndex].click();
    }
    
    scrollToTop() {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
    
    scrollToBottom() {
        window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
    }
    
    increaseFontSize() {
        const currentSize = parseFloat(getComputedStyle(document.body).fontSize);
        document.body.style.fontSize = (currentSize * 1.1) + 'px';
        this.showGestureHint('字体已放大');
    }
    
    decreaseFontSize() {
        const currentSize = parseFloat(getComputedStyle(document.body).fontSize);
        document.body.style.fontSize = (currentSize * 0.9) + 'px';
        this.showGestureHint('字体已缩小');
    }
    
    showGestureHint(message) {
        let hint = document.querySelector('.gesture-hint');
        if (!hint) {
            hint = document.createElement('div');
            hint.className = 'gesture-hint';
            document.body.appendChild(hint);
        }
        
        hint.textContent = message;
        hint.classList.add('show');
        
        setTimeout(() => {
            hint.classList.remove('show');
        }, 1500);
    }
    
    setupAccessibility() {
        // 设置ARIA标签
        this.setupAriaLabels();
        
        // 设置焦点管理
        this.setupFocusManagement();
        
        // 设置屏幕阅读器支持
        this.setupScreenReaderSupport();
    }
    
    setupAriaLabels() {
        // 为重要元素添加ARIA标签
        const elements = [
            { selector: '.tab-button', role: 'tab' },
            { selector: '.tab-content', role: 'tabpanel' },
            { selector: '.btn', role: 'button' },
            { selector: '.option-item', role: 'option' },
            { selector: '.material-card', role: 'article' },
            { selector: '.activity-item', role: 'listitem' }
        ];
        
        elements.forEach(({ selector, role }) => {
            document.querySelectorAll(selector).forEach(el => {
                if (!el.getAttribute('role')) {
                    el.setAttribute('role', role);
                }
            });
        });
    }
    
    setupFocusManagement() {
        // 确保所有交互元素都可以获得焦点
        const interactiveElements = document.querySelectorAll(
            'button, input, select, textarea, a, [tabindex], .option-item, .material-card'
        );
        
        interactiveElements.forEach(el => {
            if (!el.hasAttribute('tabindex') && !['BUTTON', 'INPUT', 'SELECT', 'TEXTAREA', 'A'].includes(el.tagName)) {
                el.setAttribute('tabindex', '0');
            }
        });
        
        // 添加焦点指示器
        document.addEventListener('focusin', (e) => {
            e.target.classList.add('focused');
        });
        
        document.addEventListener('focusout', (e) => {
            e.target.classList.remove('focused');
        });
    }
    
    setupScreenReaderSupport() {
        // 为动态内容添加live region
        const liveRegion = document.createElement('div');
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.className = 'sr-only';
        liveRegion.id = 'live-region';
        document.body.appendChild(liveRegion);
        
        // 监听内容变化并通知屏幕阅读器
        this.setupContentChangeNotifications();
    }
    
    setupContentChangeNotifications() {
        // 监听通知显示
        const originalShowNotification = window.showNotification;
        if (originalShowNotification) {
            window.showNotification = (message, type) => {
                originalShowNotification(message, type);
                this.announceToScreenReader(message);
            };
        }
        
        // 监听页面状态变化
        window.addEventListener('pageStateChanged', (e) => {
            this.announceToScreenReader(e.detail.message);
        });
    }
    
    announceToScreenReader(message) {
        const liveRegion = document.getElementById('live-region');
        if (liveRegion) {
            liveRegion.textContent = message;
        }
    }
    
    setupResponsiveFeatures() {
        // 检测设备类型
        this.detectDeviceType();
        
        // 监听屏幕方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleOrientationChange();
            }, 100);
        });
        
        // 监听窗口大小变化
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));
    }
    
    detectDeviceType() {
        const userAgent = navigator.userAgent;
        const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
        const isTablet = /iPad|Android(?!.*Mobile)/i.test(userAgent);
        
        document.body.classList.toggle('mobile', isMobile && !isTablet);
        document.body.classList.toggle('tablet', isTablet);
        document.body.classList.toggle('desktop', !isMobile && !isTablet);
        
        // 设置触摸支持
        if ('ontouchstart' in window) {
            document.body.classList.add('touch-device');
        }
    }
    
    handleOrientationChange() {
        // 重新计算布局
        this.adjustLayoutForOrientation();
        
        // 通知用户方向已改变
        const orientation = window.orientation === 0 || window.orientation === 180 ? '竖屏' : '横屏';
        this.announceToScreenReader(`屏幕方向已切换到${orientation}模式`);
    }
    
    adjustLayoutForOrientation() {
        const isLandscape = window.innerWidth > window.innerHeight;
        document.body.classList.toggle('landscape', isLandscape);
        document.body.classList.toggle('portrait', !isLandscape);
        
        // 调整内容显示
        if (isLandscape && document.body.classList.contains('mobile')) {
            // 横屏时优化移动端布局
            this.optimizeForLandscape();
        }
    }
    
    optimizeForLandscape() {
        // 在横屏模式下调整UI元素
        const tabs = document.querySelector('.classroom-tabs');
        if (tabs) {
            tabs.style.flexDirection = 'row';
        }
    }
    
    handleResize() {
        // 重新检测设备类型
        this.detectDeviceType();
        
        // 调整字体大小
        this.adjustFontSizeForScreen();
    }
    
    adjustFontSizeForScreen() {
        const screenWidth = window.innerWidth;
        let fontSize = '16px';
        
        if (screenWidth < 480) {
            fontSize = '14px';
        } else if (screenWidth < 768) {
            fontSize = '15px';
        } else if (screenWidth >= 1200) {
            fontSize = '17px';
        }
        
        document.documentElement.style.fontSize = fontSize;
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

class GestureHandler {
    constructor() {
        this.startX = 0;
        this.startY = 0;
        this.endX = 0;
        this.endY = 0;
        this.startTime = 0;
        this.minSwipeDistance = 50;
        this.maxSwipeTime = 300;
        this.touches = [];
    }
    
    handleTouchStart(e) {
        this.startTime = Date.now();
        this.touches = Array.from(e.touches);
        
        if (e.touches.length === 1) {
            this.startX = e.touches[0].clientX;
            this.startY = e.touches[0].clientY;
        }
    }
    
    handleTouchMove(e) {
        e.preventDefault(); // 防止页面滚动
    }
    
    handleTouchEnd(e) {
        if (e.changedTouches.length === 1) {
            this.endX = e.changedTouches[0].clientX;
            this.endY = e.changedTouches[0].clientY;
            
            const deltaTime = Date.now() - this.startTime;
            
            if (deltaTime <= this.maxSwipeTime) {
                this.detectSwipe();
            }
        } else if (this.touches.length === 2 && e.touches.length === 0) {
            this.detectPinch();
        }
    }
    
    detectSwipe() {
        const deltaX = this.endX - this.startX;
        const deltaY = this.endY - this.startY;
        const absDeltaX = Math.abs(deltaX);
        const absDeltaY = Math.abs(deltaY);
        
        if (Math.max(absDeltaX, absDeltaY) >= this.minSwipeDistance) {
            let gestureType;
            
            if (absDeltaX > absDeltaY) {
                gestureType = deltaX > 0 ? 'swipe-right' : 'swipe-left';
            } else {
                gestureType = deltaY > 0 ? 'swipe-down' : 'swipe-up';
            }
            
            this.dispatchGesture(gestureType, {
                deltaX,
                deltaY,
                distance: Math.sqrt(deltaX * deltaX + deltaY * deltaY)
            });
        }
    }
    
    detectPinch() {
        if (this.touches.length === 2) {
            const touch1 = this.touches[0];
            const touch2 = this.touches[1];
            
            const initialDistance = Math.sqrt(
                Math.pow(touch2.clientX - touch1.clientX, 2) +
                Math.pow(touch2.clientY - touch1.clientY, 2)
            );
            
            // 简化的缩放检测
            const gestureType = initialDistance > 100 ? 'pinch-out' : 'pinch-in';
            
            this.dispatchGesture(gestureType, {
                initialDistance,
                centerX: (touch1.clientX + touch2.clientX) / 2,
                centerY: (touch1.clientY + touch2.clientY) / 2
            });
        }
    }
    
    dispatchGesture(type, data) {
        window.dispatchEvent(new CustomEvent('gesture', {
            detail: { type, data }
        }));
    }
}

class AccessibilityHelper {
    constructor() {
        this.highContrastEnabled = false;
        this.largeTextEnabled = false;
    }
    
    enableHighContrast() {
        if (!this.highContrastEnabled) {
            document.body.classList.add('high-contrast');
            this.highContrastEnabled = true;
            this.announceChange('高对比度模式已启用');
        }
    }
    
    disableHighContrast() {
        if (this.highContrastEnabled) {
            document.body.classList.remove('high-contrast');
            this.highContrastEnabled = false;
            this.announceChange('高对比度模式已禁用');
        }
    }
    
    enableLargeText() {
        if (!this.largeTextEnabled) {
            document.body.classList.add('large-text');
            this.largeTextEnabled = true;
            this.announceChange('大字体模式已启用');
        }
    }
    
    disableLargeText() {
        if (this.largeTextEnabled) {
            document.body.classList.remove('large-text');
            this.largeTextEnabled = false;
            this.announceChange('大字体模式已禁用');
        }
    }
    
    announceChange(message) {
        const liveRegion = document.getElementById('live-region');
        if (liveRegion) {
            liveRegion.textContent = message;
        }
    }
    
    setupKeyboardNavigation() {
        // 确保所有交互元素都支持键盘导航
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                const target = e.target;
                if (target.classList.contains('option-item') || 
                    target.classList.contains('material-card') ||
                    target.classList.contains('share-option')) {
                    e.preventDefault();
                    target.click();
                }
            }
        });
    }
}

// 初始化UI增强功能
document.addEventListener('DOMContentLoaded', () => {
    window.uiEnhancements = new UIEnhancements();
});

// 导出类供其他模块使用
window.UIEnhancements = UIEnhancements;
window.GestureHandler = GestureHandler;
window.AccessibilityHelper = AccessibilityHelper;