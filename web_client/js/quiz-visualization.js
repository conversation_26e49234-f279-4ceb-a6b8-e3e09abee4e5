/**
 * 智慧课堂系统 - 答题结果可视化组件
 * 提供答题结果的图表展示功能
 */

class QuizVisualization {
    /**
     * 初始化可视化组件
     * @param {string} containerId - 图表容器元素ID
     */
    constructor(containerId) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.charts = {};

        // 检查容器是否存在
        if (!this.container) {
            console.error(`图表容器 ${containerId} 不存在`);
            return;
        }

        // 加载Chart.js库
        this.loadChartJs();
    }

    /**
     * 动态加载Chart.js库
     */
    loadChartJs() {
        if (window.Chart) {
            console.log('Chart.js已加载');
            return;
        }

        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js';
        script.integrity = 'sha256-+8RZJLOWupRKnqQOCYRPDCpZ0pWGCb8vvEkRJ8I43A0='
        script.crossOrigin = 'anonymous';
        script.onload = () => {
            console.log('Chart.js加载成功');
        };
        script.onerror = () => {
            console.error('Chart.js加载失败');
        };

        document.head.appendChild(script);
    }

    /**
     * 清除容器内容
     */
    clearContainer() {
        while (this.container.firstChild) {
            this.container.removeChild(this.container.firstChild);
        }

        // 销毁所有图表实例
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.destroy();
            }
        });

        this.charts = {};
    }

    /**
     * 创建图表容器
     * @param {string} chartId - 图表ID
     * @param {string} title - 图表标题
     * @returns {HTMLElement} 图表容器元素
     */
    createChartContainer(chartId, title) {
        const chartContainer = document.createElement('div');
        chartContainer.className = 'chart-container';

        if (title) {
            const titleElement = document.createElement('h3');
            titleElement.className = 'chart-title';
            titleElement.textContent = title;
            chartContainer.appendChild(titleElement);
        }

        const canvas = document.createElement('canvas');
        canvas.id = chartId;
        chartContainer.appendChild(canvas);

        this.container.appendChild(chartContainer);

        return canvas;
    }

    /**
     * 显示单选题或判断题结果
     * @param {Object} data - 答题结果数据
     * @param {string} title - 图表标题
     */
    showSingleChoiceResults(data, title = '答题结果分布') {
        const chartId = 'single-choice-chart';
        this.clearContainer();

        const canvas = this.createChartContainer(chartId, title);

        if (!window.Chart) {
            console.error('Chart.js未加载');
            return;
        }

        const labels = Object.keys(data.answer_distribution);
        const values = Object.values(data.answer_distribution);

        // 生成随机颜色
        const backgroundColors = this.generateColors(labels.length);

        this.charts[chartId] = new Chart(canvas, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '选择人数',
                    data: values,
                    backgroundColor: backgroundColors,
                    borderColor: backgroundColors.map(color => this.darkenColor(color, 0.1)),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function (context) {
                                const value = context.raw;
                                const total = values.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${value} 人 (${percentage}%)`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });

        // 添加统计信息
        this.addStatisticsInfo(data);
    }

    /**
     * 显示多选题结果
     * @param {Object} data - 答题结果数据
     * @param {string} title - 图表标题
     */
    showMultipleChoiceResults(data, title = '多选题答题结果') {
        const chartId = 'multiple-choice-chart';
        this.clearContainer();

        const canvas = this.createChartContainer(chartId, title);

        if (!window.Chart) {
            console.error('Chart.js未加载');
            return;
        }

        const labels = Object.keys(data.answer_distribution);
        const values = Object.values(data.answer_distribution);

        // 生成随机颜色
        const backgroundColors = this.generateColors(labels.length);

        this.charts[chartId] = new Chart(canvas, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '选择次数',
                    data: values,
                    backgroundColor: backgroundColors,
                    borderColor: backgroundColors.map(color => this.darkenColor(color, 0.1)),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function (context) {
                                const value = context.raw;
                                const totalStudents = data.statistics.total_answers;
                                const percentage = Math.round((value / totalStudents) * 100);
                                return `${value} 次 (${percentage}% 的学生)`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });

        // 添加统计信息
        this.addStatisticsInfo(data);
    }

    /**
     * 显示文本题结果
     * @param {Object} data - 答题结果数据
     * @param {string} title - 图表标题
     */
    showTextResults(data, title = '文本题答题结果') {
        this.clearContainer();

        // 创建标题
        const titleElement = document.createElement('h3');
        titleElement.className = 'chart-title';
        titleElement.textContent = title;
        this.container.appendChild(titleElement);

        // 创建文本答案列表
        const answersList = document.createElement('div');
        answersList.className = 'text-answers-list';

        if (data.student_answers && data.student_answers.length > 0) {
            data.student_answers.forEach(answer => {
                const answerItem = document.createElement('div');
                answerItem.className = 'text-answer-item';

                const studentInfo = document.createElement('div');
                studentInfo.className = 'student-info';
                studentInfo.textContent = answer.student_name;

                const answerContent = document.createElement('div');
                answerContent.className = 'answer-content';
                answerContent.textContent = answer.content;

                answerItem.appendChild(studentInfo);
                answerItem.appendChild(answerContent);
                answersList.appendChild(answerItem);
            });
        } else {
            const noAnswers = document.createElement('p');
            noAnswers.textContent = '暂无答案';
            answersList.appendChild(noAnswers);
        }

        this.container.appendChild(answersList);

        // 添加统计信息
        this.addStatisticsInfo(data);
    }

    /**
     * 显示答题正确率
     * @param {Object} data - 答题结果数据
     * @param {string} title - 图表标题
     */
    showAccuracyChart(data, title = '答题正确率') {
        const chartId = 'accuracy-chart';

        const canvas = this.createChartContainer(chartId, title);

        if (!window.Chart) {
            console.error('Chart.js未加载');
            return;
        }

        const correctCount = data.statistics.correct_answers;
        const incorrectCount = data.statistics.total_answers - correctCount;

        this.charts[chartId] = new Chart(canvas, {
            type: 'doughnut',
            data: {
                labels: ['正确', '错误'],
                datasets: [{
                    data: [correctCount, incorrectCount],
                    backgroundColor: ['#4CAF50', '#F44336'],
                    borderColor: ['#388E3C', '#D32F2F'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function (context) {
                                const value = context.raw;
                                const total = correctCount + incorrectCount;
                                const percentage = Math.round((value / total) * 100);
                                return `${value} 人 (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * 显示响应时间分布
     * @param {Object} data - 答题结果数据
     * @param {string} title - 图表标题
     */
    showResponseTimeChart(data, title = '答题时间分布') {
        const chartId = 'response-time-chart';

        const canvas = this.createChartContainer(chartId, title);

        if (!window.Chart) {
            console.error('Chart.js未加载');
            return;
        }

        // 提取响应时间数据
        const responseTimes = data.student_answers.map(answer => answer.response_time).filter(time => time !== null);

        // 计算时间区间
        const minTime = Math.min(...responseTimes);
        const maxTime = Math.max(...responseTimes);
        const timeRange = maxTime - minTime;
        const binCount = Math.min(10, Math.ceil(Math.sqrt(responseTimes.length)));
        const binSize = timeRange / binCount;

        // 创建时间区间
        const bins = Array(binCount).fill(0);
        const binLabels = [];

        for (let i = 0; i < binCount; i++) {
            const start = minTime + i * binSize;
            const end = minTime + (i + 1) * binSize;
            binLabels.push(`${start.toFixed(1)}s - ${end.toFixed(1)}s`);
        }

        // 统计每个区间的答题人数
        responseTimes.forEach(time => {
            const binIndex = Math.min(binCount - 1, Math.floor((time - minTime) / binSize));
            bins[binIndex]++;
        });

        this.charts[chartId] = new Chart(canvas, {
            type: 'bar',
            data: {
                labels: binLabels,
                datasets: [{
                    label: '答题人数',
                    data: bins,
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
    }

    /**
     * 显示小组对比结果
     * @param {Object} data - 小组对比数据
     * @param {string} title - 图表标题
     */
    showGroupComparisonChart(data, title = '小组答题对比') {
        const chartId = 'group-comparison-chart';
        this.clearContainer();

        const canvas = this.createChartContainer(chartId, title);

        if (!window.Chart) {
            console.error('Chart.js未加载');
            return;
        }

        const groups = data.groups;
        const labels = groups.map(group => group.group_name);
        const accuracyData = groups.map(group => group.accuracy_rate);
        const responseTimeData = groups.map(group => group.avg_response_time);

        // 生成随机颜色
        const backgroundColors = this.generateColors(labels.length);

        this.charts[chartId] = new Chart(canvas, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: '正确率 (%)',
                        data: accuracyData,
                        backgroundColor: backgroundColors,
                        borderColor: backgroundColors.map(color => this.darkenColor(color, 0.1)),
                        borderWidth: 1,
                        yAxisID: 'y'
                    },
                    {
                        label: '平均答题时间 (秒)',
                        data: responseTimeData,
                        backgroundColor: 'rgba(255, 99, 132, 0.5)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1,
                        type: 'line',
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '正确率 (%)'
                        },
                        max: 100
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '平均答题时间 (秒)'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                }
            }
        });

        // 添加小组详细信息
        this.addGroupDetails(data);
    }

    /**
     * 添加统计信息
     * @param {Object} data - 答题结果数据
     */
    addStatisticsInfo(data) {
        if (!data.statistics) return;

        const statsContainer = document.createElement('div');
        statsContainer.className = 'statistics-container';

        const stats = data.statistics;
        const totalAnswers = stats.total_answers;
        const correctAnswers = stats.correct_answers;
        const accuracyRate = stats.accuracy_rate;
        const avgResponseTime = stats.avg_response_time;

        const statsHTML = `
            <div class="stat-item">
                <div class="stat-value">${totalAnswers}</div>
                <div class="stat-label">总答题人数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${correctAnswers}</div>
                <div class="stat-label">答对人数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${accuracyRate.toFixed(1)}%</div>
                <div class="stat-label">正确率</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${avgResponseTime.toFixed(1)}s</div>
                <div class="stat-label">平均答题时间</div>
            </div>
        `;

        statsContainer.innerHTML = statsHTML;
        this.container.appendChild(statsContainer);
    }

    /**
     * 添加小组详细信息
     * @param {Object} data - 小组对比数据
     */
    addGroupDetails(data) {
        const detailsContainer = document.createElement('div');
        detailsContainer.className = 'group-details-container';

        const groups = data.groups;

        groups.forEach(group => {
            const groupDetail = document.createElement('div');
            groupDetail.className = 'group-detail';

            const groupHeader = document.createElement('h4');
            groupHeader.textContent = group.group_name;
            groupDetail.appendChild(groupHeader);

            const groupStats = document.createElement('div');
            groupStats.className = 'group-stats';
            groupStats.innerHTML = `
                <div class="group-stat">
                    <span class="stat-label">答题人数:</span>
                    <span class="stat-value">${group.total_answers}</span>
                </div>
                <div class="group-stat">
                    <span class="stat-label">正确人数:</span>
                    <span class="stat-value">${group.correct_answers}</span>
                </div>
                <div class="group-stat">
                    <span class="stat-label">正确率:</span>
                    <span class="stat-value">${group.accuracy_rate.toFixed(1)}%</span>
                </div>
                <div class="group-stat">
                    <span class="stat-label">平均答题时间:</span>
                    <span class="stat-value">${group.avg_response_time.toFixed(1)}s</span>
                </div>
            `;

            groupDetail.appendChild(groupStats);
            detailsContainer.appendChild(groupDetail);
        });

        this.container.appendChild(detailsContainer);
    }

    /**
     * 显示答题结果
     * @param {Object} data - 答题结果数据
     * @param {string} questionType - 题目类型
     */
    showResults(data, questionType) {
        if (!data) {
            console.error('答题结果数据为空');
            return;
        }

        this.clearContainer();

        switch (questionType) {
            case 'single_choice':
                this.showSingleChoiceResults(data, '单选题答题结果');
                break;
            case 'multiple_choice':
                this.showMultipleChoiceResults(data, '多选题答题结果');
                break;
            case 'true_false':
                this.showSingleChoiceResults(data, '判断题答题结果');
                break;
            case 'text':
                this.showTextResults(data, '文本题答题结果');
                break;
            default:
                console.error(`不支持的题目类型: ${questionType}`);
                return;
        }

        // 添加正确率图表
        if (data.statistics && data.statistics.total_answers > 0 &&
            questionType !== 'text' && data.question && data.question.correct_answer) {
            this.showAccuracyChart(data);
        }

        // 添加响应时间图表
        if (data.student_answers && data.student_answers.length > 0) {
            this.showResponseTimeChart(data);
        }
    }

    /**
     * 显示小组对比结果
     * @param {Object} data - 小组对比数据
     */
    showGroupComparison(data) {
        if (!data || !data.groups || data.groups.length === 0) {
            console.error('小组对比数据为空');
            return;
        }

        this.showGroupComparisonChart(data);
    }

    /**
     * 生成随机颜色
     * @param {number} count - 颜色数量
     * @returns {Array} 颜色数组
     */
    generateColors(count) {
        const colors = [];
        const baseColors = [
            'rgba(255, 99, 132, 0.6)',   // 红色
            'rgba(54, 162, 235, 0.6)',   // 蓝色
            'rgba(255, 206, 86, 0.6)',   // 黄色
            'rgba(75, 192, 192, 0.6)',   // 青色
            'rgba(153, 102, 255, 0.6)',  // 紫色
            'rgba(255, 159, 64, 0.6)',   // 橙色
            'rgba(199, 199, 199, 0.6)',  // 灰色
            'rgba(83, 102, 255, 0.6)',   // 靛蓝色
            'rgba(255, 99, 255, 0.6)',   // 粉色
            'rgba(99, 255, 132, 0.6)'    // 浅绿色
        ];

        for (let i = 0; i < count; i++) {
            if (i < baseColors.length) {
                colors.push(baseColors[i]);
            } else {
                // 生成随机颜色
                const r = Math.floor(Math.random() * 255);
                const g = Math.floor(Math.random() * 255);
                const b = Math.floor(Math.random() * 255);
                colors.push(`rgba(${r}, ${g}, ${b}, 0.6)`);
            }
        }

        return colors;
    }

    /**
     * 使颜色变暗
     * @param {string} color - RGBA颜色字符串
     * @param {number} amount - 变暗程度 (0-1)
     * @returns {string} 变暗后的颜色
     */
    darkenColor(color, amount) {
        // 解析RGBA颜色
        const rgba = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)/);
        if (!rgba) return color;

        const r = Math.max(0, parseInt(rgba[1]) - Math.round(parseInt(rgba[1]) * amount));
        const g = Math.max(0, parseInt(rgba[2]) - Math.round(parseInt(rgba[2]) * amount));
        const b = Math.max(0, parseInt(rgba[3]) - Math.round(parseInt(rgba[3]) * amount));
        const a = rgba[4] || 1;

        return `rgba(${r}, ${g}, ${b}, ${a})`;
    }
}