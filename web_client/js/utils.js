/**
 * 智慧课堂系统 - 工具函数
 * 提供通用的辅助功能
 */

class Utils {
    /**
     * 显示通知
     * @param {string} message - 通知消息
     * @param {string} type - 通知类型 (success, warning, error)
     * @param {number} duration - 显示时长(毫秒)
     */
    static showNotification(message, type = 'success', duration = 3000) {
        const notification = document.getElementById('notification');
        const notificationMessage = document.getElementById('notification-message');
        
        // 设置消息内容
        notificationMessage.textContent = message;
        
        // 设置通知类型
        notification.className = 'notification';
        notification.classList.add(`notification-${type}`);
        
        // 显示通知
        notification.classList.add('show');
        
        // 设置自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
        }, duration);
    }

    /**
     * 格式化日期时间
     * @param {string|Date} dateTime - 日期时间字符串或对象
     * @param {boolean} includeTime - 是否包含时间
     * @returns {string} 格式化后的日期时间
     */
    static formatDateTime(dateTime, includeTime = true) {
        const date = new Date(dateTime);
        
        if (isNaN(date.getTime())) {
            return '无效日期';
        }
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        
        if (includeTime) {
            return `${year}-${month}-${day} ${hours}:${minutes}`;
        } else {
            return `${year}-${month}-${day}`;
        }
    }

    /**
     * 格式化文件大小
     * @param {number} bytes - 文件大小(字节)
     * @returns {string} 格式化后的文件大小
     */
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 获取文件类型图标
     * @param {string} fileType - 文件类型
     * @returns {string} 图标名称
     */
    static getFileTypeIcon(fileType) {
        const iconMap = {
            'pdf': 'picture_as_pdf',
            'doc': 'description',
            'docx': 'description',
            'ppt': 'slideshow',
            'pptx': 'slideshow',
            'xls': 'table_chart',
            'xlsx': 'table_chart',
            'jpg': 'image',
            'jpeg': 'image',
            'png': 'image',
            'gif': 'image',
            'mp4': 'videocam',
            'avi': 'videocam',
            'mp3': 'audiotrack',
            'wav': 'audiotrack'
        };
        
        return iconMap[fileType.toLowerCase()] || 'insert_drive_file';
    }

    /**
     * 获取文件类型分类
     * @param {string} fileType - 文件类型
     * @returns {string} 文件分类
     */
    static getFileCategory(fileType) {
        const type = fileType.toLowerCase();
        
        if (['doc', 'docx', 'pdf', 'txt', 'ppt', 'pptx', 'xls', 'xlsx'].includes(type)) {
            return 'document';
        } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'].includes(type)) {
            return 'image';
        } else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(type)) {
            return 'video';
        } else if (['mp3', 'wav', 'ogg', 'flac', 'aac'].includes(type)) {
            return 'audio';
        } else {
            return 'other';
        }
    }

    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     */
    static generateUniqueId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
    }

    /**
     * 获取设备信息
     * @returns {Object} 设备信息
     */
    static getDeviceInfo() {
        const userAgent = navigator.userAgent;
        let deviceType = 'unknown';
        
        if (/Android/i.test(userAgent)) {
            deviceType = 'android';
        } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
            deviceType = 'ios';
        } else if (/Windows/i.test(userAgent)) {
            deviceType = 'windows';
        } else if (/Mac/i.test(userAgent)) {
            deviceType = 'mac';
        } else if (/Linux/i.test(userAgent)) {
            deviceType = 'linux';
        }
        
        return {
            device_id: this.generateDeviceId(),
            device_type: 'student_web',
            platform: deviceType,
            screen_resolution: `${window.screen.width}x${window.screen.height}`,
            user_agent: userAgent
        };
    }

    /**
     * 生成设备ID
     * @returns {string} 设备ID
     */
    static generateDeviceId() {
        let deviceId = localStorage.getItem('device_id');
        
        if (!deviceId) {
            deviceId = 'web_' + this.generateUniqueId();
            localStorage.setItem('device_id', deviceId);
        }
        
        return deviceId;
    }

    /**
     * 防抖函数
     * @param {Function} func - 要执行的函数
     * @param {number} wait - 等待时间(毫秒)
     * @returns {Function} 防抖处理后的函数
     */
    static debounce(func, wait = 300) {
        let timeout;
        
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     * @param {Function} func - 要执行的函数
     * @param {number} limit - 限制时间(毫秒)
     * @returns {Function} 节流处理后的函数
     */
    static throttle(func, limit = 300) {
        let inThrottle;
        
        return function executedFunction(...args) {
            if (!inThrottle) {
                func(...args);
                inThrottle = true;
                setTimeout(() => {
                    inThrottle = false;
                }, limit);
            }
        };
    }
}