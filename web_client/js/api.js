/**
 * 智慧课堂系统 - API服务
 * 处理与后端API的所有交互
 */

class ApiService {
    constructor() {
        this.baseUrl = 'http://localhost:5000/api';
        this.token = localStorage.getItem('classroom_token') || null;
    }

    /**
     * 设置认证令牌
     * @param {string} token - JWT令牌
     */
    setToken(token) {
        this.token = token;
        localStorage.setItem('classroom_token', token);
    }

    /**
     * 清除认证令牌
     */
    clearToken() {
        this.token = null;
        localStorage.removeItem('classroom_token');
    }

    /**
     * 获取请求头
     * @returns {Object} 包含认证信息的请求头
     */
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }
        
        return headers;
    }

    /**
     * 发送HTTP请求
     * @param {string} method - HTTP方法
     * @param {string} endpoint - API端点
     * @param {Object} data - 请求数据
     * @returns {Promise} 请求响应
     */
    async request(method, endpoint, data = null) {
        const url = `${this.baseUrl}${endpoint}`;
        const options = {
            method,
            headers: this.getHeaders(),
            credentials: 'include'
        };
        
        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }
        
        try {
            const response = await fetch(url, options);
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.message || '请求失败');
            }
            
            return result;
        } catch (error) {
            console.error('API请求错误:', error);
            throw error;
        }
    }

    /**
     * 学生加入课堂
     * @param {string} accessCode - 课堂访问码
     * @param {string} studentName - 学生姓名
     * @param {Object} deviceInfo - 设备信息
     * @returns {Promise} 加入课堂的响应
     */
    async joinClassroom(accessCode, studentName, deviceInfo) {
        return this.request('POST', '/classroom/join', {
            access_code: accessCode,
            student_name: studentName,
            device_info: deviceInfo
        });
    }

    /**
     * 获取课堂状态
     * @param {string} classroomId - 课堂ID
     * @returns {Promise} 课堂状态
     */
    async getClassroomStatus(classroomId) {
        return this.request('GET', `/classroom/status/${classroomId}`);
    }

    /**
     * 获取课堂题目列表
     * @param {string} classroomId - 课堂ID
     * @returns {Promise} 题目列表
     */
    async getQuestions(classroomId) {
        return this.request('GET', `/questions/classroom/${classroomId}`);
    }

    /**
     * 获取题目详情
     * @param {string} questionId - 题目ID
     * @returns {Promise} 题目详情
     */
    async getQuestionDetails(questionId) {
        return this.request('GET', `/questions/${questionId}`);
    }

    /**
     * 提交答案
     * @param {string} questionId - 题目ID
     * @param {string} studentId - 学生ID
     * @param {string|Array} answer - 答案内容
     * @returns {Promise} 提交结果
     */
    async submitAnswer(questionId, studentId, answer) {
        return this.request('POST', `/questions/${questionId}/answer`, {
            student_id: studentId,
            answer: answer
        });
    }
    
    /**
     * 获取题目结果
     * @param {string} questionId - 题目ID
     * @param {string} groupBy - 分组方式 ('all', 'group')
     * @returns {Promise} 题目结果
     */
    async getQuestionResults(questionId, groupBy = 'all') {
        return this.request('GET', `/questions/${questionId}/results?group_by=${groupBy}`);
    }
    
    /**
     * 比较小组答题结果
     * @param {string} classroomId - 课堂ID
     * @param {string} questionId - 题目ID (可选)
     * @returns {Promise} 小组比较结果
     */
    async compareGroupResults(classroomId, questionId = null) {
        let url = `/questions/compare-groups?classroom_id=${classroomId}`;
        if (questionId) {
            url += `&question_id=${questionId}`;
        }
        return this.request('GET', url);
    }
    
    /**
     * 获取题目统计
     * @param {string} classroomId - 课堂ID
     * @returns {Promise} 题目统计
     */
    async getQuestionStatistics(classroomId) {
        return this.request('GET', `/questions/statistics/${classroomId}`);
    }
    
    /**
     * 获取题目结果导出链接
     * @param {string} questionId - 题目ID
     * @param {string} format - 导出格式 ('csv', 'json')
     * @returns {string} 导出链接
     */
    getQuestionExportUrl(questionId, format = 'csv') {
        return `${this.baseUrl}/questions/${questionId}/export?format=${format}`;
    }

    /**
     * 上传文件
     * @param {File} file - 文件对象
     * @param {string} uploaderId - 上传者ID
     * @param {string} classroomId - 课堂ID
     * @returns {Promise} 上传结果
     */
    async uploadFile(file, uploaderId, classroomId) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('uploader_id', uploaderId);
        formData.append('classroom_id', classroomId);
        
        const url = `${this.baseUrl}/files/upload`;
        
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': this.token ? `Bearer ${this.token}` : ''
                },
                body: formData
            });
            
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.message || '文件上传失败');
            }
            
            return result;
        } catch (error) {
            console.error('文件上传错误:', error);
            throw error;
        }
    }

    /**
     * 获取文件列表
     * @param {string} classroomId - 课堂ID
     * @returns {Promise} 文件列表
     */
    async getFiles(classroomId) {
        return this.request('GET', `/files/list?classroom_id=${classroomId}`);
    }

    /**
     * 下载文件
     * @param {string} fileId - 文件ID
     * @returns {string} 文件下载URL
     */
    getFileDownloadUrl(fileId) {
        return `${this.baseUrl}/files/download/${fileId}`;
    }

    /**
     * 获取课堂活动
     * @param {string} classroomId - 课堂ID
     * @returns {Promise} 课堂活动列表
     */
    async getClassroomActivities(classroomId) {
        // 假设有一个获取课堂活动的API
        return this.request('GET', `/classroom/${classroomId}/activities`);
    }

    /**
     * 注册设备
     * @param {Object} deviceInfo - 设备信息
     * @returns {Promise} 注册结果
     */
    async registerDevice(deviceInfo) {
        return this.request('POST', '/devices/register', deviceInfo);
    }

    /**
     * 更新设备心跳
     * @param {string} deviceId - 设备ID
     * @returns {Promise} 心跳更新结果
     */
    async updateHeartbeat(deviceId) {
        return this.request('POST', `/devices/${deviceId}/heartbeat`);
    }
}

// 创建API服务实例
const api = new ApiService();