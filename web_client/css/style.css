/* 智慧课堂系统 - 学生端样式 */

:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-gray: #f5f5f5;
    --medium-gray: #ddd;
    --dark-gray: #999;
    --text-color: #333;
    --white: #fff;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* 主题变量 */
[data-theme="dark"] {
    --primary-color: #3498db;
    --secondary-color: #34495e;
    --success-color: #27ae60;
    --warning-color: #e67e22;
    --danger-color: #c0392b;
    --light-gray: #2c3e50;
    --medium-gray: #34495e;
    --dark-gray: #7f8c8d;
    --text-color: #ecf0f1;
    --white: #2c3e50;
    --shadow: 0 2px 10px rgba(0,0,0,0.3);
}

[data-theme="high-contrast"] {
    --primary-color: #0066cc;
    --secondary-color: #000000;
    --success-color: #008000;
    --warning-color: #ff8c00;
    --danger-color: #dc143c;
    --light-gray: #ffffff;
    --medium-gray: #cccccc;
    --dark-gray: #666666;
    --text-color: #000000;
    --white: #ffffff;
    --shadow: 0 2px 10px rgba(0,0,0,0.5);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: var(--light-gray);
    color: var(--text-color);
    line-height: 1.6;
}

.header {
    background-color: var(--secondary-color);
    color: var(--white);
    padding: 1rem;
    text-align: center;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow);
}

.header h1 {
    font-size: 1.5rem;
    font-weight: normal;
}

.main-content {
    max-width: 1200px;
    margin: 1rem auto;
    padding: 0 1rem;
}

/* 登录页面样式 */
.welcome-section {
    text-align: center;
    margin-bottom: 2rem;
}

.welcome-section h2 {
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.login-section {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.qr-login, .code-login {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    min-width: 300px;
}

.qr-login h3, .code-login h3 {
    margin-bottom: 1rem;
    color: var(--secondary-color);
    text-align: center;
}

#qr-code-area {
    height: 200px;
    border: 2px dashed var(--medium-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark-gray);
}

#classroom-code {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--medium-gray);
    border-radius: 4px;
    font-size: 1rem;
    margin-bottom: 1rem;
}

.btn {
    width: 100%;
    padding: 0.75rem;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
    text-align: center;
}

.btn:hover {
    background-color: var(--primary-dark);
}

.btn-success {
    background-color: var(--success-color);
}

.btn-warning {
    background-color: var(--warning-color);
}

.btn-danger {
    background-color: var(--danger-color);
}

/* 课堂界面样式 */
.classroom-interface {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.classroom-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--white);
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.classroom-info h2 {
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.classroom-tabs {
    display: flex;
    background-color: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    margin-bottom: 1rem;
}

.tab-button {
    flex: 1;
    padding: 1rem;
    text-align: center;
    background-color: var(--white);
    border: none;
    cursor: pointer;
    transition: background-color 0.3s;
    font-weight: bold;
}

.tab-button.active {
    background-color: var(--primary-color);
    color: var(--white);
}

.tab-content {
    display: none;
    background-color: var(--white);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.tab-content.active {
    display: block;
}

/* 答题界面样式 */
.question-container {
    margin-bottom: 2rem;
}

.question-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.question-type {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
}

.question-content {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
}

.options-list {
    list-style: none;
}

.option-item {
    background-color: var(--light-gray);
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.option-item:hover {
    background-color: var(--medium-gray);
}

.option-item.selected {
    background-color: var(--primary-color);
    color: var(--white);
}

/* 投屏界面样式 */
.screen-share-options {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.share-option {
    flex: 1;
    min-width: 200px;
    background-color: var(--light-gray);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    text-align: center;
    cursor: pointer;
    transition: transform 0.3s;
}

.share-option:hover {
    transform: translateY(-5px);
}

.share-option i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.preview-container {
    width: 100%;
    height: 300px;
    background-color: var(--light-gray);
    border: 2px dashed var(--medium-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

/* 资料查看样式 */
.materials-filter {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.filter-item {
    padding: 0.5rem 1rem;
    background-color: var(--light-gray);
    border-radius: 20px;
    cursor: pointer;
}

.filter-item.active {
    background-color: var(--primary-color);
    color: var(--white);
}

.materials-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

.material-card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: transform 0.3s;
}

.material-card:hover {
    transform: translateY(-5px);
}

.material-thumbnail {
    height: 150px;
    background-color: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
}

.material-info {
    padding: 1rem;
}

.material-title {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.material-meta {
    color: var(--dark-gray);
    font-size: 0.9rem;
    display: flex;
    justify-content: space-between;
}

/* 课堂动态样式 */
.activity-list {
    list-style: none;
}

.activity-item {
    padding: 1rem;
    border-bottom: 1px solid var(--medium-gray);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.activity-content {
    flex: 1;
}

.activity-time {
    color: var(--dark-gray);
    font-size: 0.9rem;
}

/* 加载动画 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--light-gray);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem;
    background-color: var(--white);
    border-left: 4px solid var(--primary-color);
    box-shadow: var(--shadow);
    border-radius: 4px;
    z-index: 1000;
    transition: transform 0.3s, opacity 0.3s;
    transform: translateX(100%);
    opacity: 0;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-success {
    border-left-color: var(--success-color);
}

.notification-warning {
    border-left-color: var(--warning-color);
}

.notification-error {
    border-left-color: var(--danger-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .login-section {
        flex-direction: column;
        align-items: center;
    }
    
    .qr-login, .code-login {
        min-width: 100%;
        max-width: 400px;
    }
    
    .classroom-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .classroom-tabs {
        flex-direction: column;
    }
    
    .screen-share-options {
        flex-direction: column;
    }
    
    .materials-filter {
        overflow-x: auto;
        padding-bottom: 0.5rem;
    }
}

/* 图标字体 */
.icon {
    font-family: 'Material Icons', sans-serif;
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}
/* 增强的
响应式设计 */
@media (max-width: 480px) {
    /* 超小屏幕 - 手机 */
    .header h1 {
        font-size: 1.2rem;
    }
    
    .main-content {
        padding: 0 0.5rem;
        margin: 0.5rem auto;
    }
    
    .btn {
        min-height: 44px;
        font-size: 16px;
        padding: 12px 20px;
    }
    
    #classroom-code, #student-name {
        min-height: 44px;
        font-size: 16px;
        padding: 12px;
    }
    
    .tab-button {
        min-height: 44px;
        font-size: 14px;
        padding: 12px 8px;
    }
    
    .option-item {
        min-height: 44px;
        padding: 12px;
        font-size: 16px;
    }
    
    .share-option {
        min-width: 100%;
        padding: 20px;
    }
    
    .materials-list {
        grid-template-columns: 1fr;
    }
    
    .classroom-tabs {
        flex-wrap: wrap;
    }
    
    .tab-button {
        flex: 1 1 50%;
        min-width: 120px;
    }
}

@media (min-width: 481px) and (max-width: 768px) {
    /* 平板竖屏 */
    .materials-list {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .tab-button {
        flex: 1 1 25%;
        min-width: 100px;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    /* 平板横屏 */
    .materials-list {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1025px) {
    /* 桌面端 */
    .materials-list {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .btn:hover,
    .tab-button:hover,
    .option-item:hover,
    .share-option:hover,
    .material-card:hover {
        transform: none;
        background-color: var(--primary-color);
        color: var(--white);
    }
    
    .btn:active,
    .tab-button:active,
    .option-item:active {
        transform: scale(0.98);
        transition: transform 0.1s;
    }
}

/* 无障碍访问支持 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 焦点指示器 */
.btn:focus,
.tab-button:focus,
.option-item:focus,
input:focus,
button:focus {
    outline: 3px solid var(--primary-color);
    outline-offset: 2px;
}

/* 高对比度模式下的焦点指示器 */
[data-theme="high-contrast"] .btn:focus,
[data-theme="high-contrast"] .tab-button:focus,
[data-theme="high-contrast"] .option-item:focus,
[data-theme="high-contrast"] input:focus,
[data-theme="high-contrast"] button:focus {
    outline: 4px solid #0066cc;
    outline-offset: 3px;
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .loading-spinner {
        animation: none;
        border: 4px solid var(--primary-color);
    }
}

/* 大字体支持 */
@media (min-resolution: 2dppx) {
    body {
        font-size: 18px;
    }
    
    .btn {
        font-size: 18px;
        padding: 14px 24px;
    }
    
    .tab-button {
        font-size: 16px;
        padding: 14px 20px;
    }
}

/* 主题切换按钮 */
.theme-switcher {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 999;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 10px;
}

.theme-switcher button {
    display: block;
    width: 100%;
    margin-bottom: 5px;
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    background: var(--light-gray);
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
}

.theme-switcher button:hover {
    background: var(--primary-color);
    color: var(--white);
}

.theme-switcher button.active {
    background: var(--primary-color);
    color: var(--white);
}

/* 快捷键提示 */
.keyboard-shortcuts {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 15px;
    max-width: 300px;
    display: none;
}

.keyboard-shortcuts.show {
    display: block;
}

.keyboard-shortcuts h4 {
    margin-bottom: 10px;
    color: var(--secondary-color);
}

.keyboard-shortcuts ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.keyboard-shortcuts li {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 14px;
}

.keyboard-shortcuts .key {
    background: var(--light-gray);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 12px;
}

/* 手势操作提示 */
.gesture-hint {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px;
    border-radius: var(--border-radius);
    text-align: center;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s;
}

.gesture-hint.show {
    opacity: 1;
}

/* 多内容展示样式 */
.content-display {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.content-display.single {
    grid-template-columns: 1fr;
}

.content-display.dual {
    grid-template-columns: 1fr 1fr;
}

.content-display.triple {
    grid-template-columns: repeat(3, 1fr);
}

.content-display.quad {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
}

@media (max-width: 768px) {
    .content-display.dual,
    .content-display.triple,
    .content-display.quad {
        grid-template-columns: 1fr;
    }
}

.content-item {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.content-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.content-item-header {
    background: var(--primary-color);
    color: var(--white);
    padding: 10px 15px;
    font-weight: bold;
}

.content-item-body {
    padding: 15px;
}

.content-item-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark-gray);
}

.content-item-text {
    padding: 15px;
    line-height: 1.6;
}

.content-item-file {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
}

.content-item-file .icon {
    font-size: 2rem;
    color: var(--primary-color);
}

.content-item-actions {
    padding: 10px 15px;
    border-top: 1px solid var(--medium-gray);
    display: flex;
    gap: 10px;
}

.content-item-actions button {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    background: var(--light-gray);
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
}

.content-item-actions button:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* 打印样式 */
@media print {
    .header,
    .classroom-tabs,
    .theme-switcher,
    .keyboard-shortcuts,
    .btn,
    button {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .main-content {
        max-width: none;
        margin: 0;
        padding: 0;
    }
    
    .tab-content {
        display: block !important;
        box-shadow: none;
        border: 1px solid #ccc;
        page-break-inside: avoid;
    }
}