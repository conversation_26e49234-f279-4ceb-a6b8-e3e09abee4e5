<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧课堂系统 - 学生端</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div id="app">
        <header class="header">
            <h1>智慧课堂系统 - 学生端</h1>
        </header>
        
        <main class="main-content">
            <!-- 登录页面 -->
            <div id="login-page">
                <div class="welcome-section">
                    <h2>欢迎使用智慧课堂系统</h2>
                    <p>请扫描二维码或输入课堂码加入课堂</p>
                </div>
                
                <div class="login-section">
                    <div class="qr-login">
                        <h3>扫码登录</h3>
                        <div id="qr-code-area">
                            <div id="qrcode"></div>
                        </div>
                    </div>
                    
                    <div class="code-login">
                        <h3>课堂码登录</h3>
                        <input type="text" id="classroom-code" placeholder="请输入课堂码">
                        <input type="text" id="student-name" placeholder="请输入您的姓名">
                        <button id="join-btn" class="btn">加入课堂</button>
                    </div>
                </div>
            </div>
            
            <!-- 课堂界面 -->
            <div id="classroom-page" style="display: none;">
                <div class="classroom-header">
                    <div class="classroom-info">
                        <h2 id="classroom-name">课堂名称</h2>
                        <p>学生: <span id="student-info">姓名</span></p>
                    </div>
                    <button id="leave-btn" class="btn btn-danger">退出课堂</button>
                </div>
                
                <div class="classroom-tabs">
                    <button class="tab-button active" data-tab="answer-tab">互动答题</button>
                    <button class="tab-button" data-tab="screen-tab">投屏展示</button>
                    <button class="tab-button" data-tab="materials-tab">课堂资料</button>
                    <button class="tab-button" data-tab="activity-tab">课堂动态</button>
                </div>
                
                <!-- 互动答题界面 -->
                <div id="answer-tab" class="tab-content active">
                    <div id="no-questions" class="message-box">
                        <p>当前没有进行中的答题活动</p>
                    </div>
                    
                    <div id="question-area" style="display: none;">
                        <div class="question-container">
                            <div class="question-header">
                                <span id="question-number">问题 1/5</span>
                                <span id="question-type" class="question-type">单选题</span>
                            </div>
                            <div id="question-content" class="question-content">
                                问题内容将显示在这里
                            </div>
                            <ul id="options-list" class="options-list">
                                <!-- 选项将动态生成 -->
                            </ul>
                            <div class="question-actions" style="margin-top: 20px;">
                                <button id="submit-answer" class="btn">提交答案</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 投屏展示界面 -->
                <div id="screen-tab" class="tab-content">
                    <h3>选择投屏方式</h3>
                    <div class="screen-share-options">
                        <div class="share-option" id="share-screen">
                            <i class="icon">computer</i>
                            <h4>屏幕共享</h4>
                            <p>分享您的整个屏幕或应用窗口</p>
                        </div>
                        <div class="share-option" id="share-file">
                            <i class="icon">insert_drive_file</i>
                            <h4>文件分享</h4>
                            <p>上传并分享文档、图片等文件</p>
                        </div>
                        <div class="share-option" id="share-camera">
                            <i class="icon">videocam</i>
                            <h4>摄像头分享</h4>
                            <p>分享您的摄像头画面</p>
                        </div>
                    </div>
                    
                    <div id="screen-preview-container" style="display: none;">
                        <h3>预览</h3>
                        <div class="preview-container">
                            <video id="preview-video" autoplay muted style="max-width: 100%; max-height: 100%;"></video>
                            <div id="file-preview"></div>
                        </div>
                        <div class="preview-actions">
                            <button id="start-sharing" class="btn">开始分享</button>
                            <button id="cancel-sharing" class="btn btn-danger">取消</button>
                        </div>
                    </div>
                    
                    <div id="file-upload-container" style="display: none;">
                        <h3>上传文件</h3>
                        <input type="file" id="file-input" accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.ppt,.pptx">
                        <p>支持的格式: JPG, PNG, PDF, DOC, DOCX, PPT, PPTX</p>
                        <div class="preview-actions">
                            <button id="upload-file" class="btn">上传并分享</button>
                            <button id="cancel-upload" class="btn btn-danger">取消</button>
                        </div>
                    </div>
                    
                    <div id="sharing-status" style="display: none;">
                        <div class="sharing-info">
                            <h3>正在分享</h3>
                            <p id="sharing-type-info">屏幕内容</p>
                        </div>
                        <button id="stop-sharing" class="btn btn-danger">停止分享</button>
                    </div>
                </div>
                
                <!-- 课堂资料界面 -->
                <div id="materials-tab" class="tab-content">
                    <div class="materials-filter">
                        <div class="filter-item active" data-filter="all">全部</div>
                        <div class="filter-item" data-filter="document">文档</div>
                        <div class="filter-item" data-filter="image">图片</div>
                        <div class="filter-item" data-filter="video">视频</div>
                        <div class="filter-item" data-filter="other">其他</div>
                    </div>
                    
                    <div class="materials-search">
                        <input type="text" id="search-materials" placeholder="搜索资料...">
                    </div>
                    
                    <div id="materials-list" class="materials-list">
                        <!-- 资料卡片将动态生成 -->
                    </div>
                    
                    <div id="no-materials" class="message-box">
                        <p>暂无课堂资料</p>
                    </div>
                    
                    <div class="loading" id="materials-loading">
                        <div class="loading-spinner"></div>
                    </div>
                </div>
                
                <!-- 课堂动态界面 -->
                <div id="activity-tab" class="tab-content">
                    <ul id="activity-list" class="activity-list">
                        <!-- 活动项将动态生成 -->
                    </ul>
                    
                    <div id="no-activities" class="message-box">
                        <p>暂无课堂动态</p>
                    </div>
                    
                    <div class="loading" id="activities-loading">
                        <div class="loading-spinner"></div>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- 通知组件 -->
        <div id="notification" class="notification">
            <div id="notification-message"></div>
        </div>
    </div>
    
    <!-- 二维码生成库 -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.0/build/qrcode.min.js"></script>
    <!-- Socket.IO 客户端库 -->
    <script src="https://cdn.socket.io/4.4.1/socket.io.min.js"></script>
    <!-- 应用脚本 -->
    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/quiz-visualization.js"></script>
    <script src="js/ui-enhancements.js"></script>
    <script src="js/app.js"></script>
</body>
</html>