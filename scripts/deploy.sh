#!/bin/bash
# 智慧课堂系统部署脚本
# Smart Classroom System Deployment Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
INSTALL_DIR="/opt/smart-classroom"
SERVICE_USER="smartclass"
LOG_DIR="/var/log/smart-classroom"
DATA_DIR="/var/lib/smart-classroom"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统要求
check_system_requirements() {
    log_info "检查系统要求..."
    
    # 检查操作系统
    if ! grep -q "UnionTech OS" /etc/os-release 2>/dev/null; then
        log_warning "建议在统信UOS系统上运行"
    fi
    
    # 检查Python版本
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)" 2>/dev/null; then
        log_error "需要Python 3.8或更高版本"
        exit 1
    fi
    
    # 检查内存
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    if [ "$MEMORY_GB" -lt 8 ]; then
        log_warning "建议至少8GB内存，当前: ${MEMORY_GB}GB"
    fi
    
    # 检查磁盘空间
    DISK_GB=$(df -BG "$PROJECT_ROOT" | awk 'NR==2{print $4}' | sed 's/G//')
    if [ "$DISK_GB" -lt 50 ]; then
        log_warning "建议至少50GB可用空间，当前: ${DISK_GB}GB"
    fi
    
    log_success "系统要求检查完成"
}

# 安装系统依赖
install_system_dependencies() {
    log_info "安装系统依赖..."
    
    # 更新包管理器
    sudo apt update
    
    # 安装基础依赖
    sudo apt install -y \
        python3-pip \
        python3-venv \
        python3-dev \
        build-essential \
        pkg-config \
        libgl1-mesa-glx \
        libglib2.0-0 \
        libxcb-xinerama0 \
        libxcb-cursor0 \
        libxkbcommon-x11-0 \
        x11-utils \
        xdotool \
        ffmpeg \
        sqlite3 \
        nginx \
        supervisor
    
    # 安装MediaMTX
    if ! command -v mediamtx &> /dev/null; then
        log_info "安装MediaMTX流媒体服务器..."
        wget -O /tmp/mediamtx.tar.gz https://github.com/bluenviron/mediamtx/releases/latest/download/mediamtx_v1.0.0_linux_amd64.tar.gz
        sudo tar -xzf /tmp/mediamtx.tar.gz -C /usr/local/bin/
        sudo chmod +x /usr/local/bin/mediamtx
    fi
    
    log_success "系统依赖安装完成"
}

# 创建用户和目录
setup_user_and_directories() {
    log_info "创建用户和目录..."
    
    # 创建系统用户
    if ! id "$SERVICE_USER" &>/dev/null; then
        sudo useradd -r -s /bin/false -d "$DATA_DIR" "$SERVICE_USER"
        log_success "创建用户: $SERVICE_USER"
    fi
    
    # 创建目录
    sudo mkdir -p "$INSTALL_DIR" "$LOG_DIR" "$DATA_DIR"
    sudo chown -R "$SERVICE_USER:$SERVICE_USER" "$LOG_DIR" "$DATA_DIR"
    
    log_success "用户和目录创建完成"
}

# 安装Python依赖
install_python_dependencies() {
    log_info "安装Python依赖..."
    
    # 创建虚拟环境
    python3 -m venv "$INSTALL_DIR/venv"
    source "$INSTALL_DIR/venv/bin/activate"
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装项目依赖
    pip install -r "$PROJECT_ROOT/requirements.txt"
    
    # 安装项目本身
    pip install -e "$PROJECT_ROOT"
    
    log_success "Python依赖安装完成"
}

# 复制项目文件
copy_project_files() {
    log_info "复制项目文件..."
    
    # 复制源代码
    sudo cp -r "$PROJECT_ROOT"/* "$INSTALL_DIR/"
    
    # 设置权限
    sudo chown -R "$SERVICE_USER:$SERVICE_USER" "$INSTALL_DIR"
    sudo chmod +x "$INSTALL_DIR/scripts"/*.sh
    
    log_success "项目文件复制完成"
}

# 配置服务
configure_services() {
    log_info "配置系统服务..."
    
    # 创建systemd服务文件
    sudo tee /etc/systemd/system/smart-classroom-backend.service > /dev/null <<EOF
[Unit]
Description=Smart Classroom Backend Service
After=network.target

[Service]
Type=simple
User=$SERVICE_USER
WorkingDirectory=$INSTALL_DIR
Environment=PATH=$INSTALL_DIR/venv/bin
ExecStart=$INSTALL_DIR/venv/bin/python -m backend.app
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

    # 创建MediaMTX服务文件
    sudo tee /etc/systemd/system/mediamtx.service > /dev/null <<EOF
[Unit]
Description=MediaMTX Streaming Server
After=network.target

[Service]
Type=simple
User=$SERVICE_USER
ExecStart=/usr/local/bin/mediamtx $INSTALL_DIR/config/mediamtx.yml
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd
    sudo systemctl daemon-reload
    
    # 启用服务
    sudo systemctl enable smart-classroom-backend
    sudo systemctl enable mediamtx
    
    log_success "系统服务配置完成"
}

# 配置Nginx
configure_nginx() {
    log_info "配置Nginx..."
    
    sudo tee /etc/nginx/sites-available/smart-classroom > /dev/null <<EOF
server {
    listen 80;
    server_name _;
    
    # 静态文件
    location /static/ {
        alias $INSTALL_DIR/web_client/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # WebSocket代理
    location /socket.io/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # 默认页面
    location / {
        root $INSTALL_DIR/web_client;
        index index.html;
        try_files \$uri \$uri/ /index.html;
    }
}
EOF

    # 启用站点
    sudo ln -sf /etc/nginx/sites-available/smart-classroom /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # 测试配置
    sudo nginx -t
    
    # 启用并启动Nginx
    sudo systemctl enable nginx
    sudo systemctl restart nginx
    
    log_success "Nginx配置完成"
}

# 创建配置文件
create_config_files() {
    log_info "创建配置文件..."
    
    # MediaMTX配置
    sudo tee "$INSTALL_DIR/config/mediamtx.yml" > /dev/null <<EOF
# MediaMTX配置文件
logLevel: info
logDestinations: [stdout]
logFile: $LOG_DIR/mediamtx.log

# API配置
api: yes
apiAddress: 127.0.0.1:9997

# RTMP配置
rtmp: yes
rtmpAddress: :1935

# WebRTC配置
webrtc: yes
webrtcAddress: :8889

# 路径配置
paths:
  all:
    source: publisher
    publishUser: ""
    publishPass: ""
    readUser: ""
    readPass: ""
EOF

    # 应用配置
    sudo tee "$INSTALL_DIR/config/production.yml" > /dev/null <<EOF
# 生产环境配置
app:
  name: "智慧课堂系统"
  version: "1.0.0"
  debug: false
  host: "0.0.0.0"
  port: 5000

database:
  url: "sqlite:///$DATA_DIR/smart_classroom.db"
  echo: false

logging:
  level: "INFO"
  file: "$LOG_DIR/app.log"
  max_bytes: 10485760  # 10MB
  backup_count: 5

mediamtx:
  rtmp_url: "rtmp://localhost:1935"
  api_url: "http://localhost:9997"

security:
  secret_key: "$(openssl rand -hex 32)"
  jwt_secret: "$(openssl rand -hex 32)"
  password_salt: "$(openssl rand -hex 16)"
EOF

    sudo chown -R "$SERVICE_USER:$SERVICE_USER" "$INSTALL_DIR/config"
    
    log_success "配置文件创建完成"
}

# 初始化数据库
initialize_database() {
    log_info "初始化数据库..."
    
    cd "$INSTALL_DIR"
    sudo -u "$SERVICE_USER" "$INSTALL_DIR/venv/bin/python" init_db.py
    
    log_success "数据库初始化完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    sudo systemctl start mediamtx
    sudo systemctl start smart-classroom-backend
    sudo systemctl start nginx
    
    # 检查服务状态
    sleep 5
    
    if sudo systemctl is-active --quiet mediamtx; then
        log_success "MediaMTX服务启动成功"
    else
        log_error "MediaMTX服务启动失败"
    fi
    
    if sudo systemctl is-active --quiet smart-classroom-backend; then
        log_success "后端服务启动成功"
    else
        log_error "后端服务启动失败"
    fi
    
    if sudo systemctl is-active --quiet nginx; then
        log_success "Nginx服务启动成功"
    else
        log_error "Nginx服务启动失败"
    fi
}

# 创建桌面快捷方式
create_desktop_shortcuts() {
    log_info "创建桌面快捷方式..."
    
    # 教师端快捷方式
    sudo tee /usr/share/applications/smart-classroom-teacher.desktop > /dev/null <<EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=智慧课堂-教师端
Name[en]=Smart Classroom Teacher
Comment=智慧课堂系统教师端应用
Comment[en]=Smart Classroom System Teacher Application
Exec=$INSTALL_DIR/venv/bin/python $INSTALL_DIR/teacher_client/main.py
Icon=$INSTALL_DIR/web_client/images/teacher-icon.png
Terminal=false
Categories=Education;
EOF

    # 小组端快捷方式
    sudo tee /usr/share/applications/smart-classroom-group.desktop > /dev/null <<EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=智慧课堂-小组端
Name[en]=Smart Classroom Group
Comment=智慧课堂系统小组端应用
Comment[en]=Smart Classroom System Group Application
Exec=$INSTALL_DIR/venv/bin/python $INSTALL_DIR/group_client/main.py
Icon=$INSTALL_DIR/web_client/images/group-icon.png
Terminal=false
Categories=Education;
EOF

    log_success "桌面快捷方式创建完成"
}

# 主部署函数
main() {
    log_info "开始部署智慧课堂系统..."
    
    # 检查是否以root权限运行
    if [[ $EUID -ne 0 ]]; then
        log_error "请以root权限运行此脚本"
        exit 1
    fi
    
    # 执行部署步骤
    check_system_requirements
    install_system_dependencies
    setup_user_and_directories
    install_python_dependencies
    copy_project_files
    create_config_files
    configure_services
    configure_nginx
    initialize_database
    start_services
    create_desktop_shortcuts
    
    log_success "智慧课堂系统部署完成！"
    echo
    echo "访问地址："
    echo "  学生端Web界面: http://$(hostname -I | awk '{print $1}')"
    echo "  系统管理界面: http://$(hostname -I | awk '{print $1}')/admin"
    echo
    echo "服务管理命令："
    echo "  查看服务状态: systemctl status smart-classroom-backend"
    echo "  重启后端服务: systemctl restart smart-classroom-backend"
    echo "  查看日志: journalctl -u smart-classroom-backend -f"
    echo
    echo "客户端启动："
    echo "  教师端: $INSTALL_DIR/venv/bin/python $INSTALL_DIR/teacher_client/main.py"
    echo "  小组端: $INSTALL_DIR/venv/bin/python $INSTALL_DIR/group_client/main.py"
}

# 运行主函数
main "$@"