#!/bin/bash
# 智慧课堂系统依赖安装脚本 (统信UOS)

set -e

echo "开始安装智慧课堂系统依赖..."

# 检查Python版本
python_version=$(python3 --version 2>&1 | grep -Po '(?<=Python )\d+\.\d+')
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "错误: 需要Python 3.8或更高版本，当前版本: $python_version"
    exit 1
fi

echo "Python版本检查通过: $python_version"

# 更新系统包
echo "更新系统包..."
sudo apt update

# 安装系统依赖
echo "安装系统依赖..."
sudo apt install -y \
    python3-pip \
    python3-venv \
    python3-dev \
    build-essential \
    pkg-config \
    libgl1-mesa-dev \
    libglib2.0-dev \
    libxcb-xinerama0 \
    libxcb-cursor0 \
    libxkbcommon-x11-0 \
    libxcb-icccm4 \
    libxcb-image0 \
    libxcb-keysyms1 \
    libxcb-randr0 \
    libxcb-render-util0 \
    libxcb-shape0 \
    libxcb-sync1 \
    libxcb-xfixes0 \
    libxcb-xkb1 \
    libxcb1 \
    libx11-xcb1 \
    ffmpeg \
    xdotool

# 安装MediaMTX
echo "安装MediaMTX..."
MEDIAMTX_VERSION="v1.2.1"
MEDIAMTX_URL="https://github.com/bluenviron/mediamtx/releases/download/${MEDIAMTX_VERSION}/mediamtx_${MEDIAMTX_VERSION}_linux_amd64.tar.gz"

if [ ! -f "/usr/local/bin/mediamtx" ]; then
    echo "下载MediaMTX..."
    wget -O /tmp/mediamtx.tar.gz "$MEDIAMTX_URL"
    
    echo "解压MediaMTX..."
    cd /tmp
    tar -xzf mediamtx.tar.gz
    
    echo "安装MediaMTX..."
    sudo mv mediamtx /usr/local/bin/
    sudo chmod +x /usr/local/bin/mediamtx
    
    # 创建配置目录
    sudo mkdir -p /etc/mediamtx
    
    echo "MediaMTX安装完成"
else
    echo "MediaMTX已安装"
fi

# 创建虚拟环境
echo "创建Python虚拟环境..."
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 升级pip
echo "升级pip..."
pip install --upgrade pip

# 安装Python依赖
echo "安装Python依赖..."
pip install -r requirements.txt

# 创建必要目录
echo "创建必要目录..."
mkdir -p logs
mkdir -p uploads
mkdir -p data

# 设置权限
chmod +x scripts/*.sh

echo "依赖安装完成！"
echo ""
echo "使用方法:"
echo "1. 激活虚拟环境: source venv/bin/activate"
echo "2. 启动后端服务: python backend/app.py"
echo "3. 启动教师端: python teacher_client/main.py"
echo "4. 启动小组端: python group_client/main.py"