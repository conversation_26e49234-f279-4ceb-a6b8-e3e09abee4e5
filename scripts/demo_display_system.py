#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 多屏显示控制系统演示
"""

import sys
import os
import time
import requests
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def demo_display_system():
    """演示多屏显示控制系统"""
    
    print("=" * 60)
    print("智慧课堂多屏显示控制系统演示")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    api_url = f"{base_url}/api/display"
    classroom_id = "demo_classroom_001"
    
    # 检查服务是否运行
    try:
        response = requests.get(f"{api_url}/modes", timeout=5)
        if response.status_code != 200:
            print("❌ 后端服务未运行，请先启动服务")
            return
        print("✅ 后端服务连接正常")
    except Exception as e:
        print(f"❌ 无法连接到后端服务: {str(e)}")
        print("请确保后端服务正在运行 (python -m smart_classroom.backend.run_server)")
        return
    
    print("\n🎯 演示场景：智慧课堂多屏协作教学")
    print("-" * 40)
    
    # 场景1：创建广播显示配置
    print("\n📺 场景1：教师广播课件到所有小组屏幕")
    broadcast_config = {
        'classroom_id': classroom_id,
        'mode': 'broadcast',
        'layout': 'single',
        'target_devices': ['group_screen_001', 'group_screen_002', 'group_screen_003', 'group_screen_004'],
        'content_sources': ['teacher_courseware'],
        'sync_enabled': True
    }
    
    try:
        response = requests.post(f"{api_url}/configs", json=broadcast_config)
        if response.status_code == 201:
            config_data = response.json()['config']
            config_id = config_data['config_id']
            print(f"✅ 创建广播配置成功: {config_id}")
            
            # 应用配置
            response = requests.post(f"{api_url}/configs/{config_id}/apply")
            if response.status_code == 200:
                print("✅ 广播配置已应用，课件正在同步到所有小组屏幕")
            else:
                print("❌ 广播配置应用失败")
        else:
            print("❌ 创建广播配置失败")
    except Exception as e:
        print(f"❌ 广播配置操作失败: {str(e)}")
    
    time.sleep(2)
    
    # 场景2：创建分组计时器
    print("\n⏰ 场景2：启动小组讨论计时器（5分钟）")
    timer_data = {
        'classroom_id': classroom_id,
        'title': '小组讨论时间',
        'duration': 300,  # 5分钟
        'group_ids': ['group_001', 'group_002', 'group_003', 'group_004']
    }
    
    try:
        response = requests.post(f"{api_url}/timers", json=timer_data)
        if response.status_code == 201:
            timer_info = response.json()['timer']
            timer_id = timer_info['timer_id']
            print(f"✅ 创建计时器成功: {timer_id}")
            
            # 启动计时器
            response = requests.post(f"{api_url}/timers/{timer_id}/start")
            if response.status_code == 200:
                print("✅ 计时器已启动，各小组开始讨论")
                
                # 模拟计时器运行
                for i in range(5):
                    time.sleep(1)
                    remaining = 300 - (i + 1)
                    minutes = remaining // 60
                    seconds = remaining % 60
                    print(f"⏱️  剩余时间: {minutes:02d}:{seconds:02d}")
                
                # 暂停计时器演示
                print("\n⏸️  教师暂停计时器进行讲解...")
                requests.post(f"{api_url}/timers/{timer_id}/pause")
                time.sleep(2)
                
                # 恢复计时器
                print("▶️  恢复计时器...")
                requests.post(f"{api_url}/timers/{timer_id}/resume")
                time.sleep(1)
                
                # 停止计时器
                print("⏹️  讨论结束，停止计时器")
                requests.post(f"{api_url}/timers/{timer_id}/stop")
            else:
                print("❌ 计时器启动失败")
        else:
            print("❌ 创建计时器失败")
    except Exception as e:
        print(f"❌ 计时器操作失败: {str(e)}")
    
    time.sleep(2)
    
    # 场景3：分发讨论主题
    print("\n💭 场景3：分发讨论主题到指定小组")
    topic_data = {
        'classroom_id': classroom_id,
        'title': '团队协作效率提升',
        'content': '请各小组讨论：\n1. 当前团队协作中存在哪些问题？\n2. 如何利用技术手段提升协作效率？\n3. 制定具体的改进方案',
        'target_groups': ['group_001', 'group_002']
    }
    
    try:
        response = requests.post(f"{api_url}/topics", json=topic_data)
        if response.status_code == 201:
            topic_info = response.json()['topic']
            topic_id = topic_info['topic_id']
            print(f"✅ 创建讨论主题成功: {topic_id}")
            
            # 分发主题
            response = requests.post(f"{api_url}/topics/{topic_id}/distribute")
            if response.status_code == 200:
                print("✅ 讨论主题已分发到指定小组")
            else:
                print("❌ 讨论主题分发失败")
        else:
            print("❌ 创建讨论主题失败")
    except Exception as e:
        print(f"❌ 讨论主题操作失败: {str(e)}")
    
    time.sleep(2)
    
    # 场景4：对比显示模式
    print("\n🔄 场景4：切换到对比显示模式，展示不同小组成果")
    comparison_config = {
        'classroom_id': classroom_id,
        'mode': 'comparison',
        'layout': 'quad',
        'target_devices': ['teacher_main_screen'],
        'content_sources': ['group_001_result', 'group_002_result', 'group_003_result', 'group_004_result'],
        'sync_enabled': True
    }
    
    try:
        response = requests.post(f"{api_url}/configs", json=comparison_config)
        if response.status_code == 201:
            config_data = response.json()['config']
            config_id = config_data['config_id']
            print(f"✅ 创建对比显示配置成功: {config_id}")
            
            # 应用配置
            response = requests.post(f"{api_url}/configs/{config_id}/apply")
            if response.status_code == 200:
                print("✅ 对比显示配置已应用，教师屏幕显示四个小组的成果对比")
            else:
                print("❌ 对比显示配置应用失败")
        else:
            print("❌ 创建对比显示配置失败")
    except Exception as e:
        print(f"❌ 对比显示配置操作失败: {str(e)}")
    
    time.sleep(2)
    
    # 场景5：双屏联动模式
    print("\n📱 场景5：启用双屏联动模式")
    dual_screen_config = {
        'classroom_id': classroom_id,
        'mode': 'dual_screen',
        'layout': 'dual',
        'target_devices': ['teacher_primary_screen', 'teacher_secondary_screen'],
        'content_sources': ['main_content', 'auxiliary_content'],
        'sync_enabled': True
    }
    
    try:
        response = requests.post(f"{api_url}/configs", json=dual_screen_config)
        if response.status_code == 201:
            config_data = response.json()['config']
            config_id = config_data['config_id']
            print(f"✅ 创建双屏联动配置成功: {config_id}")
            
            # 应用配置
            response = requests.post(f"{api_url}/configs/{config_id}/apply")
            if response.status_code == 200:
                print("✅ 双屏联动配置已应用，主屏显示课件，副屏显示辅助信息")
            else:
                print("❌ 双屏联动配置应用失败")
        else:
            print("❌ 创建双屏联动配置失败")
    except Exception as e:
        print(f"❌ 双屏联动配置操作失败: {str(e)}")
    
    time.sleep(2)
    
    # 查看活动显示状态
    print("\n📊 当前活动显示状态:")
    try:
        response = requests.get(f"{api_url}/displays/active", 
                              params={'classroom_id': classroom_id})
        if response.status_code == 200:
            displays = response.json()['displays']
            if displays:
                for i, display in enumerate(displays, 1):
                    config = display['config']
                    print(f"  {i}. 配置: {config['mode']} - {config['layout']}")
                    print(f"     目标设备: {', '.join(config['target_devices'])}")
                    print(f"     状态: {display['status']}")
            else:
                print("  暂无活动显示")
        else:
            print("  获取活动显示状态失败")
    except Exception as e:
        print(f"  获取状态失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🎉 多屏显示控制系统演示完成！")
    print("=" * 60)
    print("\n💡 系统功能特点:")
    print("  ✅ 支持多种显示模式：广播、独立、小组广播、对比、双屏联动")
    print("  ✅ 支持多种布局：单屏、双屏、四分屏、网格布局")
    print("  ✅ 分组计时器：支持启动、暂停、恢复、停止操作")
    print("  ✅ 讨论主题分发：支持文字和图片内容")
    print("  ✅ 实时状态监控：显示配置状态和设备连接情况")
    print("  ✅ 同步机制：确保多设备内容同步显示")

def main():
    """主函数"""
    try:
        demo_display_system()
    except KeyboardInterrupt:
        print("\n\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中发生错误: {str(e)}")

if __name__ == '__main__':
    main()