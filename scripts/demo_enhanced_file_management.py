#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强文件管理系统演示脚本
展示所有新增的文件管理功能
"""

import os
import sys
import json
import time
import requests
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

class FileManagementDemo:
    """文件管理系统演示类"""
    
    def __init__(self, base_url='http://localhost:5000'):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/files"
        self.session = requests.Session()
        
        # 演示用的测试数据
        self.teacher_id = 'demo_teacher_001'
        self.student_id = 'demo_student_001'
        self.classroom_id = 'demo_classroom_001'
        self.group_ids = ['demo_group_001', 'demo_group_002']
        
        print("🚀 增强文件管理系统演示")
        print("=" * 50)
    
    def demo_file_upload(self):
        """演示文件上传"""
        print("\n📁 1. 文件上传演示")
        print("-" * 30)
        
        # 创建演示文件
        demo_content = """
# 智慧课堂系统演示文档

这是一个演示文档，用于展示增强的文件管理功能。

## 功能特性
- 文件版本管理
- 分块上传支持
- 文件预览功能
- 权限控制系统
- 访问审计日志
- 智能标签分类
- 批量操作支持
- 高级分发功能

## 使用场景
1. 教师上传课件资料
2. 学生提交作业文件
3. 小组协作文档共享
4. 课堂资源分发管理
        """.strip()
        
        # 模拟文件上传
        files = {
            'file': ('demo_document.md', demo_content.encode(), 'text/markdown')
        }
        data = {
            'uploader_id': self.teacher_id,
            'classroom_id': self.classroom_id,
            'description': '演示文档 - 智慧课堂系统功能介绍'
        }
        
        try:
            response = self.session.post(f"{self.api_base}/upload", files=files, data=data)
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    self.demo_file_id = result['file']['id']
                    print(f"✅ 文件上传成功")
                    print(f"   文件ID: {self.demo_file_id}")
                    print(f"   文件名: {result['file']['original_filename']}")
                    print(f"   文件大小: {result['file']['file_size']} 字节")
                    return True
                else:
                    print(f"❌ 上传失败: {result['message']}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"❌ 上传异常: {str(e)}")
            # 使用模拟文件ID继续演示
            self.demo_file_id = 'demo_file_001'
            print(f"🔄 使用模拟文件ID继续演示: {self.demo_file_id}")
        
        return False
    
    def demo_file_versioning(self):
        """演示文件版本管理"""
        print("\n📚 2. 文件版本管理演示")
        print("-" * 30)
        
        try:
            # 获取文件版本历史
            response = self.session.get(f"{self.api_base}/{self.demo_file_id}/versions")
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    versions = result['versions']
                    print(f"✅ 获取版本历史成功")
                    print(f"   当前版本数: {len(versions)}")
                    
                    for version in versions:
                        print(f"   版本 {version['version_number']}: {version.get('version_note', '无备注')}")
                        print(f"     创建时间: {version['created_at']}")
                        print(f"     文件大小: {version['file_size']} 字节")
                else:
                    print(f"❌ 获取版本失败: {result['message']}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"❌ 版本管理异常: {str(e)}")
    
    def demo_chunked_upload(self):
        """演示分块上传"""
        print("\n⬆️ 3. 分块上传演示")
        print("-" * 30)
        
        try:
            # 初始化分块上传
            init_data = {
                'filename': 'large_demo_file.pdf',
                'total_size': 5242880,  # 5MB
                'total_chunks': 5,
                'uploader_id': self.teacher_id,
                'classroom_id': self.classroom_id
            }
            
            response = self.session.post(f"{self.api_base}/chunk/init", json=init_data)
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    upload_session = result['upload_session']
                    upload_id = upload_session['upload_id']
                    
                    print(f"✅ 分块上传初始化成功")
                    print(f"   上传ID: {upload_id}")
                    print(f"   块大小: {upload_session['chunk_size']} 字节")
                    print(f"   上传URL: {upload_session['upload_url']}")
                    
                    # 模拟检查上传状态
                    status_response = self.session.get(f"{self.api_base}/chunk/{upload_id}/status")
                    if status_response.status_code == 200:
                        status_result = status_response.json()
                        if status_result['success']:
                            status = status_result['status']
                            print(f"   上传状态: {status['status']}")
                            print(f"   进度: {status['progress']:.1f}%")
                else:
                    print(f"❌ 初始化失败: {result['message']}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"❌ 分块上传异常: {str(e)}")
    
    def demo_file_preview(self):
        """演示文件预览"""
        print("\n👁️ 4. 文件预览演示")
        print("-" * 30)
        
        try:
            response = self.session.get(f"{self.api_base}/{self.demo_file_id}/preview")
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    preview = result['preview']
                    print(f"✅ 文件预览生成成功")
                    print(f"   支持预览: {preview['supported']}")
                    
                    if preview['supported']:
                        print(f"   预览类型: {preview.get('preview_type', 'unknown')}")
                        if 'preview_content' in preview:
                            content = preview['preview_content'][:200]
                            print(f"   预览内容: {content}...")
                        if 'preview_url' in preview:
                            print(f"   预览URL: {preview['preview_url']}")
                    else:
                        print(f"   不支持原因: {preview.get('message', '未知')}")
                else:
                    print(f"❌ 预览生成失败: {result['message']}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"❌ 文件预览异常: {str(e)}")
    
    def demo_file_permissions(self):
        """演示文件权限管理"""
        print("\n🔐 5. 文件权限管理演示")
        print("-" * 30)
        
        try:
            # 设置文件权限
            permission_data = {
                'user_id': self.student_id,
                'permissions': {
                    'read': True,
                    'write': False,
                    'delete': False,
                    'share': True
                }
            }
            
            response = self.session.post(f"{self.api_base}/{self.demo_file_id}/permissions", 
                                       json=permission_data)
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    print(f"✅ 权限设置成功")
                    print(f"   用户: {self.student_id}")
                    print(f"   读取权限: {permission_data['permissions']['read']}")
                    print(f"   写入权限: {permission_data['permissions']['write']}")
                    print(f"   删除权限: {permission_data['permissions']['delete']}")
                    print(f"   分享权限: {permission_data['permissions']['share']}")
                else:
                    print(f"❌ 权限设置失败: {result['message']}")
            
            # 检查权限
            check_data = {
                'user_id': self.student_id,
                'action': 'read'
            }
            
            response = self.session.post(f"{self.api_base}/{self.demo_file_id}/check-permission",
                                       json=check_data)
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    print(f"✅ 权限检查完成")
                    print(f"   用户 {self.student_id} 读取权限: {result['has_permission']}")
                
        except Exception as e:
            print(f"❌ 权限管理异常: {str(e)}")
    
    def demo_access_audit(self):
        """演示访问审计"""
        print("\n📊 6. 访问审计演示")
        print("-" * 30)
        
        try:
            # 获取访问日志
            response = self.session.get(f"{self.api_base}/{self.demo_file_id}/access-logs?limit=10")
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    logs = result['logs']
                    print(f"✅ 获取访问日志成功")
                    print(f"   日志条数: {len(logs)}")
                    
                    for log in logs[:3]:  # 显示前3条
                        print(f"   {log['timestamp']}: {log['user_id']} - {log['action']}")
                        if log.get('ip_address'):
                            print(f"     IP地址: {log['ip_address']}")
            
            # 获取访问统计
            response = self.session.get(f"{self.api_base}/{self.demo_file_id}/access-stats")
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    stats = result['statistics']
                    print(f"✅ 获取访问统计成功")
                    print(f"   总访问次数: {stats['total_access']}")
                    print(f"   按操作统计: {stats['by_action']}")
                    print(f"   最后访问: {stats.get('last_access', '无')}")
                
        except Exception as e:
            print(f"❌ 访问审计异常: {str(e)}")
    
    def demo_file_tagging(self):
        """演示文件标签管理"""
        print("\n🏷️ 7. 文件标签管理演示")
        print("-" * 30)
        
        try:
            # 添加标签
            tags_to_add = [
                {'tag_name': '课件', 'category': 'type'},
                {'tag_name': '数学', 'category': 'subject'},
                {'tag_name': '重要', 'category': 'priority'}
            ]
            
            for tag_data in tags_to_add:
                response = self.session.post(f"{self.api_base}/{self.demo_file_id}/tags",
                                           json=tag_data)
                if response.status_code == 200:
                    result = response.json()
                    if result['success']:
                        print(f"✅ 添加标签成功: {tag_data['tag_name']}")
                    else:
                        print(f"⚠️ 标签可能已存在: {tag_data['tag_name']}")
            
            # 获取文件标签
            response = self.session.get(f"{self.api_base}/{self.demo_file_id}/tags")
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    tags = result['tags']
                    print(f"✅ 获取文件标签成功")
                    print(f"   标签数量: {len(tags)}")
                    for tag in tags:
                        print(f"   - {tag['tag_name']} ({tag.get('category', '无分类')})")
            
            # 自动分类
            response = self.session.post(f"{self.api_base}/{self.demo_file_id}/auto-classify")
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    suggested_tags = result['suggested_tags']
                    print(f"✅ 自动分类完成")
                    print(f"   建议标签: {', '.join(suggested_tags)}")
            
            # 根据标签搜索
            search_data = {
                'tags': ['课件'],
                'classroom_id': self.classroom_id
            }
            
            response = self.session.post(f"{self.api_base}/search-by-tags", json=search_data)
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    files = result['files']
                    print(f"✅ 标签搜索完成")
                    print(f"   找到文件: {len(files)} 个")
                
        except Exception as e:
            print(f"❌ 标签管理异常: {str(e)}")
    
    def demo_batch_operations(self):
        """演示批量操作"""
        print("\n⚡ 8. 批量操作演示")
        print("-" * 30)
        
        try:
            # 批量添加标签
            batch_tag_data = {
                'file_ids': [self.demo_file_id],
                'tags': ['批量测试', '演示文档'],
                'user_id': self.teacher_id
            }
            
            response = self.session.post(f"{self.api_base}/batch/tag", json=batch_tag_data)
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    batch_id = result['batch_id']
                    print(f"✅ 批量标签操作成功")
                    print(f"   批次ID: {batch_id}")
                    print(f"   操作结果: {result['result']}")
                    
                    # 检查批量操作状态
                    status_response = self.session.get(f"{self.api_base}/batch/{batch_id}/status")
                    if status_response.status_code == 200:
                        status_result = status_response.json()
                        if status_result['success']:
                            status = status_result['status']
                            print(f"   操作状态: {status['status']}")
                            print(f"   完成时间: {status.get('completed_at', '进行中')}")
            
            # 批量压缩（演示）
            compress_data = {
                'file_ids': [self.demo_file_id],
                'archive_name': 'demo_archive',
                'user_id': self.teacher_id
            }
            
            response = self.session.post(f"{self.api_base}/batch/compress", json=compress_data)
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    print(f"✅ 批量压缩操作成功")
                    print(f"   压缩结果: {result['result']}")
                
        except Exception as e:
            print(f"❌ 批量操作异常: {str(e)}")
    
    def demo_file_distribution(self):
        """演示文件分发"""
        print("\n📤 9. 文件分发演示")
        print("-" * 30)
        
        try:
            # 高级文件分发
            distribution_data = {
                'file_ids': [self.demo_file_id],
                'target_type': 'group',
                'target_ids': self.group_ids,
                'distributor_id': self.teacher_id,
                'distribution_note': '演示文档分发给各小组'
            }
            
            response = self.session.post(f"{self.api_base}/distribute/advanced", 
                                       json=distribution_data)
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    distribution_id = result['distribution_id']
                    print(f"✅ 文件分发成功")
                    print(f"   分发ID: {distribution_id}")
                    print(f"   目标类型: {distribution_data['target_type']}")
                    print(f"   目标数量: {len(distribution_data['target_ids'])}")
                    print(f"   分发结果: {result['result']}")
                    
                    # 检查分发状态
                    time.sleep(1)  # 等待一秒
                    status_response = self.session.get(f"{self.api_base}/distribute/{distribution_id}/status")
                    if status_response.status_code == 200:
                        status_result = status_response.json()
                        if status_result['success']:
                            status = status_result['status']
                            print(f"   分发状态: {status['status']}")
                            print(f"   完成时间: {status.get('completed_at', '进行中')}")
            
            # 获取分发历史
            response = self.session.get(f"{self.api_base}/distribute/history?distributor_id={self.teacher_id}&limit=5")
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    history = result['history']
                    print(f"✅ 获取分发历史成功")
                    print(f"   历史记录数: {len(history)}")
                    
                    for record in history[:2]:  # 显示前2条
                        print(f"   {record['created_at']}: {record['target_type']} - {record['status']}")
                
        except Exception as e:
            print(f"❌ 文件分发异常: {str(e)}")
    
    def demo_advanced_features(self):
        """演示高级功能"""
        print("\n🎯 10. 高级功能演示")
        print("-" * 30)
        
        try:
            # 获取热门标签
            response = self.session.get(f"{self.api_base}/popular-tags?classroom_id={self.classroom_id}&limit=10")
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    tags = result['tags']
                    print(f"✅ 获取热门标签成功")
                    print(f"   热门标签数: {len(tags)}")
                    for tag in tags[:5]:
                        print(f"   - {tag['tag_name']}: {tag['count']} 次使用")
            
            # 获取文件统计
            response = self.session.get(f"{self.api_base}/stats?classroom_id={self.classroom_id}")
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    stats = result['stats']
                    print(f"✅ 获取文件统计成功")
                    print(f"   总文件数: {stats['total_count']}")
                    print(f"   总存储大小: {stats['total_size']} 字节")
                    print(f"   按类型分布: {stats['by_type']}")
                
        except Exception as e:
            print(f"❌ 高级功能异常: {str(e)}")
    
    def run_full_demo(self):
        """运行完整演示"""
        print("开始完整的文件管理系统演示...")
        
        # 按顺序执行所有演示
        demos = [
            self.demo_file_upload,
            self.demo_file_versioning,
            self.demo_chunked_upload,
            self.demo_file_preview,
            self.demo_file_permissions,
            self.demo_access_audit,
            self.demo_file_tagging,
            self.demo_batch_operations,
            self.demo_file_distribution,
            self.demo_advanced_features
        ]
        
        for i, demo_func in enumerate(demos, 1):
            try:
                demo_func()
                time.sleep(0.5)  # 短暂暂停，便于观察
            except Exception as e:
                print(f"❌ 演示 {i} 执行异常: {str(e)}")
                continue
        
        print("\n" + "=" * 50)
        print("🎉 增强文件管理系统演示完成！")
        print("\n📋 功能总结:")
        print("✅ 文件版本管理 - 支持版本历史和恢复")
        print("✅ 分块上传 - 支持大文件和断点续传")
        print("✅ 文件预览 - 支持多种格式在线预览")
        print("✅ 权限控制 - 细粒度权限管理")
        print("✅ 访问审计 - 完整的操作日志记录")
        print("✅ 智能标签 - 自动分类和标签管理")
        print("✅ 批量操作 - 高效的批处理功能")
        print("✅ 高级分发 - 灵活的文件分发机制")
        print("\n🚀 系统已准备就绪，可以投入使用！")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='增强文件管理系统演示')
    parser.add_argument('--url', default='http://localhost:5000', 
                       help='API服务器地址 (默认: http://localhost:5000)')
    parser.add_argument('--demo', choices=[
        'upload', 'version', 'chunk', 'preview', 'permission', 
        'audit', 'tag', 'batch', 'distribute', 'advanced', 'all'
    ], default='all', help='要运行的演示模块')
    
    args = parser.parse_args()
    
    demo = FileManagementDemo(args.url)
    
    if args.demo == 'all':
        demo.run_full_demo()
    else:
        demo_methods = {
            'upload': demo.demo_file_upload,
            'version': demo.demo_file_versioning,
            'chunk': demo.demo_chunked_upload,
            'preview': demo.demo_file_preview,
            'permission': demo.demo_file_permissions,
            'audit': demo.demo_access_audit,
            'tag': demo.demo_file_tagging,
            'batch': demo.demo_batch_operations,
            'distribute': demo.demo_file_distribution,
            'advanced': demo.demo_advanced_features
        }
        
        if args.demo in demo_methods:
            demo_methods[args.demo]()
        else:
            print(f"❌ 未知的演示模块: {args.demo}")


if __name__ == '__main__':
    main()