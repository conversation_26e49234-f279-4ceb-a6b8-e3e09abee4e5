#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统更新管理器
Smart Classroom System Update Manager
"""

import os
import sys
import json
import shutil
import subprocess
import requests
import hashlib
import tempfile
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import argparse
import logging


class SystemUpdater:
    """系统更新管理器"""
    
    def __init__(self):
        self.install_dir = Path("/opt/smart-classroom")
        self.backup_dir = Path("/backup/smart-classroom/updates")
        self.config_dir = self.install_dir / "config"
        self.version_file = self.install_dir / "VERSION"
        self.update_log = Path("/var/log/smart-classroom/updates.log")
        
        # 更新服务器配置
        self.update_server = "https://updates.smartclassroom.com"
        self.current_version = self.get_current_version()
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志记录"""
        self.update_log.parent.mkdir(parents=True, exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.update_log),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def get_current_version(self) -> str:
        """获取当前系统版本"""
        try:
            if self.version_file.exists():
                return self.version_file.read_text().strip()
            else:
                # 从setup.py获取版本
                setup_file = self.install_dir / "setup.py"
                if setup_file.exists():
                    with open(setup_file, 'r', encoding='utf-8') as f:
                        for line in f:
                            if 'version=' in line:
                                version = line.split('"')[1]
                                return version
                return "1.0.0"
        except Exception as e:
            self.logger.warning(f"无法获取当前版本: {e}")
            return "unknown"
    
    def check_for_updates(self) -> Optional[Dict]:
        """检查可用更新"""
        try:
            self.logger.info("检查系统更新...")
            
            # 构建请求参数
            params = {
                'current_version': self.current_version,
                'system_info': self.get_system_info()
            }
            
            # 请求更新信息
            response = requests.get(
                f"{self.update_server}/api/check-updates",
                params=params,
                timeout=30
            )
            
            if response.status_code == 200:
                update_info = response.json()
                if update_info.get('has_update'):
                    self.logger.info(f"发现新版本: {update_info['latest_version']}")
                    return update_info
                else:
                    self.logger.info("系统已是最新版本")
                    return None
            else:
                self.logger.error(f"检查更新失败: HTTP {response.status_code}")
                return None
                
        except requests.RequestException as e:
            self.logger.error(f"网络请求失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"检查更新时发生错误: {e}")
            return None
    
    def get_system_info(self) -> Dict:
        """获取系统信息"""
        import platform
        
        return {
            'os': platform.system(),
            'os_version': platform.release(),
            'architecture': platform.machine(),
            'python_version': platform.python_version(),
            'hostname': platform.node()
        }
    
    def download_update(self, update_info: Dict) -> Optional[Path]:
        """下载更新包"""
        try:
            download_url = update_info['download_url']
            expected_hash = update_info['file_hash']
            file_size = update_info['file_size']
            
            self.logger.info(f"下载更新包: {download_url}")
            
            # 创建临时文件
            temp_file = Path(tempfile.mktemp(suffix='.tar.gz'))
            
            # 下载文件
            response = requests.get(download_url, stream=True, timeout=300)
            response.raise_for_status()
            
            downloaded_size = 0
            with open(temp_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 显示进度
                        if file_size > 0:
                            progress = (downloaded_size / file_size) * 100
                            print(f"\r下载进度: {progress:.1f}%", end='', flush=True)
            
            print()  # 换行
            
            # 验证文件哈希
            if self.verify_file_hash(temp_file, expected_hash):
                self.logger.info("更新包下载并验证成功")
                return temp_file
            else:
                self.logger.error("更新包哈希验证失败")
                temp_file.unlink()
                return None
                
        except Exception as e:
            self.logger.error(f"下载更新包失败: {e}")
            return None
    
    def verify_file_hash(self, file_path: Path, expected_hash: str) -> bool:
        """验证文件哈希"""
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            
            actual_hash = sha256_hash.hexdigest()
            return actual_hash == expected_hash
            
        except Exception as e:
            self.logger.error(f"哈希验证失败: {e}")
            return False
    
    def create_backup(self) -> bool:
        """创建系统备份"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = self.backup_dir / f"backup_{timestamp}"
            backup_path.mkdir(parents=True, exist_ok=True)
            
            self.logger.info(f"创建系统备份: {backup_path}")
            
            # 停止服务
            self.stop_services()
            
            # 备份关键目录
            backup_items = [
                (self.install_dir, backup_path / "install"),
                (Path("/var/lib/smart-classroom"), backup_path / "data"),
                (Path("/var/log/smart-classroom"), backup_path / "logs")
            ]
            
            for src, dst in backup_items:
                if src.exists():
                    if src.is_dir():
                        shutil.copytree(src, dst, ignore=shutil.ignore_patterns('venv', '.git', '__pycache__'))
                    else:
                        shutil.copy2(src, dst)
            
            # 保存版本信息
            version_info = {
                'version': self.current_version,
                'backup_time': timestamp,
                'system_info': self.get_system_info()
            }
            
            with open(backup_path / "version_info.json", 'w', encoding='utf-8') as f:
                json.dump(version_info, f, indent=2, ensure_ascii=False)
            
            self.logger.info("系统备份创建成功")
            return True
            
        except Exception as e:
            self.logger.error(f"创建备份失败: {e}")
            return False
    
    def stop_services(self):
        """停止系统服务"""
        services = ["smart-classroom-backend", "mediamtx"]
        
        for service in services:
            try:
                subprocess.run(["systemctl", "stop", service], check=True, capture_output=True)
                self.logger.info(f"停止服务: {service}")
            except subprocess.CalledProcessError as e:
                self.logger.warning(f"停止服务失败 {service}: {e}")
    
    def start_services(self):
        """启动系统服务"""
        services = ["mediamtx", "smart-classroom-backend"]
        
        for service in services:
            try:
                subprocess.run(["systemctl", "start", service], check=True, capture_output=True)
                self.logger.info(f"启动服务: {service}")
            except subprocess.CalledProcessError as e:
                self.logger.error(f"启动服务失败 {service}: {e}")
    
    def apply_update(self, update_file: Path, update_info: Dict) -> bool:
        """应用更新"""
        try:
            self.logger.info("开始应用更新...")
            
            # 解压更新包
            temp_dir = Path(tempfile.mkdtemp())
            subprocess.run(["tar", "-xzf", str(update_file), "-C", str(temp_dir)], check=True)
            
            # 查找更新内容
            update_content = None
            for item in temp_dir.iterdir():
                if item.is_dir():
                    update_content = item
                    break
            
            if not update_content:
                self.logger.error("更新包格式错误")
                return False
            
            # 执行预更新脚本
            pre_update_script = update_content / "scripts" / "pre_update.sh"
            if pre_update_script.exists():
                self.logger.info("执行预更新脚本...")
                subprocess.run(["bash", str(pre_update_script)], check=True)
            
            # 更新文件
            self.logger.info("更新系统文件...")
            self.update_files(update_content)
            
            # 更新Python依赖
            self.update_dependencies(update_content)
            
            # 执行数据库迁移
            self.migrate_database(update_content)
            
            # 更新配置文件
            self.update_configuration(update_content, update_info)
            
            # 执行后更新脚本
            post_update_script = update_content / "scripts" / "post_update.sh"
            if post_update_script.exists():
                self.logger.info("执行后更新脚本...")
                subprocess.run(["bash", str(post_update_script)], check=True)
            
            # 更新版本信息
            self.update_version(update_info['latest_version'])
            
            # 清理临时文件
            shutil.rmtree(temp_dir)
            update_file.unlink()
            
            self.logger.info("更新应用成功")
            return True
            
        except Exception as e:
            self.logger.error(f"应用更新失败: {e}")
            return False
    
    def update_files(self, update_content: Path):
        """更新系统文件"""
        # 更新应用代码
        for item in update_content.iterdir():
            if item.name in ['scripts', 'config', 'migrations']:
                continue
                
            dest = self.install_dir / item.name
            
            if item.is_dir():
                if dest.exists():
                    shutil.rmtree(dest)
                shutil.copytree(item, dest)
            else:
                shutil.copy2(item, dest)
        
        # 设置权限
        shutil.chown(self.install_dir, "smartclass", "smartclass")
        for root, dirs, files in os.walk(self.install_dir):
            for d in dirs:
                shutil.chown(os.path.join(root, d), "smartclass", "smartclass")
            for f in files:
                shutil.chown(os.path.join(root, f), "smartclass", "smartclass")
    
    def update_dependencies(self, update_content: Path):
        """更新Python依赖"""
        requirements_file = update_content / "requirements.txt"
        if requirements_file.exists():
            self.logger.info("更新Python依赖...")
            
            venv_pip = self.install_dir / "venv" / "bin" / "pip"
            subprocess.run([
                str(venv_pip), "install", "-r", str(requirements_file)
            ], check=True)
    
    def migrate_database(self, update_content: Path):
        """执行数据库迁移"""
        migrations_dir = update_content / "migrations"
        if migrations_dir.exists():
            self.logger.info("执行数据库迁移...")
            
            # 执行迁移脚本
            for migration_file in sorted(migrations_dir.glob("*.py")):
                self.logger.info(f"执行迁移: {migration_file.name}")
                
                venv_python = self.install_dir / "venv" / "bin" / "python"
                subprocess.run([
                    str(venv_python), str(migration_file)
                ], cwd=str(self.install_dir), check=True)
    
    def update_configuration(self, update_content: Path, update_info: Dict):
        """更新配置文件"""
        config_updates = update_content / "config"
        if config_updates.exists():
            self.logger.info("更新配置文件...")
            
            for config_file in config_updates.iterdir():
                dest_config = self.config_dir / config_file.name
                
                if dest_config.exists():
                    # 备份现有配置
                    backup_config = dest_config.with_suffix(f"{dest_config.suffix}.backup")
                    shutil.copy2(dest_config, backup_config)
                
                # 合并配置（如果是YAML文件）
                if config_file.suffix in ['.yml', '.yaml']:
                    self.merge_yaml_config(config_file, dest_config)
                else:
                    shutil.copy2(config_file, dest_config)
    
    def merge_yaml_config(self, source: Path, dest: Path):
        """合并YAML配置文件"""
        import yaml
        
        try:
            # 读取源配置
            with open(source, 'r', encoding='utf-8') as f:
                new_config = yaml.safe_load(f)
            
            # 读取现有配置
            existing_config = {}
            if dest.exists():
                with open(dest, 'r', encoding='utf-8') as f:
                    existing_config = yaml.safe_load(f) or {}
            
            # 深度合并配置
            merged_config = self.deep_merge_dict(existing_config, new_config)
            
            # 写入合并后的配置
            with open(dest, 'w', encoding='utf-8') as f:
                yaml.dump(merged_config, f, default_flow_style=False, allow_unicode=True)
                
        except Exception as e:
            self.logger.error(f"合并YAML配置失败: {e}")
            # 如果合并失败，直接复制新配置
            shutil.copy2(source, dest)
    
    def deep_merge_dict(self, base: Dict, update: Dict) -> Dict:
        """深度合并字典"""
        result = base.copy()
        
        for key, value in update.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self.deep_merge_dict(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def update_version(self, new_version: str):
        """更新版本信息"""
        try:
            self.version_file.write_text(new_version)
            self.logger.info(f"版本更新为: {new_version}")
        except Exception as e:
            self.logger.error(f"更新版本信息失败: {e}")
    
    def rollback_update(self, backup_path: Path) -> bool:
        """回滚更新"""
        try:
            self.logger.info(f"开始回滚更新: {backup_path}")
            
            # 停止服务
            self.stop_services()
            
            # 恢复文件
            install_backup = backup_path / "install"
            if install_backup.exists():
                if self.install_dir.exists():
                    shutil.rmtree(self.install_dir)
                shutil.copytree(install_backup, self.install_dir)
            
            data_backup = backup_path / "data"
            data_dir = Path("/var/lib/smart-classroom")
            if data_backup.exists():
                if data_dir.exists():
                    shutil.rmtree(data_dir)
                shutil.copytree(data_backup, data_dir)
            
            # 启动服务
            self.start_services()
            
            self.logger.info("更新回滚成功")
            return True
            
        except Exception as e:
            self.logger.error(f"回滚更新失败: {e}")
            return False
    
    def list_backups(self) -> List[Dict]:
        """列出可用备份"""
        backups = []
        
        if not self.backup_dir.exists():
            return backups
        
        for backup_dir in self.backup_dir.iterdir():
            if backup_dir.is_dir() and backup_dir.name.startswith("backup_"):
                version_info_file = backup_dir / "version_info.json"
                if version_info_file.exists():
                    try:
                        with open(version_info_file, 'r', encoding='utf-8') as f:
                            version_info = json.load(f)
                        
                        backups.append({
                            'path': backup_dir,
                            'timestamp': version_info.get('backup_time'),
                            'version': version_info.get('version'),
                            'system_info': version_info.get('system_info')
                        })
                    except Exception as e:
                        self.logger.warning(f"读取备份信息失败 {backup_dir}: {e}")
        
        return sorted(backups, key=lambda x: x['timestamp'], reverse=True)
    
    def cleanup_old_backups(self, keep_count: int = 5):
        """清理旧备份"""
        backups = self.list_backups()
        
        if len(backups) > keep_count:
            for backup in backups[keep_count:]:
                try:
                    shutil.rmtree(backup['path'])
                    self.logger.info(f"删除旧备份: {backup['path']}")
                except Exception as e:
                    self.logger.warning(f"删除备份失败 {backup['path']}: {e}")
    
    def verify_system_integrity(self) -> bool:
        """验证系统完整性"""
        try:
            self.logger.info("验证系统完整性...")
            
            # 检查关键文件
            critical_files = [
                self.install_dir / "run.py",
                self.install_dir / "backend" / "__init__.py",
                self.config_dir / "app.yml"
            ]
            
            for file_path in critical_files:
                if not file_path.exists():
                    self.logger.error(f"关键文件缺失: {file_path}")
                    return False
            
            # 检查服务状态
            services = ["smart-classroom-backend", "mediamtx"]
            for service in services:
                try:
                    result = subprocess.run(
                        ["systemctl", "is-active", service],
                        capture_output=True, text=True
                    )
                    if result.stdout.strip() != "active":
                        self.logger.warning(f"服务未运行: {service}")
                except Exception as e:
                    self.logger.warning(f"检查服务状态失败 {service}: {e}")
            
            # 检查数据库连接
            try:
                venv_python = self.install_dir / "venv" / "bin" / "python"
                test_script = self.install_dir / "scripts" / "test_db_connection.py"
                if test_script.exists():
                    subprocess.run([str(venv_python), str(test_script)], check=True)
            except Exception as e:
                self.logger.warning(f"数据库连接测试失败: {e}")
            
            self.logger.info("系统完整性验证完成")
            return True
            
        except Exception as e:
            self.logger.error(f"系统完整性验证失败: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="智慧课堂系统更新管理器")
    parser.add_argument("--check", action="store_true", help="检查可用更新")
    parser.add_argument("--update", action="store_true", help="执行系统更新")
    parser.add_argument("--rollback", type=str, help="回滚到指定备份")
    parser.add_argument("--list-backups", action="store_true", help="列出可用备份")
    parser.add_argument("--cleanup", action="store_true", help="清理旧备份")
    parser.add_argument("--verify", action="store_true", help="验证系统完整性")
    parser.add_argument("--force", action="store_true", help="强制执行操作")
    
    args = parser.parse_args()
    
    # 检查权限
    if os.geteuid() != 0:
        print("错误: 需要root权限运行此脚本")
        sys.exit(1)
    
    updater = SystemUpdater()
    
    try:
        if args.check:
            # 检查更新
            update_info = updater.check_for_updates()
            if update_info:
                print(f"发现新版本: {update_info['latest_version']}")
                print(f"当前版本: {updater.current_version}")
                print(f"更新说明: {update_info.get('description', '无')}")
                print(f"文件大小: {update_info.get('file_size', 0) / 1024 / 1024:.1f} MB")
            else:
                print("系统已是最新版本")
        
        elif args.update:
            # 执行更新
            update_info = updater.check_for_updates()
            if not update_info:
                print("没有可用更新")
                return
            
            if not args.force:
                confirm = input(f"确认更新到版本 {update_info['latest_version']}? (y/N): ")
                if confirm.lower() != 'y':
                    print("更新已取消")
                    return
            
            # 创建备份
            if not updater.create_backup():
                print("创建备份失败，更新已取消")
                return
            
            # 下载更新
            update_file = updater.download_update(update_info)
            if not update_file:
                print("下载更新失败")
                return
            
            # 应用更新
            if updater.apply_update(update_file, update_info):
                print("更新成功完成")
                
                # 启动服务
                updater.start_services()
                
                # 验证系统
                if updater.verify_system_integrity():
                    print("系统验证通过")
                else:
                    print("警告: 系统验证失败，请检查日志")
                
                # 清理旧备份
                updater.cleanup_old_backups()
            else:
                print("更新失败")
        
        elif args.rollback:
            # 回滚更新
            backup_path = Path(args.rollback)
            if not backup_path.exists():
                print(f"备份路径不存在: {backup_path}")
                return
            
            if not args.force:
                confirm = input(f"确认回滚到备份 {backup_path.name}? (y/N): ")
                if confirm.lower() != 'y':
                    print("回滚已取消")
                    return
            
            if updater.rollback_update(backup_path):
                print("回滚成功完成")
            else:
                print("回滚失败")
        
        elif args.list_backups:
            # 列出备份
            backups = updater.list_backups()
            if backups:
                print("可用备份:")
                for backup in backups:
                    print(f"  {backup['timestamp']} - 版本 {backup['version']} - {backup['path']}")
            else:
                print("没有可用备份")
        
        elif args.cleanup:
            # 清理备份
            if not args.force:
                confirm = input("确认清理旧备份? (y/N): ")
                if confirm.lower() != 'y':
                    print("清理已取消")
                    return
            
            updater.cleanup_old_backups()
            print("备份清理完成")
        
        elif args.verify:
            # 验证系统
            if updater.verify_system_integrity():
                print("系统完整性验证通过")
            else:
                print("系统完整性验证失败")
        
        else:
            parser.print_help()
    
    except KeyboardInterrupt:
        print("\n操作已取消")
        sys.exit(1)
    except Exception as e:
        print(f"执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
