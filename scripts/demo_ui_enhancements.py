#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI增强功能演示脚本
展示主题切换、响应式设计、快捷键、手势操作等功能
"""

import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from PyQt5.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
        QPushButton, QLabel, QTextEdit, QComboBox, QSlider, QGroupBox,
        QTabWidget, QMessageBox, QProgressBar, QCheckBox
    )
    from PyQt5.QtCore import Qt, QTimer, pyqtSignal
    from PyQt5.QtGui import QFont, QColor
    
    PYQT5_AVAILABLE = True
except ImportError:
    PYQT5_AVAILABLE = False
    print("PyQt5 未安装，将使用模拟模式演示")

from shared.ui_themes import UITheme, theme_manager, AccessibilityHelper
from shared.ui_shortcuts import ShortcutManager, TeacherShortcuts
from shared.ui_responsive import ResponsiveWidget, MobileOptimizedWidget

class UIEnhancementDemo(QMainWindow if PYQT5_AVAILABLE else object):
    """UI增强功能演示窗口"""
    
    def __init__(self, use_gui=False):
        self.theme_manager = UITheme()
        self.accessibility_helper = AccessibilityHelper
        self.shortcut_manager = None
        
        if use_gui and PYQT5_AVAILABLE:
            super().__init__()
            try:
                self.init_ui()
                self.setup_shortcuts()
                self.setup_demo_timer()
            except Exception as e:
                print(f"GUI初始化失败: {e}")
                self.run_console_demo()
        else:
            self.run_console_demo()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("智慧课堂系统 - UI增强功能演示")
        self.setGeometry(100, 100, 1000, 700)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标题
        title_label = QLabel("智慧课堂系统 - UI增强功能演示")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 主题演示选项卡
        self.create_theme_demo_tab()
        
        # 响应式设计演示选项卡
        self.create_responsive_demo_tab()
        
        # 快捷键演示选项卡
        self.create_shortcuts_demo_tab()
        
        # 无障碍访问演示选项卡
        self.create_accessibility_demo_tab()
        
        main_layout.addWidget(self.tab_widget)
        
        # 创建状态栏
        self.status_label = QLabel("就绪")
        main_layout.addWidget(self.status_label)
        
        # 应用初始主题
        self.apply_current_theme()
    
    def create_theme_demo_tab(self):
        """创建主题演示选项卡"""
        theme_widget = QWidget()
        layout = QVBoxLayout(theme_widget)
        
        # 主题选择组
        theme_group = QGroupBox("主题选择")
        theme_layout = QVBoxLayout(theme_group)
        
        self.theme_combo = QComboBox()
        themes = self.theme_manager.get_available_themes()
        for theme_key, theme_name in themes:
            self.theme_combo.addItem(theme_name, theme_key)
        
        self.theme_combo.currentIndexChanged.connect(self.on_theme_changed)
        theme_layout.addWidget(self.theme_combo)
        
        layout.addWidget(theme_group)
        
        # 样式预览组
        preview_group = QGroupBox("样式预览")
        preview_layout = QVBoxLayout(preview_group)
        
        # 按钮样式预览
        button_layout = QHBoxLayout()
        
        self.primary_btn = QPushButton("主要按钮")
        self.success_btn = QPushButton("成功按钮")
        self.danger_btn = QPushButton("危险按钮")
        
        button_layout.addWidget(self.primary_btn)
        button_layout.addWidget(self.success_btn)
        button_layout.addWidget(self.danger_btn)
        
        preview_layout.addLayout(button_layout)
        
        # 文本预览
        self.preview_text = QTextEdit()
        self.preview_text.setPlainText(
            "这是主题预览文本。\n"
            "您可以看到不同主题下的文本显示效果。\n"
            "包括背景色、文字颜色、边框等样式的变化。"
        )
        self.preview_text.setMaximumHeight(100)
        preview_layout.addWidget(self.preview_text)
        
        layout.addWidget(preview_group)
        
        # 主题信息组
        info_group = QGroupBox("主题信息")
        info_layout = QVBoxLayout(info_group)
        
        self.theme_info_label = QLabel()
        self.update_theme_info()
        info_layout.addWidget(self.theme_info_label)
        
        layout.addWidget(info_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(theme_widget, "主题演示")
    
    def create_responsive_demo_tab(self):
        """创建响应式设计演示选项卡"""
        responsive_widget = ResponsiveWidget()
        layout = QVBoxLayout(responsive_widget)
        
        # 断点信息组
        breakpoint_group = QGroupBox("断点信息")
        breakpoint_layout = QVBoxLayout(breakpoint_group)
        
        self.breakpoint_label = QLabel()
        self.update_breakpoint_info()
        breakpoint_layout.addWidget(self.breakpoint_label)
        
        # 窗口大小滑块
        size_layout = QHBoxLayout()
        size_layout.addWidget(QLabel("模拟窗口宽度:"))
        
        self.width_slider = QSlider(Qt.Horizontal)
        self.width_slider.setRange(320, 1920)
        self.width_slider.setValue(1000)
        self.width_slider.valueChanged.connect(self.on_width_changed)
        size_layout.addWidget(self.width_slider)
        
        self.width_label = QLabel("1000px")
        size_layout.addWidget(self.width_label)
        
        breakpoint_layout.addLayout(size_layout)
        layout.addWidget(breakpoint_group)
        
        # 响应式内容演示
        content_group = QGroupBox("响应式内容")
        content_layout = QVBoxLayout(content_group)
        
        # 创建一些演示内容
        for i in range(3):
            demo_widget = QWidget()
            demo_layout = QHBoxLayout(demo_widget)
            
            demo_layout.addWidget(QLabel(f"内容项 {i+1}"))
            demo_layout.addWidget(QPushButton(f"按钮 {i+1}"))
            demo_layout.addStretch()
            
            content_layout.addWidget(demo_widget)
        
        layout.addWidget(content_group)
        
        # 连接响应式信号
        responsive_widget.breakpoint_changed.connect(self.on_breakpoint_changed)
        
        layout.addStretch()
        
        self.tab_widget.addTab(responsive_widget, "响应式设计")
    
    def create_shortcuts_demo_tab(self):
        """创建快捷键演示选项卡"""
        shortcuts_widget = QWidget()
        layout = QVBoxLayout(shortcuts_widget)
        
        # 快捷键列表组
        shortcuts_group = QGroupBox("可用快捷键")
        shortcuts_layout = QVBoxLayout(shortcuts_group)
        
        shortcuts_text = QTextEdit()
        shortcuts_text.setReadOnly(True)
        
        # 显示快捷键列表
        shortcuts_info = """
快捷键列表：

基本操作：
• F5 - 刷新所有数据
• Ctrl+S - 保存会话
• Ctrl+N - 新建会话
• Ctrl+, - 打开设置
• F11 - 切换全屏
• Ctrl+Q - 退出应用

设备管理：
• Ctrl+R - 刷新设备
• Ctrl+Shift+C - 连接所有设备
• Ctrl+Shift+D - 断开所有设备

小组管理：
• Ctrl+G - 随机分组
• Ctrl+Shift+G - 清除分组
• Ctrl+→ - 下一个小组
• Ctrl+← - 上一个小组

视频控制：
• Ctrl+B - 开始广播
• Ctrl+Shift+B - 停止广播
• Ctrl+V - 切换视频面板
• Ctrl+L - 切换视频布局

互动功能：
• Ctrl+Q - 开始答题
• Ctrl+Shift+Q - 结束答题
• Ctrl+Shift+R - 显示结果
• Ctrl+E - 导出结果
        """
        
        shortcuts_text.setPlainText(shortcuts_info)
        shortcuts_layout.addWidget(shortcuts_text)
        
        layout.addWidget(shortcuts_group)
        
        # 快捷键测试组
        test_group = QGroupBox("快捷键测试")
        test_layout = QVBoxLayout(test_group)
        
        self.shortcut_status_label = QLabel("按下快捷键进行测试...")
        test_layout.addWidget(self.shortcut_status_label)
        
        layout.addWidget(test_group)
        
        self.tab_widget.addTab(shortcuts_widget, "快捷键演示")
    
    def create_accessibility_demo_tab(self):
        """创建无障碍访问演示选项卡"""
        accessibility_widget = QWidget()
        layout = QVBoxLayout(accessibility_widget)
        
        # 无障碍功能组
        features_group = QGroupBox("无障碍功能")
        features_layout = QVBoxLayout(features_group)
        
        # 高对比度模式
        self.high_contrast_cb = QCheckBox("高对比度模式")
        self.high_contrast_cb.stateChanged.connect(self.on_high_contrast_changed)
        features_layout.addWidget(self.high_contrast_cb)
        
        # 大字体模式
        self.large_text_cb = QCheckBox("大字体模式")
        self.large_text_cb.stateChanged.connect(self.on_large_text_changed)
        features_layout.addWidget(self.large_text_cb)
        
        layout.addWidget(features_group)
        
        # 字体大小控制组
        font_group = QGroupBox("字体大小控制")
        font_layout = QVBoxLayout(font_group)
        
        font_size_layout = QHBoxLayout()
        font_size_layout.addWidget(QLabel("字体大小:"))
        
        self.font_size_slider = QSlider(Qt.Horizontal)
        self.font_size_slider.setRange(8, 24)
        self.font_size_slider.setValue(12)
        self.font_size_slider.valueChanged.connect(self.on_font_size_changed)
        font_size_layout.addWidget(self.font_size_slider)
        
        self.font_size_label = QLabel("12px")
        font_size_layout.addWidget(self.font_size_label)
        
        font_layout.addLayout(font_size_layout)
        layout.addWidget(font_group)
        
        # 无障碍信息组
        info_group = QGroupBox("无障碍信息")
        info_layout = QVBoxLayout(info_group)
        
        info_text = QTextEdit()
        info_text.setReadOnly(True)
        info_text.setPlainText(
            "无障碍访问功能说明：\n\n"
            "1. 高对比度模式：提高文字和背景的对比度，便于视力不佳的用户阅读。\n\n"
            "2. 大字体模式：增大界面字体，提高可读性。\n\n"
            "3. 键盘导航：支持Tab键在界面元素间切换，Enter键激活按钮。\n\n"
            "4. 屏幕阅读器支持：为重要元素添加ARIA标签，支持屏幕阅读器。\n\n"
            "5. 焦点指示器：清晰显示当前焦点位置。"
        )
        info_layout.addWidget(info_text)
        
        layout.addWidget(info_group)
        
        self.tab_widget.addTab(accessibility_widget, "无障碍访问")
    
    def setup_shortcuts(self):
        """设置快捷键"""
        if not PYQT5_AVAILABLE:
            return
        
        self.shortcut_manager = ShortcutManager(self)
        
        # 注册演示快捷键
        shortcuts = {
            'F5': self.on_refresh_demo,
            'Ctrl+T': self.on_toggle_theme_demo,
            'Ctrl+H': self.on_toggle_high_contrast_demo,
            'Ctrl+Plus': self.on_increase_font_demo,
            'Ctrl+Minus': self.on_decrease_font_demo,
            'F1': self.on_show_help_demo
        }
        
        for key, callback in shortcuts.items():
            self.shortcut_manager.register_shortcut(key, f"demo_{key}", callback)
    
    def setup_demo_timer(self):
        """设置演示定时器"""
        if not PYQT5_AVAILABLE:
            return
        
        self.demo_timer = QTimer()
        self.demo_timer.timeout.connect(self.update_demo_status)
        self.demo_timer.start(1000)  # 每秒更新一次
    
    def on_theme_changed(self, index):
        """主题变化处理"""
        theme_key = self.theme_combo.itemData(index)
        self.theme_manager.set_theme(theme_key)
        self.apply_current_theme()
        self.update_theme_info()
        self.update_status("主题已切换到: " + self.theme_combo.currentText())
    
    def apply_current_theme(self):
        """应用当前主题"""
        if not PYQT5_AVAILABLE:
            return
        
        # 应用按钮样式
        self.primary_btn.setStyleSheet(self.theme_manager.get_stylesheet('button'))
        self.success_btn.setStyleSheet(self.theme_manager.get_stylesheet('success_button'))
        self.danger_btn.setStyleSheet(self.theme_manager.get_stylesheet('danger_button'))
        
        # 应用面板样式
        self.tab_widget.setStyleSheet(self.theme_manager.get_stylesheet('tab_widget'))
        
        # 应用输入框样式
        self.preview_text.setStyleSheet(self.theme_manager.get_stylesheet('input'))
    
    def update_theme_info(self):
        """更新主题信息"""
        current_theme = self.theme_manager.get_current_theme()
        info_text = f"""
当前主题: {current_theme.get('name', '未知')}
主色调: {current_theme.get('primary_color', '#000000')}
背景色: {current_theme.get('background_color', '#ffffff')}
文字色: {current_theme.get('text_color', '#000000')}
成功色: {current_theme.get('success_color', '#00ff00')}
警告色: {current_theme.get('warning_color', '#ffff00')}
危险色: {current_theme.get('danger_color', '#ff0000')}
        """
        
        if PYQT5_AVAILABLE:
            self.theme_info_label.setText(info_text)
    
    def on_width_changed(self, width):
        """窗口宽度变化处理"""
        if PYQT5_AVAILABLE:
            self.width_label.setText(f"{width}px")
        
        # 模拟响应式设计
        if width <= 480:
            breakpoint = "xs (超小屏幕)"
        elif width <= 768:
            breakpoint = "sm (小屏幕)"
        elif width <= 1024:
            breakpoint = "md (中等屏幕)"
        elif width <= 1200:
            breakpoint = "lg (大屏幕)"
        else:
            breakpoint = "xl (超大屏幕)"
        
        self.update_status(f"当前断点: {breakpoint}")
    
    def update_breakpoint_info(self):
        """更新断点信息"""
        info_text = """
响应式断点说明：
• xs: ≤ 480px (超小屏幕 - 手机)
• sm: ≤ 768px (小屏幕 - 平板竖屏)
• md: ≤ 1024px (中等屏幕 - 平板横屏)
• lg: ≤ 1200px (大屏幕 - 桌面)
• xl: > 1200px (超大屏幕 - 大桌面)
        """
        
        if PYQT5_AVAILABLE:
            self.breakpoint_label.setText(info_text)
    
    def on_breakpoint_changed(self, old_bp, new_bp):
        """断点变化处理"""
        self.update_status(f"断点变化: {old_bp} → {new_bp}")
    
    def on_high_contrast_changed(self, state):
        """高对比度模式变化处理"""
        if state == Qt.Checked:
            if PYQT5_AVAILABLE:
                app = QApplication.instance()
                self.accessibility_helper.set_high_contrast_mode(app)
            self.theme_manager.set_theme('high_contrast')
        else:
            self.theme_manager.set_theme('default')
        
        self.apply_current_theme()
        self.update_status("高对比度模式: " + ("启用" if state == Qt.Checked else "禁用"))
    
    def on_large_text_changed(self, state):
        """大字体模式变化处理"""
        if state == Qt.Checked:
            if PYQT5_AVAILABLE:
                # 增大字体
                self.accessibility_helper.increase_font_size(self, 1.2)
        else:
            if PYQT5_AVAILABLE:
                # 恢复正常字体
                font = self.font()
                font.setPointSize(12)  # 恢复默认大小
                self.setFont(font)
        
        self.update_status("大字体模式: " + ("启用" if state == Qt.Checked else "禁用"))
    
    def on_font_size_changed(self, size):
        """字体大小变化处理"""
        if PYQT5_AVAILABLE:
            self.font_size_label.setText(f"{size}px")
            
            # 应用字体大小到整个应用
            font = self.font()
            font.setPointSize(size)
            self.setFont(font)
        
        self.update_status(f"字体大小: {size}px")
    
    # 快捷键回调方法
    def on_refresh_demo(self):
        """刷新演示"""
        self.update_status("快捷键触发: 刷新演示 (F5)")
        if PYQT5_AVAILABLE:
            self.shortcut_status_label.setText("快捷键触发: F5 - 刷新演示")
    
    def on_toggle_theme_demo(self):
        """切换主题演示"""
        current_index = self.theme_combo.currentIndex() if PYQT5_AVAILABLE else 0
        next_index = (current_index + 1) % self.theme_combo.count() if PYQT5_AVAILABLE else 0
        
        if PYQT5_AVAILABLE:
            self.theme_combo.setCurrentIndex(next_index)
            self.shortcut_status_label.setText("快捷键触发: Ctrl+T - 切换主题")
        
        self.update_status("快捷键触发: 切换主题 (Ctrl+T)")
    
    def on_toggle_high_contrast_demo(self):
        """切换高对比度演示"""
        if PYQT5_AVAILABLE:
            current_state = self.high_contrast_cb.isChecked()
            self.high_contrast_cb.setChecked(not current_state)
            self.shortcut_status_label.setText("快捷键触发: Ctrl+H - 切换高对比度")
        
        self.update_status("快捷键触发: 切换高对比度 (Ctrl+H)")
    
    def on_increase_font_demo(self):
        """增大字体演示"""
        if PYQT5_AVAILABLE:
            current_size = self.font_size_slider.value()
            new_size = min(current_size + 2, self.font_size_slider.maximum())
            self.font_size_slider.setValue(new_size)
            self.shortcut_status_label.setText("快捷键触发: Ctrl++ - 增大字体")
        
        self.update_status("快捷键触发: 增大字体 (Ctrl++)")
    
    def on_decrease_font_demo(self):
        """减小字体演示"""
        if PYQT5_AVAILABLE:
            current_size = self.font_size_slider.value()
            new_size = max(current_size - 2, self.font_size_slider.minimum())
            self.font_size_slider.setValue(new_size)
            self.shortcut_status_label.setText("快捷键触发: Ctrl+- - 减小字体")
        
        self.update_status("快捷键触发: 减小字体 (Ctrl+-)")
    
    def on_show_help_demo(self):
        """显示帮助演示"""
        if PYQT5_AVAILABLE:
            QMessageBox.information(
                self,
                "帮助",
                "智慧课堂系统 UI增强功能演示\n\n"
                "功能特性：\n"
                "• 多主题支持\n"
                "• 响应式设计\n"
                "• 快捷键操作\n"
                "• 无障碍访问\n"
                "• 手势操作支持\n\n"
                "按F1显示此帮助信息"
            )
            self.shortcut_status_label.setText("快捷键触发: F1 - 显示帮助")
        
        self.update_status("快捷键触发: 显示帮助 (F1)")
    
    def update_demo_status(self):
        """更新演示状态"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.update_status(f"演示运行中... {current_time}")
    
    def update_status(self, message):
        """更新状态信息"""
        if PYQT5_AVAILABLE:
            self.status_label.setText(f"状态: {message}")
        else:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    
    def run_console_demo(self):
        """运行控制台演示"""
        print("=" * 60)
        print("智慧课堂系统 - UI增强功能演示 (控制台模式)")
        print("=" * 60)
        
        # 演示主题功能
        print("\n1. 主题功能演示:")
        print("-" * 30)
        
        themes = self.theme_manager.get_available_themes()
        print(f"可用主题: {len(themes)}个")
        for theme_key, theme_name in themes:
            print(f"  • {theme_name} ({theme_key})")
        
        print(f"\n当前主题: {self.theme_manager.current_theme}")
        
        # 切换主题演示
        print("\n切换到深色主题...")
        self.theme_manager.set_theme('dark')
        current_theme = self.theme_manager.get_current_theme()
        print(f"主题已切换: {current_theme['name']}")
        print(f"主色调: {current_theme['primary_color']}")
        print(f"背景色: {current_theme['background_color']}")
        
        # 演示响应式设计
        print("\n2. 响应式设计演示:")
        print("-" * 30)
        
        # 模拟响应式断点检测
        breakpoints = {
            'xs': 480,
            'sm': 768,
            'md': 1024,
            'lg': 1200,
            'xl': 1920
        }
        
        test_widths = [400, 600, 900, 1100, 1500]
        for width in test_widths:
            if width <= breakpoints['xs']:
                breakpoint = 'xs'
            elif width <= breakpoints['sm']:
                breakpoint = 'sm'
            elif width <= breakpoints['md']:
                breakpoint = 'md'
            elif width <= breakpoints['lg']:
                breakpoint = 'lg'
            else:
                breakpoint = 'xl'
            
            print(f"宽度 {width}px -> 断点: {breakpoint}")
        
        # 演示快捷键功能
        print("\n3. 快捷键功能演示:")
        print("-" * 30)
        
        shortcuts = TeacherShortcuts.SHORTCUTS
        print(f"已定义快捷键: {len(shortcuts)}个")
        
        # 显示部分快捷键
        important_shortcuts = [
            'refresh_all', 'start_broadcast', 'start_quiz', 
            'random_grouping', 'toggle_fullscreen'
        ]
        
        for shortcut_name in important_shortcuts:
            if shortcut_name in shortcuts:
                print(f"  • {shortcut_name}: {shortcuts[shortcut_name]}")
        
        # 演示无障碍功能
        print("\n4. 无障碍功能演示:")
        print("-" * 30)
        
        print("启用高对比度模式...")
        print("高对比度模式: 已启用 (模拟)")
        
        print("启用大字体模式...")
        print("大字体模式: 已启用 (模拟)")
        
        # 演示样式表生成
        print("\n5. 样式表生成演示:")
        print("-" * 30)
        
        button_style = self.theme_manager.get_stylesheet('button')
        print("按钮样式表片段:")
        print(button_style[:200] + "..." if len(button_style) > 200 else button_style)
        
        print("\n演示完成!")
        print("=" * 60)

def main():
    """主函数"""
    # 强制使用控制台模式进行演示
    print("智慧课堂系统 - UI增强功能演示")
    print("由于环境限制，使用控制台模式演示")
    print()
    
    demo = UIEnhancementDemo()
    
    # 如果PyQt5可用，也可以尝试GUI模式
    if PYQT5_AVAILABLE and len(sys.argv) > 1 and sys.argv[1] == '--gui':
        try:
            app = QApplication(sys.argv)
            theme_manager.apply_theme_to_app(app)
            demo_gui = UIEnhancementDemo()
            demo_gui.show()
            sys.exit(app.exec_())
        except Exception as e:
            print(f"GUI模式失败: {e}")
            print("继续使用控制台模式")

if __name__ == '__main__':
    main()