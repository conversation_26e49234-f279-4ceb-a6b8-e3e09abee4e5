#!/bin/bash
# 智慧课堂系统服务启动脚本

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "智慧课堂系统服务启动脚本"
echo "项目目录: $PROJECT_DIR"

# 检查虚拟环境
if [ ! -d "$PROJECT_DIR/venv" ]; then
    echo "错误: 虚拟环境不存在，请先运行 install_dependencies.sh"
    exit 1
fi

# 激活虚拟环境
source "$PROJECT_DIR/venv/bin/activate"

# 检查MediaMTX
if ! command -v mediamtx &> /dev/null; then
    echo "警告: MediaMTX未安装，视频功能可能无法正常工作"
fi

# 创建日志目录
mkdir -p "$PROJECT_DIR/logs"

# 启动MediaMTX (后台运行)
echo "启动MediaMTX服务..."
if command -v mediamtx &> /dev/null; then
    mediamtx > "$PROJECT_DIR/logs/mediamtx.log" 2>&1 &
    MEDIAMTX_PID=$!
    echo "MediaMTX已启动 (PID: $MEDIAMTX_PID)"
else
    echo "跳过MediaMTX启动"
fi

# 启动Flask后端服务 (后台运行)
echo "启动Flask后端服务..."
cd "$PROJECT_DIR"
python backend/app.py > logs/backend.log 2>&1 &
BACKEND_PID=$!
echo "Flask后端已启动 (PID: $BACKEND_PID)"

# 等待服务启动
sleep 3

# 检查服务状态
echo "检查服务状态..."

# 检查后端服务
if curl -s http://localhost:5000/api/health > /dev/null; then
    echo "✓ Flask后端服务运行正常"
else
    echo "✗ Flask后端服务启动失败"
fi

# 保存PID到文件
echo "$BACKEND_PID" > "$PROJECT_DIR/logs/backend.pid"
if [ ! -z "$MEDIAMTX_PID" ]; then
    echo "$MEDIAMTX_PID" > "$PROJECT_DIR/logs/mediamtx.pid"
fi

echo ""
echo "服务启动完成！"
echo "后端服务地址: http://localhost:5000"
echo "学生端访问: http://localhost:5000/static/index.html"
echo ""
echo "启动客户端应用:"
echo "教师端: python teacher_client/main.py"
echo "小组端: python group_client/main.py"
echo ""
echo "停止服务: ./scripts/stop_services.sh"