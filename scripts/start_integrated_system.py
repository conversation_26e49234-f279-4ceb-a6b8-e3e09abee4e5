#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 集成系统启动脚本
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志"""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='[%(levelname)s] %(asctime)s - %(name)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_dir / "system_startup.log", encoding='utf-8')
        ]
    )
    
    return logging.getLogger(__name__)

def check_dependencies():
    """检查依赖"""
    logger = logging.getLogger(__name__)
    
    required_packages = [
        'flask', 'flask_socketio', 'sqlalchemy', 
        'psutil', 'yaml', 'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            logger.error(f"✗ {package} 未安装")
    
    if missing_packages:
        logger.error(f"缺少依赖包: {', '.join(missing_packages)}")
        logger.error("请运行: pip install -r requirements.txt")
        return False
    
    return True

def initialize_directories():
    """初始化目录结构"""
    logger = logging.getLogger(__name__)
    
    directories = [
        "logs",
        "config",
        "instance",
        "instance/uploads",
        "instance/exports",
        "instance/backups"
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"✓ 目录已创建: {directory}")

def start_system_services():
    """启动系统服务"""
    logger = logging.getLogger(__name__)
    
    try:
        # 导入系统服务
        from backend.services.logging_service import get_logging_service
        from backend.services.config_manager import get_config_manager
        from backend.services.performance_monitor import get_performance_monitor
        from backend.services.system_integration import get_integration_manager
        
        logger.info("正在启动系统服务...")
        
        # 1. 启动日志服务
        logger.info("启动日志服务...")
        logging_service = get_logging_service()
        logger.info("✓ 日志服务已启动")
        
        # 2. 初始化配置管理器
        logger.info("初始化配置管理器...")
        config_manager = get_config_manager()
        logger.info("✓ 配置管理器已初始化")
        
        # 3. 启动性能监控
        logger.info("启动性能监控...")
        performance_monitor = get_performance_monitor()
        performance_monitor.start_monitoring()
        logger.info("✓ 性能监控已启动")
        
        # 4. 启动系统集成管理器
        logger.info("启动系统集成管理器...")
        integration_manager = get_integration_manager()
        integration_manager.start_integration()
        logger.info("✓ 系统集成管理器已启动")
        
        # 等待服务稳定
        time.sleep(2)
        
        # 检查服务状态
        system_status = integration_manager.get_system_status()
        logger.info(f"系统运行时间: {system_status.get('uptime', 0):.1f}秒")
        
        services = system_status.get('services', {})
        running_services = sum(1 for s in services.values() if s.get('status') == 'running')
        logger.info(f"运行中的服务: {running_services}/{len(services)}")
        
        return True
        
    except Exception as e:
        logger.error(f"启动系统服务失败: {e}")
        return False

def start_flask_app():
    """启动Flask应用"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("正在启动Flask应用...")
        
        # 设置环境变量
        os.environ['FLASK_ENV'] = 'development'
        os.environ['FLASK_DEBUG'] = '1'
        
        # 导入Flask应用
        from backend.app import app, socketio
        
        logger.info("Flask应用已加载")
        
        # 启动应用
        logger.info("启动Web服务器...")
        logger.info("系统监控面板: http://localhost:5000/system-dashboard")
        logger.info("API文档: http://localhost:5000/api/health")
        
        socketio.run(
            app, 
            host='0.0.0.0', 
            port=5000, 
            debug=True,
            use_reloader=False  # 避免重复启动服务
        )
        
    except Exception as e:
        logger.error(f"启动Flask应用失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("智慧课堂系统 - 集成系统启动")
    print("=" * 60)
    
    # 设置日志
    logger = setup_logging()
    logger.info("系统启动开始")
    
    try:
        # 1. 检查依赖
        logger.info("检查系统依赖...")
        if not check_dependencies():
            logger.error("依赖检查失败，退出启动")
            return 1
        
        # 2. 初始化目录
        logger.info("初始化目录结构...")
        initialize_directories()
        
        # 3. 启动系统服务
        logger.info("启动系统服务...")
        if not start_system_services():
            logger.error("系统服务启动失败，退出启动")
            return 1
        
        # 4. 启动Flask应用
        logger.info("启动Web应用...")
        start_flask_app()
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭系统...")
        
        # 清理资源
        try:
            from backend.services.system_integration import get_integration_manager
            from backend.services.performance_monitor import get_performance_monitor
            from backend.services.logging_service import get_logging_service
            
            logger.info("正在停止系统服务...")
            
            integration_manager = get_integration_manager()
            integration_manager.stop_integration()
            
            performance_monitor = get_performance_monitor()
            performance_monitor.stop_monitoring()
            
            logging_service = get_logging_service()
            logging_service.stop()
            
            logger.info("系统服务已停止")
            
        except Exception as e:
            logger.error(f"清理资源时出错: {e}")
        
        logger.info("系统已关闭")
        return 0
        
    except Exception as e:
        logger.error(f"系统启动异常: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())