# -*- coding: utf-8 -*-
"""
设备发现演示脚本

这个脚本演示了如何使用设备发现协议在客户端和服务器之间进行通信。
可以同时运行多个客户端实例，模拟不同类型的设备。
"""

import sys
import os
import time
import argparse
import threading
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入设备发现客户端
from shared.device_discovery_client import DeviceDiscoveryClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def simulate_device_activity(client):
    """模拟设备活动"""
    logger.info(f"设备 {client.device_name} ({client.device_id}) 已启动")
    
    # 模拟设备活动
    count = 0
    try:
        while True:
            count += 1
            if count % 10 == 0:
                logger.info(f"设备 {client.device_name} 正在运行中...")
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info(f"设备 {client.device_name} 正在关闭...")
        client.stop()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='智慧课堂设备发现演示')
    parser.add_argument('--type', choices=['teacher', 'group', 'student'], default='student',
                        help='设备类型 (teacher, group, student)')
    parser.add_argument('--name', help='设备名称')
    parser.add_argument('--port', type=int, default=8080, help='设备服务端口')
    
    args = parser.parse_args()
    
    # 根据设备类型设置默认能力
    capabilities = []
    if args.type == 'teacher':
        capabilities = ['screen_share', 'remote_control', 'file_transfer', 'classroom_control']
    elif args.type == 'group':
        capabilities = ['screen_share', 'file_transfer', 'group_discussion']
    elif args.type == 'student':
        capabilities = ['screen_share', 'file_receive']
    
    # 创建设备名称（如果未提供）
    device_name = args.name
    if not device_name:
        import socket
        hostname = socket.gethostname()
        device_name = f"{args.type.capitalize()} - {hostname}"
    
    # 创建并启动设备发现客户端
    client = DeviceDiscoveryClient(
        device_type=args.type,
        device_name=device_name,
        port=args.port,
        capabilities=capabilities
    )
    
    # 启动客户端
    if client.start():
        # 模拟设备活动
        simulate_device_activity(client)
    else:
        logger.error("设备发现客户端启动失败")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())