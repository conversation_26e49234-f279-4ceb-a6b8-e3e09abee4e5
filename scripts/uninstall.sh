#!/bin/bash
# 智慧课堂系统卸载脚本
# Smart Classroom System Uninstall Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
INSTALL_DIR="/opt/smart-classroom"
SERVICE_USER="smartclass"
LOG_DIR="/var/log/smart-classroom"
DATA_DIR="/var/lib/smart-classroom"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 确认卸载
confirm_uninstall() {
    echo "⚠️  即将卸载智慧课堂系统"
    echo "这将删除以下内容："
    echo "  - 所有程序文件 ($INSTALL_DIR)"
    echo "  - 系统服务配置"
    echo "  - Nginx配置"
    echo "  - 桌面快捷方式"
    echo
    echo "以下内容将被保留："
    echo "  - 数据库文件 ($DATA_DIR)"
    echo "  - 日志文件 ($LOG_DIR)"
    echo
    read -p "确认继续卸载？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "取消卸载"
        exit 0
    fi
}

# 停止服务
stop_services() {
    log_info "停止系统服务..."
    
    services=("smart-classroom-backend" "mediamtx")
    
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "$service"; then
            systemctl stop "$service"
            log_success "停止服务: $service"
        fi
        
        if systemctl is-enabled --quiet "$service" 2>/dev/null; then
            systemctl disable "$service"
            log_success "禁用服务: $service"
        fi
    done
}

# 删除系统服务文件
remove_service_files() {
    log_info "删除系统服务文件..."
    
    service_files=(
        "/etc/systemd/system/smart-classroom-backend.service"
        "/etc/systemd/system/mediamtx.service"
    )
    
    for service_file in "${service_files[@]}"; do
        if [ -f "$service_file" ]; then
            rm -f "$service_file"
            log_success "删除服务文件: $service_file"
        fi
    done
    
    # 重新加载systemd
    systemctl daemon-reload
    log_success "重新加载systemd配置"
}

# 删除Nginx配置
remove_nginx_config() {
    log_info "删除Nginx配置..."
    
    # 删除站点配置
    if [ -f "/etc/nginx/sites-available/smart-classroom" ]; then
        rm -f "/etc/nginx/sites-available/smart-classroom"
        log_success "删除Nginx站点配置"
    fi
    
    if [ -L "/etc/nginx/sites-enabled/smart-classroom" ]; then
        rm -f "/etc/nginx/sites-enabled/smart-classroom"
        log_success "删除Nginx站点链接"
    fi
    
    # 重启Nginx（如果正在运行）
    if systemctl is-active --quiet nginx; then
        systemctl restart nginx
        log_success "重启Nginx服务"
    fi
}

# 删除程序文件
remove_program_files() {
    log_info "删除程序文件..."
    
    if [ -d "$INSTALL_DIR" ]; then
        rm -rf "$INSTALL_DIR"
        log_success "删除安装目录: $INSTALL_DIR"
    fi
}

# 删除MediaMTX
remove_mediamtx() {
    log_info "删除MediaMTX..."
    
    if [ -f "/usr/local/bin/mediamtx" ]; then
        rm -f "/usr/local/bin/mediamtx"
        log_success "删除MediaMTX可执行文件"
    fi
}

# 删除桌面快捷方式
remove_desktop_shortcuts() {
    log_info "删除桌面快捷方式..."
    
    shortcuts=(
        "/usr/share/applications/smart-classroom-teacher.desktop"
        "/usr/share/applications/smart-classroom-group.desktop"
    )
    
    for shortcut in "${shortcuts[@]}"; do
        if [ -f "$shortcut" ]; then
            rm -f "$shortcut"
            log_success "删除快捷方式: $(basename "$shortcut")"
        fi
    done
}

# 清理用户和权限
cleanup_user() {
    log_info "清理用户和权限..."
    
    # 询问是否删除用户
    read -p "是否删除系统用户 $SERVICE_USER？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if id "$SERVICE_USER" &>/dev/null; then
            userdel "$SERVICE_USER"
            log_success "删除用户: $SERVICE_USER"
        fi
    else
        log_info "保留用户: $SERVICE_USER"
    fi
}

# 清理数据和日志
cleanup_data() {
    log_info "清理数据和日志..."
    
    # 询问是否删除数据
    read -p "是否删除数据目录 $DATA_DIR？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if [ -d "$DATA_DIR" ]; then
            rm -rf "$DATA_DIR"
            log_success "删除数据目录: $DATA_DIR"
        fi
    else
        log_info "保留数据目录: $DATA_DIR"
    fi
    
    # 询问是否删除日志
    read -p "是否删除日志目录 $LOG_DIR？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if [ -d "$LOG_DIR" ]; then
            rm -rf "$LOG_DIR"
            log_success "删除日志目录: $LOG_DIR"
        fi
    else
        log_info "保留日志目录: $LOG_DIR"
    fi
}

# 卸载Python包
uninstall_python_packages() {
    log_info "卸载Python包..."
    
    # 检查是否有全局安装的包
    if pip3 list | grep -q "smart-classroom-system"; then
        read -p "检测到全局安装的Python包，是否卸载？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            pip3 uninstall -y smart-classroom-system
            log_success "卸载Python包"
        fi
    fi
}

# 显示卸载后信息
show_post_uninstall_info() {
    log_info "卸载后信息："
    echo
    echo "已删除的组件："
    echo "  ✅ 程序文件"
    echo "  ✅ 系统服务"
    echo "  ✅ Nginx配置"
    echo "  ✅ 桌面快捷方式"
    echo "  ✅ MediaMTX"
    echo
    echo "可能保留的组件："
    echo "  📁 数据目录: $DATA_DIR"
    echo "  📁 日志目录: $LOG_DIR"
    echo "  👤 系统用户: $SERVICE_USER"
    echo "  📦 系统依赖包"
    echo
    echo "如需完全清理，请手动删除保留的组件。"
    echo
    echo "如需重新安装，请运行安装脚本："
    echo "  sudo ./scripts/deploy.sh"
    echo "  或"
    echo "  sudo python3 ./scripts/install.py"
}

# 主卸载函数
main() {
    log_info "智慧课堂系统卸载程序"
    
    # 检查是否以root权限运行
    if [[ $EUID -ne 0 ]]; then
        log_error "请以root权限运行此脚本"
        exit 1
    fi
    
    # 确认卸载
    confirm_uninstall
    
    # 执行卸载步骤
    echo
    log_info "开始卸载智慧课堂系统..."
    
    stop_services
    remove_service_files
    remove_nginx_config
    remove_program_files
    remove_mediamtx
    remove_desktop_shortcuts
    uninstall_python_packages
    cleanup_user
    cleanup_data
    
    echo
    log_success "智慧课堂系统卸载完成！"
    show_post_uninstall_info
}

# 运行主函数
main "$@"