#!/bin/bash
# 智慧课堂系统服务停止脚本

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "停止智慧课堂系统服务..."

# 停止Flask后端服务
if [ -f "$PROJECT_DIR/logs/backend.pid" ]; then
    BACKEND_PID=$(cat "$PROJECT_DIR/logs/backend.pid")
    if ps -p $BACKEND_PID > /dev/null 2>&1; then
        echo "停止Flask后端服务 (PID: $BACKEND_PID)..."
        kill $BACKEND_PID
        rm -f "$PROJECT_DIR/logs/backend.pid"
        echo "✓ Flask后端服务已停止"
    else
        echo "Flask后端服务未运行"
        rm -f "$PROJECT_DIR/logs/backend.pid"
    fi
else
    echo "未找到Flask后端服务PID文件"
fi

# 停止MediaMTX服务
if [ -f "$PROJECT_DIR/logs/mediamtx.pid" ]; then
    MEDIAMTX_PID=$(cat "$PROJECT_DIR/logs/mediamtx.pid")
    if ps -p $MEDIAMTX_PID > /dev/null 2>&1; then
        echo "停止MediaMTX服务 (PID: $MEDIAMTX_PID)..."
        kill $MEDIAMTX_PID
        rm -f "$PROJECT_DIR/logs/mediamtx.pid"
        echo "✓ MediaMTX服务已停止"
    else
        echo "MediaMTX服务未运行"
        rm -f "$PROJECT_DIR/logs/mediamtx.pid"
    fi
else
    echo "未找到MediaMTX服务PID文件"
fi

# 清理可能残留的进程
echo "清理残留进程..."
pkill -f "python.*backend/app.py" 2>/dev/null || true
pkill -f "mediamtx" 2>/dev/null || true

echo "所有服务已停止"