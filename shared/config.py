#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 共享配置
"""

import os

class Config:
    """基础配置类"""
    
    # 应用配置
    APP_NAME = "智慧课堂系统"
    VERSION = "1.0.0"
    
    # 网络配置
    UDP_DISCOVERY_PORT = 8888
    BACKEND_HOST = "localhost"
    BACKEND_PORT = 5000
    
    # 视频配置
    MEDIAMTX_HOST = "localhost"
    MEDIAMTX_RTMP_PORT = 1935
    MEDIAMTX_HTTP_PORT = 8889
    
    # 视频质量配置
    VIDEO_RESOLUTION = "1920x1080"
    VIDEO_FPS = 30
    VIDEO_BITRATE = "2M"
    
    # 延迟要求
    SCREEN_SHARE_DELAY_MAX = 1.0  # 秒
    BROADCAST_DELAY_MAX = 1.5     # 秒
    
    # 并发配置
    MAX_TERMINALS = 75
    MAX_GROUPS = 8
    MAX_SCREEN_SHARES = 6
    
    # 数据库配置
    DATABASE_URL = "sqlite:///smart_classroom.db"
    
    # 文件配置
    UPLOAD_FOLDER = "uploads"
    MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
    ALLOWED_EXTENSIONS = {
        'image': ['jpg', 'jpeg', 'png', 'gif'],
        'document': ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'],
        'video': ['mp4', 'avi', 'mov', 'wmv'],
        'audio': ['mp3', 'wav', 'aac']
    }

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    TESTING = False

class TestingConfig(Config):
    """测试环境配置"""
    DEBUG = True
    TESTING = True
    DATABASE_URL = "sqlite:///test_smart_classroom.db"

# 根据环境变量选择配置
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config():
    """获取当前配置"""
    env = os.environ.get('FLASK_ENV', 'default')
    return config.get(env, config['default'])