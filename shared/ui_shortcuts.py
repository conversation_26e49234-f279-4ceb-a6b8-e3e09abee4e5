#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快捷键和手势操作管理
"""

from PyQt5.QtCore import Qt, QObject, pyqtSignal
from PyQt5.QtGui import Q<PERSON>eySequence
from PyQt5.QtWidgets import QShortcut, QApplication

class ShortcutManager(QObject):
    """快捷键管理器"""
    
    # 信号
    shortcut_triggered = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.shortcuts = {}
        self.parent_widget = parent
    
    def register_shortcut(self, key_sequence, action_name, callback=None):
        """注册快捷键"""
        if self.parent_widget:
            shortcut = QShortcut(QKeySequence(key_sequence), self.parent_widget)
            
            if callback:
                shortcut.activated.connect(callback)
            else:
                shortcut.activated.connect(lambda: self.shortcut_triggered.emit(action_name))
            
            self.shortcuts[action_name] = {
                'shortcut': shortcut,
                'key_sequence': key_sequence,
                'callback': callback
            }
            
            return True
        return False
    
    def unregister_shortcut(self, action_name):
        """注销快捷键"""
        if action_name in self.shortcuts:
            shortcut_info = self.shortcuts[action_name]
            shortcut_info['shortcut'].setEnabled(False)
            del self.shortcuts[action_name]
            return True
        return False
    
    def get_shortcuts(self):
        """获取所有快捷键"""
        return {name: info['key_sequence'] for name, info in self.shortcuts.items()}
    
    def enable_shortcut(self, action_name, enabled=True):
        """启用/禁用快捷键"""
        if action_name in self.shortcuts:
            self.shortcuts[action_name]['shortcut'].setEnabled(enabled)

class TeacherShortcuts:
    """教师端快捷键定义"""
    
    SHORTCUTS = {
        # 基本操作
        'refresh_all': 'F5',
        'save_session': 'Ctrl+S',
        'new_session': 'Ctrl+N',
        'open_settings': 'Ctrl+,',
        'toggle_fullscreen': 'F11',
        'quit_app': 'Ctrl+Q',
        
        # 设备管理
        'refresh_devices': 'Ctrl+R',
        'connect_all_devices': 'Ctrl+Shift+C',
        'disconnect_all_devices': 'Ctrl+Shift+D',
        
        # 小组管理
        'random_grouping': 'Ctrl+G',
        'clear_groups': 'Ctrl+Shift+G',
        'next_group': 'Ctrl+Right',
        'prev_group': 'Ctrl+Left',
        
        # 视频控制
        'start_broadcast': 'Ctrl+B',
        'stop_broadcast': 'Ctrl+Shift+B',
        'toggle_video_panel': 'Ctrl+V',
        'switch_video_layout': 'Ctrl+L',
        
        # 互动功能
        'start_quiz': 'Ctrl+Q',
        'end_quiz': 'Ctrl+Shift+Q',
        'show_results': 'Ctrl+Shift+R',
        'export_results': 'Ctrl+E',
        
        # 文件操作
        'open_file': 'Ctrl+O',
        'distribute_file': 'Ctrl+D',
        'show_file_manager': 'Ctrl+F',
        
        # 白板操作
        'toggle_whiteboard': 'Ctrl+W',
        'clear_whiteboard': 'Ctrl+Shift+W',
        'save_whiteboard': 'Ctrl+Shift+S',
        
        # 显示控制
        'toggle_display_mode': 'Ctrl+M',
        'start_timer': 'Ctrl+T',
        'stop_timer': 'Ctrl+Shift+T',
    }
    
    @classmethod
    def setup_shortcuts(cls, main_window):
        """为教师端主窗口设置快捷键"""
        shortcut_manager = ShortcutManager(main_window)
        
        for action, key_sequence in cls.SHORTCUTS.items():
            callback = getattr(main_window, f'on_{action}', None)
            shortcut_manager.register_shortcut(key_sequence, action, callback)
        
        return shortcut_manager

class GroupShortcuts:
    """小组端快捷键定义"""
    
    SHORTCUTS = {
        # 基本操作
        'refresh_all': 'F5',
        'toggle_fullscreen': 'F11',
        'quit_app': 'Ctrl+Q',
        'show_help': 'F1',
        
        # 视频操作
        'start_screen_share': 'Ctrl+Shift+S',
        'stop_screen_share': 'Ctrl+Shift+X',
        'toggle_camera': 'Ctrl+Shift+C',
        'toggle_microphone': 'Ctrl+Shift+M',
        
        # 白板操作
        'toggle_whiteboard': 'Ctrl+W',
        'clear_whiteboard': 'Ctrl+Shift+W',
        'undo_whiteboard': 'Ctrl+Z',
        'redo_whiteboard': 'Ctrl+Y',
        'save_whiteboard': 'Ctrl+S',
        
        # 文件操作
        'open_file': 'Ctrl+O',
        'upload_file': 'Ctrl+U',
        'download_file': 'Ctrl+D',
        
        # 录制操作
        'start_recording': 'Ctrl+R',
        'stop_recording': 'Ctrl+Shift+R',
        'pause_recording': 'Ctrl+P',
        
        # 模式切换
        'switch_mode': 'Ctrl+Tab',
        'toggle_collaboration': 'Ctrl+Shift+T',
    }
    
    @classmethod
    def setup_shortcuts(cls, main_window):
        """为小组端主窗口设置快捷键"""
        shortcut_manager = ShortcutManager(main_window)
        
        for action, key_sequence in cls.SHORTCUTS.items():
            callback = getattr(main_window, f'on_{action}', None)
            shortcut_manager.register_shortcut(key_sequence, action, callback)
        
        return shortcut_manager

class GestureManager(QObject):
    """手势操作管理器"""
    
    # 信号
    gesture_detected = pyqtSignal(str, dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.gestures = {}
        self.touch_points = []
        self.gesture_threshold = 50  # 像素
    
    def register_gesture(self, gesture_name, pattern):
        """注册手势"""
        self.gestures[gesture_name] = pattern
    
    def process_touch_event(self, event):
        """处理触摸事件"""
        # 简化的手势识别逻辑
        if event.type() == event.TouchBegin:
            self.touch_points = []
        elif event.type() == event.TouchUpdate:
            for touch_point in event.touchPoints():
                self.touch_points.append({
                    'x': touch_point.pos().x(),
                    'y': touch_point.pos().y(),
                    'timestamp': touch_point.timestamp()
                })
        elif event.type() == event.TouchEnd:
            self.analyze_gesture()
    
    def analyze_gesture(self):
        """分析手势"""
        if len(self.touch_points) < 2:
            return
        
        # 检测滑动手势
        start_point = self.touch_points[0]
        end_point = self.touch_points[-1]
        
        dx = end_point['x'] - start_point['x']
        dy = end_point['y'] - start_point['y']
        
        if abs(dx) > self.gesture_threshold or abs(dy) > self.gesture_threshold:
            if abs(dx) > abs(dy):
                # 水平滑动
                gesture = 'swipe_right' if dx > 0 else 'swipe_left'
            else:
                # 垂直滑动
                gesture = 'swipe_down' if dy > 0 else 'swipe_up'
            
            self.gesture_detected.emit(gesture, {
                'start': start_point,
                'end': end_point,
                'distance': (dx**2 + dy**2)**0.5
            })
        
        # 检测缩放手势
        if len(self.touch_points) >= 4:  # 至少两个触摸点的开始和结束
            # 简化的缩放检测
            self.gesture_detected.emit('pinch', {'points': self.touch_points})

def setup_global_shortcuts():
    """设置全局快捷键"""
    app = QApplication.instance()
    if not app:
        return
    
    # 全局快捷键（在应用程序级别）
    global_shortcuts = {
        'emergency_exit': 'Ctrl+Alt+Q',  # 紧急退出
        'toggle_all_fullscreen': 'Ctrl+Alt+F',  # 切换所有窗口全屏
        'system_info': 'Ctrl+Alt+I',  # 显示系统信息
    }
    
    for action, key_sequence in global_shortcuts.items():
        shortcut = QShortcut(QKeySequence(key_sequence), app.activeWindow())
        shortcut.activated.connect(lambda a=action: handle_global_shortcut(a))

def handle_global_shortcut(action):
    """处理全局快捷键"""
    app = QApplication.instance()
    
    if action == 'emergency_exit':
        app.quit()
    elif action == 'toggle_all_fullscreen':
        for widget in app.allWidgets():
            if hasattr(widget, 'isWindow') and widget.isWindow():
                if widget.isFullScreen():
                    widget.showNormal()
                else:
                    widget.showFullScreen()
    elif action == 'system_info':
        # 显示系统信息对话框
        from PyQt5.QtWidgets import QMessageBox
        import platform
        import psutil
        
        info = f"""
        系统信息:
        操作系统: {platform.system()} {platform.release()}
        Python版本: {platform.python_version()}
        CPU使用率: {psutil.cpu_percent()}%
        内存使用率: {psutil.virtual_memory().percent}%
        """
        
        QMessageBox.information(None, "系统信息", info)