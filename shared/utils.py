#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 共享工具函数
"""

import socket
import uuid
import json
import hashlib
from datetime import datetime
from typing import Dict, List, Any

def get_local_ip():
    """获取本机IP地址"""
    try:
        # 创建一个UDP socket
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        # 连接到一个不存在的地址，不会真正发送数据
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"

def generate_device_id():
    """生成设备唯一ID"""
    return str(uuid.uuid4())

def generate_classroom_code(length=6):
    """生成课堂码"""
    import random
    import string
    return ''.join(random.choices(string.digits, k=length))

def validate_ip_address(ip):
    """验证IP地址格式"""
    try:
        socket.inet_aton(ip)
        return True
    except socket.error:
        return False

def create_message(msg_type: str, data: Dict[str, Any], sender_id: str = None):
    """创建标准消息格式"""
    message = {
        'type': msg_type,
        'data': data,
        'timestamp': datetime.now().isoformat(),
        'sender_id': sender_id,
        'message_id': str(uuid.uuid4())
    }
    return message

def parse_message(message_str: str):
    """解析消息"""
    try:
        return json.loads(message_str)
    except json.JSONDecodeError:
        return None

def calculate_file_hash(file_path: str):
    """计算文件哈希值"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except FileNotFoundError:
        return None

def format_file_size(size_bytes: int):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

def is_port_available(host: str, port: int):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex((host, port))
            return result != 0
    except Exception:
        return False

def get_available_port(start_port: int = 8000, max_attempts: int = 100):
    """获取可用端口"""
    for port in range(start_port, start_port + max_attempts):
        if is_port_available("localhost", port):
            return port
    return None

class Logger:
    """简单日志记录器"""
    
    @staticmethod
    def info(message: str):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[INFO] {timestamp} - {message}")
    
    @staticmethod
    def error(message: str):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[ERROR] {timestamp} - {message}")
    
    @staticmethod
    def warning(message: str):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[WARNING] {timestamp} - {message}")
    
    @staticmethod
    def debug(message: str):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[DEBUG] {timestamp} - {message}")