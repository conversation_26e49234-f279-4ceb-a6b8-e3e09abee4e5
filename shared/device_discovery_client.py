# -*- coding: utf-8 -*-
"""
设备发现客户端 - 用于客户端响应服务器的设备发现请求
"""

import json
import socket
import threading
import time
import logging
import platform
import uuid
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# UDP广播配置
DISCOVERY_PORT = 5678
SERVER_DISCOVERY_MESSAGE = json.dumps({
    'type': 'discovery',
    'service': 'smart_classroom',
    'version': '1.0'
})


class DeviceDiscoveryClient:
    """设备发现客户端"""
    
    def __init__(self, device_id=None, device_type=None, device_name=None, port=8080, capabilities=None):
        """
        初始化设备发现客户端
        
        Args:
            device_id: 设备ID，如果为None则自动生成
            device_type: 设备类型 (teacher, group, student)
            device_name: 设备名称，如果为None则使用主机名
            port: 设备服务端口
            capabilities: 设备能力列表
        """
        # 设备信息
        self.device_id = device_id or self._generate_device_id()
        self.device_type = device_type or 'unknown'
        self.device_name = device_name or self._get_hostname()
        self.port = port
        self.capabilities = capabilities or []
        
        # 运行状态
        self.running = False
        self.listen_thread = None
        self.socket = None
        
        # 获取系统信息
        self.os_info = self._get_os_info()
        self.screen_resolution = self._get_screen_resolution()
    
    def start(self):
        """启动设备发现客户端"""
        if self.running:
            logger.warning("设备发现客户端已经在运行")
            return False
        
        try:
            # 创建UDP套接字
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind(('', DISCOVERY_PORT))
            
            self.running = True
            
            # 启动监听线程
            self.listen_thread = threading.Thread(target=self._listen_loop)
            self.listen_thread.daemon = True
            self.listen_thread.start()
            
            logger.info(f"设备发现客户端已启动 (ID: {self.device_id}, 类型: {self.device_type})")
            return True
        
        except Exception as e:
            logger.error(f"启动设备发现客户端失败: {str(e)}")
            self.stop()
            return False
    
    def stop(self):
        """停止设备发现客户端"""
        self.running = False
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        
        if self.listen_thread:
            self.listen_thread.join(timeout=1.0)
            self.listen_thread = None
        
        logger.info("设备发现客户端已停止")
    
    def _listen_loop(self):
        """监听设备发现广播"""
        logger.info("设备发现监听线程已启动")
        
        while self.running:
            try:
                # 接收广播
                data, addr = self.socket.recvfrom(1024)
                self._process_discovery_message(data, addr)
            
            except Exception as e:
                if self.running:  # 只在运行状态下记录错误
                    logger.error(f"监听设备发现广播出错: {str(e)}")
                    time.sleep(1)  # 出错后等待1秒再继续
    
    def _process_discovery_message(self, data, addr):
        """处理设备发现消息"""
        try:
            message = json.loads(data.decode('utf-8'))
            
            # 检查是否是服务器的发现消息
            if (message.get('type') == 'discovery' and 
                message.get('service') == 'smart_classroom'):
                
                logger.info(f"收到来自 {addr[0]}:{addr[1]} 的设备发现请求")
                
                # 发送响应
                self._send_discovery_response(addr)
        
        except json.JSONDecodeError:
            logger.warning(f"收到无效的JSON数据: {data}")
        except Exception as e:
            logger.error(f"处理设备发现消息出错: {str(e)}")
    
    def _send_discovery_response(self, addr):
        """发送设备发现响应"""
        try:
            # 构建响应消息
            response = {
                'type': 'discovery_response',
                'service': 'smart_classroom',
                'device_id': self.device_id,
                'device_type': self.device_type,
                'device_name': self.device_name,
                'port': self.port,
                'capabilities': self.capabilities,
                'os_info': self.os_info,
                'screen_resolution': self.screen_resolution
            }
            
            # 发送响应
            response_data = json.dumps(response).encode('utf-8')
            self.socket.sendto(response_data, addr)
            
            logger.info(f"已向 {addr[0]}:{addr[1]} 发送设备发现响应")
        
        except Exception as e:
            logger.error(f"发送设备发现响应出错: {str(e)}")
    
    def _generate_device_id(self):
        """生成设备ID"""
        # 使用MAC地址和随机数生成唯一ID
        mac = uuid.getnode()
        return f"{mac:012x}-{uuid.uuid4().hex[:8]}"
    
    def _get_hostname(self):
        """获取主机名"""
        return socket.gethostname()
    
    def _get_os_info(self):
        """获取操作系统信息"""
        return f"{platform.system()} {platform.release()}"
    
    def _get_screen_resolution(self):
        """获取屏幕分辨率"""
        try:
            # 这里需要根据不同平台实现
            # 简单起见，返回一个默认值
            return "1920x1080"
        except:
            return "unknown"


# 示例用法
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Smart Classroom Device Discovery Client')
    parser.add_argument('--type', choices=['teacher', 'group', 'student'], default='student',
                        help='Device type (teacher, group, student)')
    parser.add_argument('--name', help='Device name')
    parser.add_argument('--port', type=int, default=8080, help='Device service port')
    
    args = parser.parse_args()
    
    # 创建并启动设备发现客户端
    client = DeviceDiscoveryClient(
        device_type=args.type,
        device_name=args.name,
        port=args.port,
        capabilities=['screen_share', 'remote_control']
    )
    
    client.start()
    
    try:
        # 保持程序运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        client.stop()
        print("程序已退出")