#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI主题和样式管理
"""

from PyQt5.QtCore import QSettings
from PyQt5.QtGui import QPalette, QColor
from PyQt5.QtWidgets import QApplication

class UITheme:
    """UI主题类"""
    
    # 预定义主题
    THEMES = {
        'default': {
            'name': '默认主题',
            'primary_color': '#3498db',
            'secondary_color': '#2c3e50',
            'success_color': '#2ecc71',
            'warning_color': '#f39c12',
            'danger_color': '#e74c3c',
            'background_color': '#f5f5f5',
            'text_color': '#333333',
            'border_color': '#dddddd',
            'hover_color': '#ecf0f1'
        },
        'dark': {
            'name': '深色主题',
            'primary_color': '#3498db',
            'secondary_color': '#34495e',
            'success_color': '#27ae60',
            'warning_color': '#e67e22',
            'danger_color': '#c0392b',
            'background_color': '#2c3e50',
            'text_color': '#ecf0f1',
            'border_color': '#34495e',
            'hover_color': '#34495e'
        },
        'light': {
            'name': '浅色主题',
            'primary_color': '#2980b9',
            'secondary_color': '#ecf0f1',
            'success_color': '#2ecc71',
            'warning_color': '#f39c12',
            'danger_color': '#e74c3c',
            'background_color': '#ffffff',
            'text_color': '#2c3e50',
            'border_color': '#bdc3c7',
            'hover_color': '#f8f9fa'
        },
        'high_contrast': {
            'name': '高对比度主题',
            'primary_color': '#0066cc',
            'secondary_color': '#000000',
            'success_color': '#008000',
            'warning_color': '#ff8c00',
            'danger_color': '#dc143c',
            'background_color': '#ffffff',
            'text_color': '#000000',
            'border_color': '#000000',
            'hover_color': '#f0f0f0'
        }
    }
    
    def __init__(self):
        self.settings = QSettings('SmartClassroom', 'UITheme')
        self.current_theme = self.settings.value('current_theme', 'default')
    
    def get_current_theme(self):
        """获取当前主题"""
        return self.THEMES.get(self.current_theme, self.THEMES['default'])
    
    def set_theme(self, theme_name):
        """设置主题"""
        if theme_name in self.THEMES:
            self.current_theme = theme_name
            self.settings.setValue('current_theme', theme_name)
            return True
        return False
    
    def get_available_themes(self):
        """获取可用主题列表"""
        return [(key, theme['name']) for key, theme in self.THEMES.items()]
    
    def apply_theme_to_app(self, app):
        """将主题应用到应用程序"""
        theme = self.get_current_theme()
        
        # 创建调色板
        palette = QPalette()
        
        # 设置颜色
        palette.setColor(QPalette.Window, QColor(theme['background_color']))
        palette.setColor(QPalette.WindowText, QColor(theme['text_color']))
        palette.setColor(QPalette.Base, QColor(theme['background_color']))
        palette.setColor(QPalette.AlternateBase, QColor(theme['hover_color']))
        palette.setColor(QPalette.ToolTipBase, QColor(theme['background_color']))
        palette.setColor(QPalette.ToolTipText, QColor(theme['text_color']))
        palette.setColor(QPalette.Text, QColor(theme['text_color']))
        palette.setColor(QPalette.Button, QColor(theme['hover_color']))
        palette.setColor(QPalette.ButtonText, QColor(theme['text_color']))
        palette.setColor(QPalette.BrightText, QColor(theme['danger_color']))
        palette.setColor(QPalette.Link, QColor(theme['primary_color']))
        palette.setColor(QPalette.Highlight, QColor(theme['primary_color']))
        palette.setColor(QPalette.HighlightedText, QColor('#ffffff'))
        
        app.setPalette(palette)
    
    def get_stylesheet(self, widget_type='default'):
        """获取样式表"""
        theme = self.get_current_theme()
        
        stylesheets = {
            'button': f"""
                QPushButton {{
                    background-color: {theme['primary_color']};
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {theme['secondary_color']};
                }}
                QPushButton:pressed {{
                    background-color: {theme['text_color']};
                }}
                QPushButton:disabled {{
                    background-color: {theme['border_color']};
                    color: {theme['text_color']};
                }}
            """,
            'success_button': f"""
                QPushButton {{
                    background-color: {theme['success_color']};
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    opacity: 0.8;
                }}
            """,
            'danger_button': f"""
                QPushButton {{
                    background-color: {theme['danger_color']};
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    opacity: 0.8;
                }}
            """,
            'panel': f"""
                QWidget {{
                    background-color: {theme['background_color']};
                    color: {theme['text_color']};
                    border: 1px solid {theme['border_color']};
                    border-radius: 8px;
                }}
            """,
            'tab_widget': f"""
                QTabWidget::pane {{
                    border: 1px solid {theme['border_color']};
                    background-color: {theme['background_color']};
                }}
                QTabBar::tab {{
                    background-color: {theme['hover_color']};
                    color: {theme['text_color']};
                    padding: 8px 16px;
                    margin-right: 2px;
                    border-top-left-radius: 4px;
                    border-top-right-radius: 4px;
                }}
                QTabBar::tab:selected {{
                    background-color: {theme['primary_color']};
                    color: white;
                }}
                QTabBar::tab:hover {{
                    background-color: {theme['secondary_color']};
                    color: white;
                }}
            """,
            'input': f"""
                QLineEdit, QTextEdit, QComboBox {{
                    background-color: {theme['background_color']};
                    color: {theme['text_color']};
                    border: 2px solid {theme['border_color']};
                    border-radius: 4px;
                    padding: 8px;
                    font-size: 14px;
                }}
                QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
                    border-color: {theme['primary_color']};
                }}
            """,
            'toolbar': f"""
                QToolBar {{
                    background-color: {theme['hover_color']};
                    border: 1px solid {theme['border_color']};
                    spacing: 4px;
                }}
                QToolButton {{
                    background-color: transparent;
                    color: {theme['text_color']};
                    border: none;
                    padding: 8px;
                    border-radius: 4px;
                }}
                QToolButton:hover {{
                    background-color: {theme['primary_color']};
                    color: white;
                }}
            """
        }
        
        return stylesheets.get(widget_type, '')

class AccessibilityHelper:
    """无障碍访问辅助类"""
    
    @staticmethod
    def set_high_contrast_mode(app):
        """设置高对比度模式"""
        theme_manager = UITheme()
        theme_manager.set_theme('high_contrast')
        theme_manager.apply_theme_to_app(app)
    
    @staticmethod
    def increase_font_size(widget, scale_factor=1.2):
        """增大字体大小"""
        font = widget.font()
        font.setPointSize(int(font.pointSize() * scale_factor))
        widget.setFont(font)
    
    @staticmethod
    def set_focus_indicators(widget):
        """设置焦点指示器"""
        widget.setStyleSheet(widget.styleSheet() + """
            QWidget:focus {
                border: 3px solid #0066cc;
                outline: none;
            }
        """)
    
    @staticmethod
    def add_keyboard_navigation(widget):
        """添加键盘导航支持"""
        widget.setFocusPolicy(Qt.StrongFocus)
        widget.setTabOrder(widget, widget.nextInFocusChain())

# 全局主题管理器实例
theme_manager = UITheme()