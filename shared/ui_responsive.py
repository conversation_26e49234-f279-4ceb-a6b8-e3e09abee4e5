#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
响应式UI设计和移动端适配
"""

from PyQt5.QtCore import Qt, QSize, QRect, pyqtSignal, QObject
from PyQt5.QtWidgets import QWidget, QLayout, QSizePolicy
from PyQt5.QtGui import QScreen

class ResponsiveLayout(QLayout):
    """响应式布局"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.items = []
        self.breakpoints = {
            'xs': 480,   # 超小屏幕
            'sm': 768,   # 小屏幕
            'md': 1024,  # 中等屏幕
            'lg': 1200,  # 大屏幕
            'xl': 1920   # 超大屏幕
        }
        self.current_breakpoint = 'lg'
    
    def addItem(self, item):
        """添加项目"""
        self.items.append(item)
    
    def count(self):
        """返回项目数量"""
        return len(self.items)
    
    def itemAt(self, index):
        """获取指定索引的项目"""
        if 0 <= index < len(self.items):
            return self.items[index]
        return None
    
    def takeAt(self, index):
        """移除并返回指定索引的项目"""
        if 0 <= index < len(self.items):
            return self.items.pop(index)
        return None
    
    def setGeometry(self, rect):
        """设置几何形状"""
        super().setGeometry(rect)
        self.update_breakpoint(rect.width())
        self.arrange_items(rect)
    
    def sizeHint(self):
        """返回建议大小"""
        return QSize(800, 600)
    
    def minimumSize(self):
        """返回最小大小"""
        return QSize(320, 240)
    
    def update_breakpoint(self, width):
        """更新断点"""
        old_breakpoint = self.current_breakpoint
        
        if width <= self.breakpoints['xs']:
            self.current_breakpoint = 'xs'
        elif width <= self.breakpoints['sm']:
            self.current_breakpoint = 'sm'
        elif width <= self.breakpoints['md']:
            self.current_breakpoint = 'md'
        elif width <= self.breakpoints['lg']:
            self.current_breakpoint = 'lg'
        else:
            self.current_breakpoint = 'xl'
        
        if old_breakpoint != self.current_breakpoint:
            self.on_breakpoint_changed(old_breakpoint, self.current_breakpoint)
    
    def on_breakpoint_changed(self, old_bp, new_bp):
        """断点变化处理"""
        # 子类可以重写此方法来处理断点变化
        pass
    
    def arrange_items(self, rect):
        """排列项目"""
        if not self.items:
            return
        
        # 根据当前断点调整布局
        if self.current_breakpoint in ['xs', 'sm']:
            self.arrange_mobile(rect)
        else:
            self.arrange_desktop(rect)
    
    def arrange_mobile(self, rect):
        """移动端布局"""
        # 垂直堆叠布局
        item_height = rect.height() // len(self.items)
        
        for i, item in enumerate(self.items):
            if item.widget():
                item_rect = QRect(
                    rect.x(),
                    rect.y() + i * item_height,
                    rect.width(),
                    item_height
                )
                item.widget().setGeometry(item_rect)
    
    def arrange_desktop(self, rect):
        """桌面端布局"""
        # 网格布局
        cols = 2 if self.current_breakpoint == 'md' else 3
        rows = (len(self.items) + cols - 1) // cols
        
        item_width = rect.width() // cols
        item_height = rect.height() // rows
        
        for i, item in enumerate(self.items):
            if item.widget():
                row = i // cols
                col = i % cols
                
                item_rect = QRect(
                    rect.x() + col * item_width,
                    rect.y() + row * item_height,
                    item_width,
                    item_height
                )
                item.widget().setGeometry(item_rect)

class ResponsiveWidget(QWidget):
    """响应式组件基类"""
    
    breakpoint_changed = pyqtSignal(str, str)  # old_bp, new_bp
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.breakpoints = {
            'xs': 480,
            'sm': 768,
            'md': 1024,
            'lg': 1200,
            'xl': 1920
        }
        self.current_breakpoint = 'lg'
        self.responsive_styles = {}
    
    def resizeEvent(self, event):
        """窗口大小变化事件"""
        super().resizeEvent(event)
        self.update_responsive_design(event.size().width())
    
    def update_responsive_design(self, width):
        """更新响应式设计"""
        old_breakpoint = self.current_breakpoint
        
        # 确定当前断点
        if width <= self.breakpoints['xs']:
            self.current_breakpoint = 'xs'
        elif width <= self.breakpoints['sm']:
            self.current_breakpoint = 'sm'
        elif width <= self.breakpoints['md']:
            self.current_breakpoint = 'md'
        elif width <= self.breakpoints['lg']:
            self.current_breakpoint = 'lg'
        else:
            self.current_breakpoint = 'xl'
        
        # 如果断点发生变化，应用相应样式
        if old_breakpoint != self.current_breakpoint:
            self.apply_responsive_styles()
            self.breakpoint_changed.emit(old_breakpoint, self.current_breakpoint)
    
    def set_responsive_style(self, breakpoint, style):
        """设置响应式样式"""
        self.responsive_styles[breakpoint] = style
    
    def apply_responsive_styles(self):
        """应用响应式样式"""
        if self.current_breakpoint in self.responsive_styles:
            self.setStyleSheet(self.responsive_styles[self.current_breakpoint])

class MobileOptimizedWidget(ResponsiveWidget):
    """移动端优化组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_mobile_styles()
        self.setup_touch_gestures()
    
    def setup_mobile_styles(self):
        """设置移动端样式"""
        # 移动端样式
        mobile_style = """
            QWidget {
                font-size: 16px;
                padding: 12px;
            }
            QPushButton {
                min-height: 44px;
                font-size: 16px;
                padding: 12px 20px;
                border-radius: 8px;
            }
            QLineEdit, QTextEdit {
                min-height: 44px;
                font-size: 16px;
                padding: 12px;
                border-radius: 8px;
            }
            QTabBar::tab {
                min-height: 44px;
                padding: 12px 20px;
                font-size: 16px;
            }
        """
        
        # 平板样式
        tablet_style = """
            QWidget {
                font-size: 14px;
                padding: 10px;
            }
            QPushButton {
                min-height: 40px;
                font-size: 14px;
                padding: 10px 16px;
                border-radius: 6px;
            }
            QLineEdit, QTextEdit {
                min-height: 40px;
                font-size: 14px;
                padding: 10px;
                border-radius: 6px;
            }
        """
        
        # 桌面样式
        desktop_style = """
            QWidget {
                font-size: 12px;
                padding: 8px;
            }
            QPushButton {
                min-height: 32px;
                font-size: 12px;
                padding: 8px 12px;
                border-radius: 4px;
            }
            QLineEdit, QTextEdit {
                min-height: 32px;
                font-size: 12px;
                padding: 8px;
                border-radius: 4px;
            }
        """
        
        self.set_responsive_style('xs', mobile_style)
        self.set_responsive_style('sm', tablet_style)
        self.set_responsive_style('md', desktop_style)
        self.set_responsive_style('lg', desktop_style)
        self.set_responsive_style('xl', desktop_style)
    
    def setup_touch_gestures(self):
        """设置触摸手势"""
        self.setAttribute(Qt.WA_AcceptTouchEvents, True)
        self.setFocusPolicy(Qt.StrongFocus)
    
    def event(self, event):
        """事件处理"""
        if event.type() in [event.TouchBegin, event.TouchUpdate, event.TouchEnd]:
            return self.handle_touch_event(event)
        return super().event(event)
    
    def handle_touch_event(self, event):
        """处理触摸事件"""
        # 基本的触摸事件处理
        if event.type() == event.TouchBegin:
            self.on_touch_begin(event)
        elif event.type() == event.TouchUpdate:
            self.on_touch_update(event)
        elif event.type() == event.TouchEnd:
            self.on_touch_end(event)
        
        return True
    
    def on_touch_begin(self, event):
        """触摸开始"""
        pass
    
    def on_touch_update(self, event):
        """触摸更新"""
        pass
    
    def on_touch_end(self, event):
        """触摸结束"""
        pass

class ScreenSizeDetector(QObject):
    """屏幕尺寸检测器"""
    
    screen_changed = pyqtSignal(str)  # 屏幕类型变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_screen_type = self.detect_screen_type()
    
    def detect_screen_type(self):
        """检测屏幕类型"""
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication.instance()
        if not app:
            return 'desktop'
        
        screen = app.primaryScreen()
        if not screen:
            return 'desktop'
        
        size = screen.size()
        width = size.width()
        height = size.height()
        
        # 根据屏幕尺寸判断设备类型
        if width <= 480 or height <= 480:
            return 'mobile'
        elif width <= 1024 or height <= 768:
            return 'tablet'
        else:
            return 'desktop'
    
    def get_screen_info(self):
        """获取屏幕信息"""
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication.instance()
        if not app:
            return {}
        
        screen = app.primaryScreen()
        if not screen:
            return {}
        
        return {
            'size': screen.size(),
            'available_size': screen.availableSize(),
            'dpi': screen.logicalDotsPerInch(),
            'device_pixel_ratio': screen.devicePixelRatio(),
            'orientation': screen.orientation(),
            'type': self.current_screen_type
        }

def create_responsive_button(text, parent=None):
    """创建响应式按钮"""
    from PyQt5.QtWidgets import QPushButton
    
    button = QPushButton(text, parent)
    
    # 设置响应式样式
    button.setStyleSheet("""
        QPushButton {
            min-height: 32px;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
            background-color: #3498db;
            color: white;
            border: none;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
        QPushButton:pressed {
            background-color: #21618c;
        }
    """)
    
    # 设置大小策略
    button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
    
    return button

def create_responsive_input(placeholder="", parent=None):
    """创建响应式输入框"""
    from PyQt5.QtWidgets import QLineEdit
    
    input_field = QLineEdit(parent)
    input_field.setPlaceholderText(placeholder)
    
    # 设置响应式样式
    input_field.setStyleSheet("""
        QLineEdit {
            min-height: 32px;
            padding: 8px 12px;
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            font-size: 14px;
        }
        QLineEdit:focus {
            border-color: #3498db;
        }
    """)
    
    # 设置大小策略
    input_field.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
    
    return input_field