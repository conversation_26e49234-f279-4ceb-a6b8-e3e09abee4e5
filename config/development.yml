# 智慧课堂系统 - 开发环境配置

app:
  name: "智慧课堂系统"
  version: "1.0.0"
  debug: true

network:
  udp_discovery_port: 8888
  backend_host: "localhost"
  backend_port: 5000
  
video:
  mediamtx_host: "localhost"
  mediamtx_rtmp_port: 1935
  mediamtx_http_port: 8889
  resolution: "1920x1080"
  fps: 30
  bitrate: "2M"

performance:
  screen_share_delay_max: 1.0
  broadcast_delay_max: 1.5
  max_terminals: 75
  max_groups: 8
  max_screen_shares: 6

database:
  url: "sqlite:///smart_classroom.db"
  echo: true

files:
  upload_folder: "uploads"
  max_file_size: 104857600  # 100MB
  allowed_extensions:
    image: ["jpg", "jpeg", "png", "gif"]
    document: ["pdf", "doc", "docx", "ppt", "pptx", "xls", "xlsx"]
    video: ["mp4", "avi", "mov", "wmv"]
    audio: ["mp3", "wav", "aac"]

logging:
  level: "DEBUG"
  format: "[%(levelname)s] %(asctime)s - %(name)s - %(message)s"
  file: "logs/smart_classroom.log"