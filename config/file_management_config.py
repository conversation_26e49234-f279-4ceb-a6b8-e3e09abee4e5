# -*- coding: utf-8 -*-
"""
增强文件管理系统配置
"""

import os
from datetime import timedelta

class FileManagementConfig:
    """文件管理系统配置类"""
    
    # ==================== 基础配置 ====================
    
    # 文件存储路径
    UPLOAD_FOLDER = os.path.join('instance', 'uploads')
    VERSION_FOLDER = os.path.join('instance', 'versions')
    CHUNK_FOLDER = os.path.join('instance', 'chunks')
    PREVIEW_FOLDER = os.path.join('instance', 'previews')
    BATCH_FOLDER = os.path.join('instance', 'batch')
    DISTRIBUTION_FOLDER = os.path.join('instance', 'distributions')
    
    # 支持的文件类型
    ALLOWED_EXTENSIONS = {
        # 文档类型
        'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx',
        'txt', 'md', 'rtf', 'odt', 'ods', 'odp',
        
        # 图片类型
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp',
        
        # 音视频类型
        'mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv',
        'mp3', 'wav', 'aac', 'flac', 'ogg',
        
        # 压缩文件
        'zip', 'rar', '7z', 'tar', 'gz',
        
        # 代码文件
        'py', 'js', 'html', 'css', 'java', 'cpp', 'c',
        'json', 'xml', 'yaml', 'yml'
    }
    
    # 文件大小限制
    MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
    MAX_CHUNK_SIZE = 5 * 1024 * 1024   # 5MB per chunk
    
    # ==================== 版本管理配置 ====================
    
    # 版本保留策略
    VERSION_RETENTION = {
        'max_versions_per_file': 10,  # 每个文件最多保留版本数
        'auto_cleanup_days': 90,      # 自动清理天数
        'compress_old_versions': True  # 压缩旧版本
    }
    
    # ==================== 分块上传配置 ====================
    
    CHUNKED_UPLOAD = {
        'chunk_size': 1024 * 1024,     # 1MB chunks
        'max_chunks': 1000,            # 最大块数
        'session_timeout': 3600,       # 会话超时时间（秒）
        'cleanup_interval': 300,       # 清理间隔（秒）
        'retry_attempts': 3            # 重试次数
    }
    
    # ==================== 文件预览配置 ====================
    
    PREVIEW_CONFIG = {
        'enable_preview': True,
        'preview_timeout': 30,         # 预览生成超时时间（秒）
        'thumbnail_size': (200, 200),  # 缩略图尺寸
        'max_preview_size': 10 * 1024 * 1024,  # 最大预览文件大小
        
        # 预览工具路径
        'tools': {
            'pdf2image': '/usr/bin/pdf2image',
            'libreoffice': '/usr/bin/libreoffice',
            'ffmpeg': '/usr/bin/ffmpeg',
            'imagemagick': '/usr/bin/convert'
        },
        
        # 支持的预览格式
        'supported_formats': {
            'pdf': {'type': 'pdf', 'engine': 'pdf2image'},
            'doc': {'type': 'document', 'engine': 'libreoffice'},
            'docx': {'type': 'document', 'engine': 'libreoffice'},
            'ppt': {'type': 'presentation', 'engine': 'libreoffice'},
            'pptx': {'type': 'presentation', 'engine': 'libreoffice'},
            'xls': {'type': 'spreadsheet', 'engine': 'libreoffice'},
            'xlsx': {'type': 'spreadsheet', 'engine': 'libreoffice'},
            'jpg': {'type': 'image', 'engine': 'native'},
            'jpeg': {'type': 'image', 'engine': 'native'},
            'png': {'type': 'image', 'engine': 'native'},
            'gif': {'type': 'image', 'engine': 'native'},
            'mp4': {'type': 'video', 'engine': 'ffmpeg'},
            'avi': {'type': 'video', 'engine': 'ffmpeg'},
            'txt': {'type': 'text', 'engine': 'native'},
            'md': {'type': 'markdown', 'engine': 'native'}
        }
    }
    
    # ==================== 权限管理配置 ====================
    
    PERMISSION_CONFIG = {
        'default_permissions': {
            'owner': {'read': True, 'write': True, 'delete': True, 'share': True},
            'teacher': {'read': True, 'write': True, 'delete': False, 'share': True},
            'student': {'read': True, 'write': False, 'delete': False, 'share': False},
            'guest': {'read': True, 'write': False, 'delete': False, 'share': False}
        },
        
        'role_hierarchy': ['owner', 'teacher', 'student', 'guest'],
        
        'permission_inheritance': True,  # 是否支持权限继承
        'group_permissions': True,       # 是否支持组权限
    }
    
    # ==================== 审计日志配置 ====================
    
    AUDIT_CONFIG = {
        'enable_audit': True,
        'log_actions': [
            'view', 'download', 'upload', 'edit', 'delete', 
            'share', 'copy', 'move', 'rename', 'tag'
        ],
        'retention_days': 365,           # 日志保留天数
        'batch_size': 1000,             # 批量处理大小
        'anonymize_after_days': 180,    # 匿名化处理天数
        
        # 敏感操作需要额外记录
        'sensitive_actions': ['delete', 'share', 'permission_change'],
        'detailed_logging': True
    }
    
    # ==================== 标签管理配置 ====================
    
    TAG_CONFIG = {
        'enable_auto_tagging': True,
        'max_tags_per_file': 20,
        'tag_suggestions': True,
        
        # 预定义标签类别
        'categories': {
            'type': ['课件', '作业', '资料', '视频', '音频', '图片', '文档'],
            'subject': ['数学', '语文', '英语', '物理', '化学', '生物', '历史', '地理'],
            'difficulty': ['简单', '中等', '困难', '挑战'],
            'status': ['草稿', '已发布', '已归档', '待审核'],
            'priority': ['高', '中', '低', '紧急']
        },
        
        # 自动标签规则
        'auto_tag_rules': {
            'file_type': {
                'pdf': ['文档', '资料'],
                'ppt': ['课件', '演示'],
                'doc': ['文档', '作业'],
                'mp4': ['视频', '多媒体'],
                'jpg': ['图片', '素材']
            },
            'filename_keywords': {
                '作业': ['作业', 'homework', 'assignment'],
                '课件': ['课件', 'slide', 'presentation'],
                '测试': ['测试', 'test', 'exam', '考试'],
                '资料': ['资料', 'material', 'resource']
            }
        }
    }
    
    # ==================== 批量操作配置 ====================
    
    BATCH_CONFIG = {
        'max_batch_size': 100,          # 最大批量操作文件数
        'batch_timeout': 300,           # 批量操作超时时间（秒）
        'concurrent_operations': 5,      # 并发操作数
        'retry_failed_operations': True, # 重试失败的操作
        
        # 支持的批量操作类型
        'supported_operations': [
            'delete', 'move', 'copy', 'tag', 'permission', 
            'compress', 'convert', 'rename'
        ],
        
        # 操作优先级
        'operation_priority': {
            'delete': 1,
            'move': 2,
            'copy': 3,
            'tag': 4,
            'permission': 5,
            'compress': 6,
            'convert': 7,
            'rename': 8
        }
    }
    
    # ==================== 文件分发配置 ====================
    
    DISTRIBUTION_CONFIG = {
        'max_distribution_size': 50,     # 最大分发文件数
        'distribution_timeout': 600,     # 分发超时时间（秒）
        'retry_attempts': 3,             # 重试次数
        'batch_distribution': True,      # 批量分发
        
        # 分发目标类型
        'target_types': ['all', 'group', 'student', 'device'],
        
        # 分发策略
        'strategies': {
            'immediate': {'priority': 1, 'timeout': 60},
            'scheduled': {'priority': 2, 'timeout': 300},
            'background': {'priority': 3, 'timeout': 600}
        },
        
        # 分发状态跟踪
        'status_tracking': True,
        'progress_reporting': True,
        'failure_notification': True
    }
    
    # ==================== 性能优化配置 ====================
    
    PERFORMANCE_CONFIG = {
        'enable_caching': True,
        'cache_timeout': 3600,           # 缓存超时时间（秒）
        'max_cache_size': 100 * 1024 * 1024,  # 最大缓存大小
        
        # 数据库优化
        'db_pool_size': 10,
        'db_pool_timeout': 30,
        'db_pool_recycle': 3600,
        
        # 文件操作优化
        'async_operations': True,
        'background_tasks': True,
        'compression_enabled': True,
        
        # 监控配置
        'enable_monitoring': True,
        'metrics_collection': True,
        'performance_alerts': True
    }
    
    # ==================== 安全配置 ====================
    
    SECURITY_CONFIG = {
        'enable_virus_scan': False,      # 病毒扫描（需要安装ClamAV）
        'scan_timeout': 60,              # 扫描超时时间
        'quarantine_folder': os.path.join('instance', 'quarantine'),
        
        # 文件内容检查
        'content_validation': True,
        'mime_type_validation': True,
        'file_signature_check': True,
        
        # 访问控制
        'ip_whitelist': [],              # IP白名单
        'rate_limiting': {
            'enabled': True,
            'max_requests': 100,         # 每分钟最大请求数
            'window_size': 60            # 时间窗口（秒）
        },
        
        # 加密配置
        'encrypt_sensitive_files': False,
        'encryption_key_rotation': 30,   # 密钥轮换天数
    }
    
    # ==================== 清理任务配置 ====================
    
    CLEANUP_CONFIG = {
        'enable_auto_cleanup': True,
        'cleanup_schedule': '0 2 * * *',  # 每天凌晨2点执行
        
        'cleanup_rules': {
            'temp_files': {'max_age_hours': 24},
            'failed_uploads': {'max_age_hours': 48},
            'old_versions': {'max_age_days': 90},
            'audit_logs': {'max_age_days': 365},
            'preview_cache': {'max_age_days': 7}
        }
    }
    
    @classmethod
    def get_config(cls, section=None):
        """获取配置"""
        if section:
            return getattr(cls, f"{section.upper()}_CONFIG", {})
        
        # 返回所有配置
        config = {}
        for attr in dir(cls):
            if attr.endswith('_CONFIG') and not attr.startswith('_'):
                config[attr.lower().replace('_config', '')] = getattr(cls, attr)
        
        return config
    
    @classmethod
    def validate_config(cls):
        """验证配置"""
        errors = []
        
        # 检查必要的目录
        required_dirs = [
            cls.UPLOAD_FOLDER, cls.VERSION_FOLDER, cls.CHUNK_FOLDER,
            cls.PREVIEW_FOLDER, cls.BATCH_FOLDER, cls.DISTRIBUTION_FOLDER
        ]
        
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                try:
                    os.makedirs(dir_path, exist_ok=True)
                except Exception as e:
                    errors.append(f"无法创建目录 {dir_path}: {str(e)}")
        
        # 检查文件大小限制
        if cls.MAX_FILE_SIZE <= 0:
            errors.append("MAX_FILE_SIZE 必须大于 0")
        
        if cls.MAX_CHUNK_SIZE <= 0:
            errors.append("MAX_CHUNK_SIZE 必须大于 0")
        
        # 检查预览工具
        if cls.PREVIEW_CONFIG['enable_preview']:
            for tool, path in cls.PREVIEW_CONFIG['tools'].items():
                if path and not os.path.exists(path):
                    errors.append(f"预览工具 {tool} 路径不存在: {path}")
        
        return errors


# 创建默认配置实例
default_config = FileManagementConfig()

# 验证配置
config_errors = default_config.validate_config()
if config_errors:
    print("⚠️ 配置验证发现问题:")
    for error in config_errors:
        print(f"  - {error}")
else:
    print("✅ 文件管理系统配置验证通过")