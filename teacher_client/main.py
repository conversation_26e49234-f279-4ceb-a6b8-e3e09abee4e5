#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 教师端主程序
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from teacher_client.ui.main_window import MainWindow

def main():
    """教师端应用入口"""
    app = QApplication(sys.argv)
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()