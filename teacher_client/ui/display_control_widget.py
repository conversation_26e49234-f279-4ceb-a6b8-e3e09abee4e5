#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 教师端多屏显示控制界面
"""

import sys
import json
import requests
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QComboBox, QSpinBox, QLineEdit,
    QTextEdit, QListWidget, QListWidgetItem, QGroupBox,
    QTabWidget, QProgressBar, QCheckBox, QSlider,
    QMessageBox, QDialog, QDialogButtonBox, QFormLayout,
    QSplitter, QFrame, QScrollArea
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QPixmap, QIcon, QPalette, QColor

class DisplayControlWidget(QWidget):
    """多屏显示控制主界面"""
    
    # 信号定义
    config_applied = pyqtSignal(dict)
    timer_started = pyqtSignal(dict)
    topic_distributed = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.classroom_id = None
        self.api_base_url = "http://localhost:5000/api/display"
        self.current_configs = []
        self.active_timers = []
        self.discussion_topics = []
        
        # 设置定时器用于更新状态
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(1000)  # 每秒更新一次
        
        self.init_ui()
        self.load_display_modes()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("多屏显示控制")
        self.setMinimumSize(1200, 800)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)
        
        # 创建标题
        title_label = QLabel("多屏显示控制系统")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 显示配置选项卡
        self.display_config_tab = self.create_display_config_tab()
        self.tab_widget.addTab(self.display_config_tab, "显示配置")
        
        # 计时器选项卡
        self.timer_tab = self.create_timer_tab()
        self.tab_widget.addTab(self.timer_tab, "分组计时器")
        
        # 讨论主题选项卡
        self.topic_tab = self.create_topic_tab()
        self.tab_widget.addTab(self.topic_tab, "讨论主题")
        
        # 状态监控选项卡
        self.status_tab = self.create_status_tab()
        self.tab_widget.addTab(self.status_tab, "状态监控")
    
    def create_display_config_tab(self):
        """创建显示配置选项卡"""
        tab = QWidget()
        layout = QHBoxLayout()
        tab.setLayout(layout)
        
        # 左侧：配置创建和编辑
        left_panel = QGroupBox("显示配置")
        left_layout = QVBoxLayout()
        left_panel.setLayout(left_layout)
        
        # 配置表单
        form_layout = QFormLayout()
        
        self.config_name_edit = QLineEdit()
        form_layout.addRow("配置名称:", self.config_name_edit)
        
        self.display_mode_combo = QComboBox()
        form_layout.addRow("显示模式:", self.display_mode_combo)
        
        self.layout_combo = QComboBox()
        form_layout.addRow("布局类型:", self.layout_combo)
        
        self.target_devices_edit = QLineEdit()
        self.target_devices_edit.setPlaceholderText("设备ID列表，用逗号分隔")
        form_layout.addRow("目标设备:", self.target_devices_edit)
        
        self.content_sources_edit = QLineEdit()
        self.content_sources_edit.setPlaceholderText("内容源ID列表，用逗号分隔")
        form_layout.addRow("内容源:", self.content_sources_edit)
        
        self.sync_enabled_checkbox = QCheckBox("启用同步")
        self.sync_enabled_checkbox.setChecked(True)
        form_layout.addRow("同步设置:", self.sync_enabled_checkbox)
        
        left_layout.addLayout(form_layout)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        self.create_config_btn = QPushButton("创建配置")
        self.create_config_btn.clicked.connect(self.create_display_config)
        button_layout.addWidget(self.create_config_btn)
        
        self.update_config_btn = QPushButton("更新配置")
        self.update_config_btn.clicked.connect(self.update_display_config)
        self.update_config_btn.setEnabled(False)
        button_layout.addWidget(self.update_config_btn)
        
        self.apply_config_btn = QPushButton("应用配置")
        self.apply_config_btn.clicked.connect(self.apply_display_config)
        self.apply_config_btn.setEnabled(False)
        button_layout.addWidget(self.apply_config_btn)
        
        left_layout.addLayout(button_layout)
        
        # 右侧：配置列表
        right_panel = QGroupBox("配置列表")
        right_layout = QVBoxLayout()
        right_panel.setLayout(right_layout)
        
        self.config_list = QListWidget()
        self.config_list.itemClicked.connect(self.on_config_selected)
        right_layout.addWidget(self.config_list)
        
        # 配置操作按钮
        config_button_layout = QHBoxLayout()
        
        self.refresh_configs_btn = QPushButton("刷新")
        self.refresh_configs_btn.clicked.connect(self.load_display_configs)
        config_button_layout.addWidget(self.refresh_configs_btn)
        
        self.delete_config_btn = QPushButton("删除")
        self.delete_config_btn.clicked.connect(self.delete_display_config)
        self.delete_config_btn.setEnabled(False)
        config_button_layout.addWidget(self.delete_config_btn)
        
        self.stop_display_btn = QPushButton("停止显示")
        self.stop_display_btn.clicked.connect(self.stop_display)
        self.stop_display_btn.setEnabled(False)
        config_button_layout.addWidget(self.stop_display_btn)
        
        right_layout.addLayout(config_button_layout)
        
        # 添加到主布局
        layout.addWidget(left_panel, 1)
        layout.addWidget(right_panel, 1)
        
        return tab
    
    def create_timer_tab(self):
        """创建计时器选项卡"""
        tab = QWidget()
        layout = QHBoxLayout()
        tab.setLayout(layout)
        
        # 左侧：计时器创建
        left_panel = QGroupBox("创建计时器")
        left_layout = QVBoxLayout()
        left_panel.setLayout(left_layout)
        
        # 计时器表单
        form_layout = QFormLayout()
        
        self.timer_title_edit = QLineEdit()
        form_layout.addRow("计时器标题:", self.timer_title_edit)
        
        self.timer_duration_spin = QSpinBox()
        self.timer_duration_spin.setRange(1, 7200)  # 1秒到2小时
        self.timer_duration_spin.setValue(300)  # 默认5分钟
        self.timer_duration_spin.setSuffix(" 秒")
        form_layout.addRow("持续时间:", self.timer_duration_spin)
        
        self.timer_groups_edit = QLineEdit()
        self.timer_groups_edit.setPlaceholderText("小组ID列表，用逗号分隔")
        form_layout.addRow("目标小组:", self.timer_groups_edit)
        
        left_layout.addLayout(form_layout)
        
        # 计时器按钮
        timer_button_layout = QHBoxLayout()
        
        self.create_timer_btn = QPushButton("创建计时器")
        self.create_timer_btn.clicked.connect(self.create_group_timer)
        timer_button_layout.addWidget(self.create_timer_btn)
        
        left_layout.addLayout(timer_button_layout)
        
        # 右侧：计时器列表和控制
        right_panel = QGroupBox("计时器控制")
        right_layout = QVBoxLayout()
        right_panel.setLayout(right_layout)
        
        self.timer_list = QListWidget()
        self.timer_list.itemClicked.connect(self.on_timer_selected)
        right_layout.addWidget(self.timer_list)
        
        # 计时器控制按钮
        timer_control_layout = QHBoxLayout()
        
        self.start_timer_btn = QPushButton("启动")
        self.start_timer_btn.clicked.connect(self.start_group_timer)
        self.start_timer_btn.setEnabled(False)
        timer_control_layout.addWidget(self.start_timer_btn)
        
        self.pause_timer_btn = QPushButton("暂停")
        self.pause_timer_btn.clicked.connect(self.pause_group_timer)
        self.pause_timer_btn.setEnabled(False)
        timer_control_layout.addWidget(self.pause_timer_btn)
        
        self.resume_timer_btn = QPushButton("恢复")
        self.resume_timer_btn.clicked.connect(self.resume_group_timer)
        self.resume_timer_btn.setEnabled(False)
        timer_control_layout.addWidget(self.resume_timer_btn)
        
        self.stop_timer_btn = QPushButton("停止")
        self.stop_timer_btn.clicked.connect(self.stop_group_timer)
        self.stop_timer_btn.setEnabled(False)
        timer_control_layout.addWidget(self.stop_timer_btn)
        
        right_layout.addLayout(timer_control_layout)
        
        # 计时器显示
        self.timer_display = QLabel("00:00")
        self.timer_display.setFont(QFont("Arial", 24, QFont.Bold))
        self.timer_display.setAlignment(Qt.AlignCenter)
        self.timer_display.setStyleSheet("QLabel { background-color: black; color: green; border: 2px solid gray; }")
        right_layout.addWidget(self.timer_display)
        
        # 添加到主布局
        layout.addWidget(left_panel, 1)
        layout.addWidget(right_panel, 1)
        
        return tab
    
    def create_topic_tab(self):
        """创建讨论主题选项卡"""
        tab = QWidget()
        layout = QHBoxLayout()
        tab.setLayout(layout)
        
        # 左侧：主题创建
        left_panel = QGroupBox("创建讨论主题")
        left_layout = QVBoxLayout()
        left_panel.setLayout(left_layout)
        
        # 主题表单
        form_layout = QFormLayout()
        
        self.topic_title_edit = QLineEdit()
        form_layout.addRow("主题标题:", self.topic_title_edit)
        
        self.topic_content_edit = QTextEdit()
        self.topic_content_edit.setMaximumHeight(150)
        form_layout.addRow("主题内容:", self.topic_content_edit)
        
        self.topic_groups_edit = QLineEdit()
        self.topic_groups_edit.setPlaceholderText("目标小组ID，用逗号分隔（留空表示全部小组）")
        form_layout.addRow("目标小组:", self.topic_groups_edit)
        
        left_layout.addLayout(form_layout)
        
        # 图片上传区域
        image_group = QGroupBox("图片附件")
        image_layout = QVBoxLayout()
        image_group.setLayout(image_layout)
        
        self.image_preview = QLabel("点击选择图片")
        self.image_preview.setMinimumHeight(100)
        self.image_preview.setStyleSheet("QLabel { border: 2px dashed gray; }")
        self.image_preview.setAlignment(Qt.AlignCenter)
        self.image_preview.mousePressEvent = self.select_image
        image_layout.addWidget(self.image_preview)
        
        left_layout.addWidget(image_group)
        
        # 主题按钮
        topic_button_layout = QHBoxLayout()
        
        self.create_topic_btn = QPushButton("创建主题")
        self.create_topic_btn.clicked.connect(self.create_discussion_topic)
        topic_button_layout.addWidget(self.create_topic_btn)
        
        left_layout.addLayout(topic_button_layout)
        
        # 右侧：主题列表
        right_panel = QGroupBox("主题列表")
        right_layout = QVBoxLayout()
        right_panel.setLayout(right_layout)
        
        self.topic_list = QListWidget()
        self.topic_list.itemClicked.connect(self.on_topic_selected)
        right_layout.addWidget(self.topic_list)
        
        # 主题操作按钮
        topic_control_layout = QHBoxLayout()
        
        self.distribute_topic_btn = QPushButton("分发主题")
        self.distribute_topic_btn.clicked.connect(self.distribute_discussion_topic)
        self.distribute_topic_btn.setEnabled(False)
        topic_control_layout.addWidget(self.distribute_topic_btn)
        
        self.refresh_topics_btn = QPushButton("刷新")
        self.refresh_topics_btn.clicked.connect(self.load_discussion_topics)
        topic_control_layout.addWidget(self.refresh_topics_btn)
        
        right_layout.addLayout(topic_control_layout)
        
        # 添加到主布局
        layout.addWidget(left_panel, 1)
        layout.addWidget(right_panel, 1)
        
        return tab
    
    def create_status_tab(self):
        """创建状态监控选项卡"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)
        
        # 活动显示状态
        display_group = QGroupBox("活动显示状态")
        display_layout = QVBoxLayout()
        display_group.setLayout(display_layout)
        
        self.active_displays_list = QListWidget()
        display_layout.addWidget(self.active_displays_list)
        
        layout.addWidget(display_group)
        
        # 系统状态
        system_group = QGroupBox("系统状态")
        system_layout = QGridLayout()
        system_group.setLayout(system_layout)
        
        # 连接状态
        system_layout.addWidget(QLabel("API连接状态:"), 0, 0)
        self.api_status_label = QLabel("未知")
        system_layout.addWidget(self.api_status_label, 0, 1)
        
        # 活动配置数量
        system_layout.addWidget(QLabel("活动配置数量:"), 1, 0)
        self.active_configs_label = QLabel("0")
        system_layout.addWidget(self.active_configs_label, 1, 1)
        
        # 活动计时器数量
        system_layout.addWidget(QLabel("活动计时器数量:"), 2, 0)
        self.active_timers_label = QLabel("0")
        system_layout.addWidget(self.active_timers_label, 2, 1)
        
        layout.addWidget(system_group)
        
        return tab
    
    def load_display_modes(self):
        """加载显示模式和布局类型"""
        try:
            response = requests.get(f"{self.api_base_url}/modes")
            if response.status_code == 200:
                data = response.json()
                
                # 加载显示模式
                self.display_mode_combo.clear()
                for mode in data.get('modes', []):
                    self.display_mode_combo.addItem(mode)
                
                # 加载布局类型
                self.layout_combo.clear()
                for layout in data.get('layouts', []):
                    self.layout_combo.addItem(layout)
                    
        except Exception as e:
            self.show_error(f"加载显示模式失败: {str(e)}")
    
    def set_classroom_id(self, classroom_id):
        """设置课堂ID"""
        self.classroom_id = classroom_id
        self.load_display_configs()
        self.load_group_timers()
        self.load_discussion_topics()
    
    def create_display_config(self):
        """创建显示配置"""
        if not self.classroom_id:
            self.show_error("请先选择课堂")
            return
        
        try:
            # 获取表单数据
            name = self.config_name_edit.text().strip()
            if not name:
                self.show_error("请输入配置名称")
                return
            
            mode = self.display_mode_combo.currentText()
            layout = self.layout_combo.currentText()
            target_devices = [d.strip() for d in self.target_devices_edit.text().split(',') if d.strip()]
            content_sources = [s.strip() for s in self.content_sources_edit.text().split(',') if s.strip()]
            sync_enabled = self.sync_enabled_checkbox.isChecked()
            
            if not target_devices:
                self.show_error("请输入目标设备")
                return
            
            if not content_sources:
                self.show_error("请输入内容源")
                return
            
            # 发送创建请求
            data = {
                'classroom_id': self.classroom_id,
                'mode': mode,
                'layout': layout,
                'target_devices': target_devices,
                'content_sources': content_sources,
                'sync_enabled': sync_enabled
            }
            
            response = requests.post(f"{self.api_base_url}/configs", json=data)
            if response.status_code == 201:
                self.show_success("显示配置创建成功")
                self.clear_config_form()
                self.load_display_configs()
            else:
                error_msg = response.json().get('error', '创建失败')
                self.show_error(f"创建显示配置失败: {error_msg}")
                
        except Exception as e:
            self.show_error(f"创建显示配置时出错: {str(e)}")
    
    def update_display_config(self):
        """更新显示配置"""
        # 实现配置更新逻辑
        pass
    
    def apply_display_config(self):
        """应用显示配置"""
        current_item = self.config_list.currentItem()
        if not current_item:
            self.show_error("请选择要应用的配置")
            return
        
        try:
            config_data = current_item.data(Qt.UserRole)
            config_id = config_data['config_id']
            
            response = requests.post(f"{self.api_base_url}/configs/{config_id}/apply")
            if response.status_code == 200:
                self.show_success("显示配置应用成功")
                self.config_applied.emit(config_data)
            else:
                error_msg = response.json().get('error', '应用失败')
                self.show_error(f"应用显示配置失败: {error_msg}")
                
        except Exception as e:
            self.show_error(f"应用显示配置时出错: {str(e)}")
    
    def delete_display_config(self):
        """删除显示配置"""
        current_item = self.config_list.currentItem()
        if not current_item:
            return
        
        config_data = current_item.data(Qt.UserRole)
        config_id = config_data['config_id']
        
        reply = QMessageBox.question(
            self, '确认删除', 
            f"确定要删除配置 '{config_data.get('name', config_id)}' 吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                response = requests.delete(f"{self.api_base_url}/configs/{config_id}")
                if response.status_code == 200:
                    self.show_success("显示配置删除成功")
                    self.load_display_configs()
                else:
                    error_msg = response.json().get('error', '删除失败')
                    self.show_error(f"删除显示配置失败: {error_msg}")
                    
            except Exception as e:
                self.show_error(f"删除显示配置时出错: {str(e)}")
    
    def stop_display(self):
        """停止显示"""
        current_item = self.config_list.currentItem()
        if not current_item:
            return
        
        try:
            config_data = current_item.data(Qt.UserRole)
            config_id = config_data['config_id']
            
            response = requests.post(f"{self.api_base_url}/displays/{config_id}/stop")
            if response.status_code == 200:
                self.show_success("显示已停止")
            else:
                error_msg = response.json().get('error', '停止失败')
                self.show_error(f"停止显示失败: {error_msg}")
                
        except Exception as e:
            self.show_error(f"停止显示时出错: {str(e)}")
    
    def load_display_configs(self):
        """加载显示配置列表"""
        if not self.classroom_id:
            return
        
        try:
            response = requests.get(f"{self.api_base_url}/configs", 
                                  params={'classroom_id': self.classroom_id})
            if response.status_code == 200:
                data = response.json()
                self.current_configs = data.get('configs', [])
                self.update_config_list()
            else:
                self.show_error("加载显示配置失败")
                
        except Exception as e:
            self.show_error(f"加载显示配置时出错: {str(e)}")
    
    def update_config_list(self):
        """更新配置列表显示"""
        self.config_list.clear()
        
        for config in self.current_configs:
            item_text = f"{config.get('name', config['config_id'])} - {config['mode']} ({config['layout']})"
            if config.get('is_active'):
                item_text += " [活动中]"
            
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, config)
            self.config_list.addItem(item)
    
    def on_config_selected(self, item):
        """配置选中事件"""
        config_data = item.data(Qt.UserRole)
        
        # 启用相关按钮
        self.update_config_btn.setEnabled(True)
        self.apply_config_btn.setEnabled(True)
        self.delete_config_btn.setEnabled(True)
        self.stop_display_btn.setEnabled(config_data.get('is_active', False))
        
        # 填充表单（用于编辑）
        self.config_name_edit.setText(config_data.get('name', ''))
        
        # 设置下拉框选中项
        mode_index = self.display_mode_combo.findText(config_data['mode'])
        if mode_index >= 0:
            self.display_mode_combo.setCurrentIndex(mode_index)
        
        layout_index = self.layout_combo.findText(config_data['layout'])
        if layout_index >= 0:
            self.layout_combo.setCurrentIndex(layout_index)
        
        self.target_devices_edit.setText(','.join(config_data.get('target_devices', [])))
        self.content_sources_edit.setText(','.join(config_data.get('content_sources', [])))
        self.sync_enabled_checkbox.setChecked(config_data.get('sync_enabled', True))
    
    def clear_config_form(self):
        """清空配置表单"""
        self.config_name_edit.clear()
        self.target_devices_edit.clear()
        self.content_sources_edit.clear()
        self.sync_enabled_checkbox.setChecked(True)
    
    def create_group_timer(self):
        """创建分组计时器"""
        if not self.classroom_id:
            self.show_error("请先选择课堂")
            return
        
        try:
            title = self.timer_title_edit.text().strip()
            if not title:
                self.show_error("请输入计时器标题")
                return
            
            duration = self.timer_duration_spin.value()
            group_ids = [g.strip() for g in self.timer_groups_edit.text().split(',') if g.strip()]
            
            if not group_ids:
                self.show_error("请输入目标小组")
                return
            
            data = {
                'classroom_id': self.classroom_id,
                'title': title,
                'duration': duration,
                'group_ids': group_ids
            }
            
            response = requests.post(f"{self.api_base_url}/timers", json=data)
            if response.status_code == 201:
                self.show_success("分组计时器创建成功")
                self.clear_timer_form()
                self.load_group_timers()
            else:
                error_msg = response.json().get('error', '创建失败')
                self.show_error(f"创建分组计时器失败: {error_msg}")
                
        except Exception as e:
            self.show_error(f"创建分组计时器时出错: {str(e)}")
    
    def start_group_timer(self):
        """启动分组计时器"""
        current_item = self.timer_list.currentItem()
        if not current_item:
            return
        
        try:
            timer_data = current_item.data(Qt.UserRole)
            timer_id = timer_data['timer_id']
            
            response = requests.post(f"{self.api_base_url}/timers/{timer_id}/start")
            if response.status_code == 200:
                self.show_success("计时器已启动")
                self.timer_started.emit(timer_data)
                self.load_group_timers()
            else:
                error_msg = response.json().get('error', '启动失败')
                self.show_error(f"启动计时器失败: {error_msg}")
                
        except Exception as e:
            self.show_error(f"启动计时器时出错: {str(e)}")
    
    def pause_group_timer(self):
        """暂停分组计时器"""
        current_item = self.timer_list.currentItem()
        if not current_item:
            return
        
        try:
            timer_data = current_item.data(Qt.UserRole)
            timer_id = timer_data['timer_id']
            
            response = requests.post(f"{self.api_base_url}/timers/{timer_id}/pause")
            if response.status_code == 200:
                self.show_success("计时器已暂停")
                self.load_group_timers()
            else:
                error_msg = response.json().get('error', '暂停失败')
                self.show_error(f"暂停计时器失败: {error_msg}")
                
        except Exception as e:
            self.show_error(f"暂停计时器时出错: {str(e)}")
    
    def resume_group_timer(self):
        """恢复分组计时器"""
        current_item = self.timer_list.currentItem()
        if not current_item:
            return
        
        try:
            timer_data = current_item.data(Qt.UserRole)
            timer_id = timer_data['timer_id']
            
            response = requests.post(f"{self.api_base_url}/timers/{timer_id}/resume")
            if response.status_code == 200:
                self.show_success("计时器已恢复")
                self.load_group_timers()
            else:
                error_msg = response.json().get('error', '恢复失败')
                self.show_error(f"恢复计时器失败: {error_msg}")
                
        except Exception as e:
            self.show_error(f"恢复计时器时出错: {str(e)}")
    
    def stop_group_timer(self):
        """停止分组计时器"""
        current_item = self.timer_list.currentItem()
        if not current_item:
            return
        
        try:
            timer_data = current_item.data(Qt.UserRole)
            timer_id = timer_data['timer_id']
            
            response = requests.post(f"{self.api_base_url}/timers/{timer_id}/stop")
            if response.status_code == 200:
                self.show_success("计时器已停止")
                self.load_group_timers()
            else:
                error_msg = response.json().get('error', '停止失败')
                self.show_error(f"停止计时器失败: {error_msg}")
                
        except Exception as e:
            self.show_error(f"停止计时器时出错: {str(e)}")
    
    def load_group_timers(self):
        """加载分组计时器列表"""
        if not self.classroom_id:
            return
        
        try:
            response = requests.get(f"{self.api_base_url}/timers", 
                                  params={'classroom_id': self.classroom_id})
            if response.status_code == 200:
                data = response.json()
                self.active_timers = data.get('timers', [])
                self.update_timer_list()
            else:
                self.show_error("加载分组计时器失败")
                
        except Exception as e:
            self.show_error(f"加载分组计时器时出错: {str(e)}")
    
    def update_timer_list(self):
        """更新计时器列表显示"""
        self.timer_list.clear()
        
        for timer in self.active_timers:
            status = "活动中" if timer.get('is_active') else "已停止"
            if timer.get('is_paused'):
                status = "已暂停"
            
            remaining = timer.get('remaining_time', 0)
            time_str = f"{remaining // 60:02d}:{remaining % 60:02d}"
            
            item_text = f"{timer['title']} - {status} ({time_str})"
            
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, timer)
            self.timer_list.addItem(item)
    
    def on_timer_selected(self, item):
        """计时器选中事件"""
        timer_data = item.data(Qt.UserRole)
        
        # 启用相关按钮
        is_active = timer_data.get('is_active', False)
        is_paused = timer_data.get('is_paused', False)
        
        self.start_timer_btn.setEnabled(not is_active)
        self.pause_timer_btn.setEnabled(is_active and not is_paused)
        self.resume_timer_btn.setEnabled(is_active and is_paused)
        self.stop_timer_btn.setEnabled(is_active)
        
        # 更新计时器显示
        remaining = timer_data.get('remaining_time', 0)
        time_str = f"{remaining // 60:02d}:{remaining % 60:02d}"
        self.timer_display.setText(time_str)
    
    def clear_timer_form(self):
        """清空计时器表单"""
        self.timer_title_edit.clear()
        self.timer_duration_spin.setValue(300)
        self.timer_groups_edit.clear()
    
    def create_discussion_topic(self):
        """创建讨论主题"""
        if not self.classroom_id:
            self.show_error("请先选择课堂")
            return
        
        try:
            title = self.topic_title_edit.text().strip()
            content = self.topic_content_edit.toPlainText().strip()
            
            if not title:
                self.show_error("请输入主题标题")
                return
            
            if not content:
                self.show_error("请输入主题内容")
                return
            
            target_groups = [g.strip() for g in self.topic_groups_edit.text().split(',') if g.strip()]
            
            data = {
                'classroom_id': self.classroom_id,
                'title': title,
                'content': content,
                'target_groups': target_groups
            }
            
            response = requests.post(f"{self.api_base_url}/topics", json=data)
            if response.status_code == 201:
                self.show_success("讨论主题创建成功")
                self.clear_topic_form()
                self.load_discussion_topics()
            else:
                error_msg = response.json().get('error', '创建失败')
                self.show_error(f"创建讨论主题失败: {error_msg}")
                
        except Exception as e:
            self.show_error(f"创建讨论主题时出错: {str(e)}")
    
    def distribute_discussion_topic(self):
        """分发讨论主题"""
        current_item = self.topic_list.currentItem()
        if not current_item:
            return
        
        try:
            topic_data = current_item.data(Qt.UserRole)
            topic_id = topic_data['topic_id']
            
            response = requests.post(f"{self.api_base_url}/topics/{topic_id}/distribute")
            if response.status_code == 200:
                self.show_success("讨论主题分发成功")
                self.topic_distributed.emit(topic_data)
                self.load_discussion_topics()
            else:
                error_msg = response.json().get('error', '分发失败')
                self.show_error(f"分发讨论主题失败: {error_msg}")
                
        except Exception as e:
            self.show_error(f"分发讨论主题时出错: {str(e)}")
    
    def load_discussion_topics(self):
        """加载讨论主题列表"""
        if not self.classroom_id:
            return
        
        try:
            response = requests.get(f"{self.api_base_url}/topics", 
                                  params={'classroom_id': self.classroom_id})
            if response.status_code == 200:
                data = response.json()
                self.discussion_topics = data.get('topics', [])
                self.update_topic_list()
            else:
                self.show_error("加载讨论主题失败")
                
        except Exception as e:
            self.show_error(f"加载讨论主题时出错: {str(e)}")
    
    def update_topic_list(self):
        """更新主题列表显示"""
        self.topic_list.clear()
        
        for topic in self.discussion_topics:
            status = "已分发" if topic.get('is_distributed') else "未分发"
            item_text = f"{topic['title']} - {status}"
            
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, topic)
            self.topic_list.addItem(item)
    
    def on_topic_selected(self, item):
        """主题选中事件"""
        topic_data = item.data(Qt.UserRole)
        
        # 启用分发按钮
        self.distribute_topic_btn.setEnabled(True)
        
        # 填充表单（用于查看）
        self.topic_title_edit.setText(topic_data['title'])
        self.topic_content_edit.setPlainText(topic_data['content'])
        self.topic_groups_edit.setText(','.join(topic_data.get('target_groups', [])))
    
    def clear_topic_form(self):
        """清空主题表单"""
        self.topic_title_edit.clear()
        self.topic_content_edit.clear()
        self.topic_groups_edit.clear()
    
    def select_image(self, event):
        """选择图片"""
        from PyQt5.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图片", "", 
            "图片文件 (*.png *.jpg *.jpeg *.gif *.bmp)"
        )
        
        if file_path:
            # 这里应该处理图片上传和Base64编码
            self.image_preview.setText(f"已选择: {file_path}")
    
    def update_status(self):
        """更新状态显示"""
        # 更新计时器显示
        current_timer_item = self.timer_list.currentItem()
        if current_timer_item:
            timer_data = current_timer_item.data(Qt.UserRole)
            if timer_data.get('is_active'):
                # 这里应该从服务器获取最新的计时器状态
                pass
        
        # 更新系统状态
        try:
            # 检查API连接状态
            response = requests.get(f"{self.api_base_url}/modes", timeout=2)
            if response.status_code == 200:
                self.api_status_label.setText("正常")
                self.api_status_label.setStyleSheet("color: green")
            else:
                self.api_status_label.setText("异常")
                self.api_status_label.setStyleSheet("color: red")
        except:
            self.api_status_label.setText("断开")
            self.api_status_label.setStyleSheet("color: red")
        
        # 更新活动配置和计时器数量
        active_configs = len([c for c in self.current_configs if c.get('is_active')])
        active_timers = len([t for t in self.active_timers if t.get('is_active')])
        
        self.active_configs_label.setText(str(active_configs))
        self.active_timers_label.setText(str(active_timers))
    
    def show_success(self, message):
        """显示成功消息"""
        QMessageBox.information(self, "成功", message)
    
    def show_error(self, message):
        """显示错误消息"""
        QMessageBox.critical(self, "错误", message)