#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的视频面板 - 支持多屏显示和4个学生同时投屏对比
"""

import sys
import os
import json
import requests
from datetime import datetime

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QPushButton, QLabel,
    QGroupBox, QComboBox, QSlider, QFrame, QScrollArea, QSplitter,
    QButtonGroup, QRadioButton, QCheckBox, QSpinBox, QMessageBox,
    QMenu, QAction, QToolButton, QSizePolicy
)
from PyQt5.QtCore import Qt, QSize, QTimer, pyqtSignal, QThread, QUrl
from PyQt5.QtGui import QPixmap, QPainter, QColor, QFont
from PyQt5.QtWebEngineWidgets import QWebEngineView

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from shared.ui_themes import theme_manager
from shared.config import get_config

config = get_config()

class VideoDisplayWidget(QFrame):
    """视频显示组件"""
    
    clicked = pyqtSignal(str)  # 点击信号，传递stream_id
    
    def __init__(self, stream_id=None, parent=None):
        super().__init__(parent)
        self.stream_id = stream_id
        self.stream_info = {}
        self.is_selected = False
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMinimumSize(200, 150)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)
        
        # 视频显示区域
        self.video_view = QWebEngineView()
        self.video_view.setMinimumSize(180, 120)
        layout.addWidget(self.video_view)
        
        # 信息标签
        self.info_label = QLabel("无视频流")
        self.info_label.setAlignment(Qt.AlignCenter)
        self.info_label.setStyleSheet("background-color: rgba(0,0,0,0.7); color: white; padding: 4px;")
        layout.addWidget(self.info_label)
        
        # 控制按钮
        controls_layout = QHBoxLayout()
        controls_layout.setContentsMargins(0, 0, 0, 0)
        
        self.play_btn = QPushButton("播放")
        self.play_btn.setMaximumHeight(25)
        self.play_btn.clicked.connect(self.toggle_play)
        controls_layout.addWidget(self.play_btn)
        
        self.fullscreen_btn = QPushButton("全屏")
        self.fullscreen_btn.setMaximumHeight(25)
        self.fullscreen_btn.clicked.connect(self.toggle_fullscreen)
        controls_layout.addWidget(self.fullscreen_btn)
        
        layout.addLayout(controls_layout)
        
        # 设置样式
        self.update_style()
    
    def set_stream(self, stream_info):
        """设置视频流"""
        self.stream_info = stream_info
        self.stream_id = stream_info.get('stream_id')
        
        # 更新信息标签
        source_name = stream_info.get('source_name', '未知设备')
        stream_type = stream_info.get('stream_type', '未知类型')
        self.info_label.setText(f"{source_name} - {stream_type}")
        
        # 加载视频流
        stream_url = stream_info.get('stream_url')
        if stream_url:
            self.load_stream(stream_url)
    
    def load_stream(self, stream_url):
        """加载视频流"""
        # 创建HTML5视频播放器
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {{ margin: 0; padding: 0; background: black; }}
                video {{ width: 100%; height: 100%; object-fit: contain; }}
            </style>
        </head>
        <body>
            <video id="video" autoplay muted controls>
                <source src="{stream_url}" type="application/x-mpegURL">
                <source src="{stream_url}" type="video/mp4">
                您的浏览器不支持视频播放。
            </video>
            <script>
                const video = document.getElementById('video');
                video.addEventListener('error', function(e) {{
                    console.error('视频加载错误:', e);
                }});
                video.addEventListener('loadstart', function() {{
                    console.log('开始加载视频');
                }});
                video.addEventListener('canplay', function() {{
                    console.log('视频可以播放');
                }});
            </script>
        </body>
        </html>
        """
        
        self.video_view.setHtml(html_content)
    
    def toggle_play(self):
        """切换播放状态"""
        # 通过JavaScript控制视频播放
        if self.play_btn.text() == "播放":
            self.video_view.page().runJavaScript("document.getElementById('video').play();")
            self.play_btn.setText("暂停")
        else:
            self.video_view.page().runJavaScript("document.getElementById('video').pause();")
            self.play_btn.setText("播放")
    
    def toggle_fullscreen(self):
        """切换全屏"""
        if self.isFullScreen():
            self.showNormal()
            self.fullscreen_btn.setText("全屏")
        else:
            self.showFullScreen()
            self.fullscreen_btn.setText("退出全屏")
    
    def set_selected(self, selected):
        """设置选中状态"""
        self.is_selected = selected
        self.update_style()
    
    def update_style(self):
        """更新样式"""
        if self.is_selected:
            self.setStyleSheet("""
                QFrame {
                    border: 3px solid #3498db;
                    background-color: #ecf0f1;
                }
            """)
        else:
            self.setStyleSheet("""
                QFrame {
                    border: 1px solid #bdc3c7;
                    background-color: white;
                }
                QFrame:hover {
                    border: 2px solid #3498db;
                }
            """)
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.stream_id or "")
        super().mousePressEvent(event)

class DisplayLayoutWidget(QWidget):
    """显示布局控制组件"""
    
    layout_changed = pyqtSignal(str)  # 布局变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_layout = "grid_2x2"
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 布局选择
        layout_group = QGroupBox("显示布局")
        layout_group_layout = QGridLayout(layout_group)
        
        self.layout_buttons = QButtonGroup()
        
        # 单屏显示
        single_btn = QRadioButton("单屏显示")
        single_btn.clicked.connect(lambda: self.set_layout("single"))
        self.layout_buttons.addButton(single_btn, 0)
        layout_group_layout.addWidget(single_btn, 0, 0)
        
        # 二分屏
        split2_btn = QRadioButton("二分屏")
        split2_btn.clicked.connect(lambda: self.set_layout("split_2"))
        self.layout_buttons.addButton(split2_btn, 1)
        layout_group_layout.addWidget(split2_btn, 0, 1)
        
        # 四分屏
        split4_btn = QRadioButton("四分屏")
        split4_btn.setChecked(True)
        split4_btn.clicked.connect(lambda: self.set_layout("grid_2x2"))
        self.layout_buttons.addButton(split4_btn, 2)
        layout_group_layout.addWidget(split4_btn, 1, 0)
        
        # 六分屏
        split6_btn = QRadioButton("六分屏")
        split6_btn.clicked.connect(lambda: self.set_layout("grid_2x3"))
        self.layout_buttons.addButton(split6_btn, 3)
        layout_group_layout.addWidget(split6_btn, 1, 1)
        
        # 九分屏
        split9_btn = QRadioButton("九分屏")
        split9_btn.clicked.connect(lambda: self.set_layout("grid_3x3"))
        self.layout_buttons.addButton(split9_btn, 4)
        layout_group_layout.addWidget(split9_btn, 2, 0)
        
        # 自定义布局
        custom_btn = QRadioButton("自定义")
        custom_btn.clicked.connect(lambda: self.set_layout("custom"))
        self.layout_buttons.addButton(custom_btn, 5)
        layout_group_layout.addWidget(custom_btn, 2, 1)
        
        layout.addWidget(layout_group)
        
        # 显示模式
        mode_group = QGroupBox("显示模式")
        mode_layout = QVBoxLayout(mode_group)
        
        self.broadcast_mode = QCheckBox("广播模式")
        self.broadcast_mode.stateChanged.connect(self.on_mode_changed)
        mode_layout.addWidget(self.broadcast_mode)
        
        self.comparison_mode = QCheckBox("对比模式")
        self.comparison_mode.stateChanged.connect(self.on_mode_changed)
        mode_layout.addWidget(self.comparison_mode)
        
        self.sync_mode = QCheckBox("同步模式")
        self.sync_mode.stateChanged.connect(self.on_mode_changed)
        mode_layout.addWidget(self.sync_mode)
        
        layout.addWidget(mode_group)
        
        # 应用主题
        self.setStyleSheet(theme_manager.get_stylesheet('panel'))
    
    def set_layout(self, layout_type):
        """设置布局类型"""
        self.current_layout = layout_type
        self.layout_changed.emit(layout_type)
    
    def on_mode_changed(self):
        """模式变化处理"""
        modes = []
        if self.broadcast_mode.isChecked():
            modes.append("broadcast")
        if self.comparison_mode.isChecked():
            modes.append("comparison")
        if self.sync_mode.isChecked():
            modes.append("sync")
        
        # 发送模式变化信号
        self.send_mode_update(modes)
    
    def send_mode_update(self, modes):
        """发送模式更新"""
        try:
            response = requests.post(
                f"http://{config.BACKEND_HOST}:{config.BACKEND_PORT}/api/display/mode",
                json={
                    'modes': modes,
                    'layout': self.current_layout,
                    'timestamp': datetime.now().isoformat()
                }
            )
            
            if response.status_code != 200:
                QMessageBox.warning(self, "警告", f"更新显示模式失败: {response.status_code}")
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"更新显示模式时出错: {str(e)}")

class StudentScreenPanel(QWidget):
    """学生投屏面板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.student_streams = {}
        self.max_streams = 4  # 最多支持4个学生同时投屏
        
        self.init_ui()
        self.load_student_streams()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.load_student_streams)
        control_layout.addWidget(refresh_btn)
        
        # 清除所有按钮
        clear_btn = QPushButton("清除所有")
        clear_btn.clicked.connect(self.clear_all_streams)
        control_layout.addWidget(clear_btn)
        
        # 布局选择
        layout_combo = QComboBox()
        layout_combo.addItems(["2x2网格", "1+3布局", "水平排列", "垂直排列"])
        layout_combo.currentTextChanged.connect(self.change_layout)
        control_layout.addWidget(layout_combo)
        
        control_layout.addStretch()
        layout.addWidget(control_panel)
        
        # 学生投屏显示区域
        self.display_area = QWidget()
        self.display_layout = QGridLayout(self.display_area)
        self.display_layout.setSpacing(5)
        
        # 创建4个显示位置
        self.display_widgets = []
        for i in range(self.max_streams):
            display_widget = VideoDisplayWidget()
            display_widget.clicked.connect(self.on_stream_clicked)
            
            row = i // 2
            col = i % 2
            self.display_layout.addWidget(display_widget, row, col)
            self.display_widgets.append(display_widget)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.display_area)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        # 学生列表
        students_group = QGroupBox("在线学生")
        students_layout = QVBoxLayout(students_group)
        
        self.students_list = QWidget()
        self.students_list_layout = QVBoxLayout(self.students_list)
        
        students_scroll = QScrollArea()
        students_scroll.setWidget(self.students_list)
        students_scroll.setWidgetResizable(True)
        students_scroll.setMaximumHeight(200)
        students_layout.addWidget(students_scroll)
        
        layout.addWidget(students_group)
        
        # 应用主题
        self.setStyleSheet(theme_manager.get_stylesheet('panel'))
    
    def load_student_streams(self):
        """加载学生投屏流"""
        try:
            response = requests.get(
                f"http://{config.BACKEND_HOST}:{config.BACKEND_PORT}/api/video/streams/student"
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    streams = result.get('streams', [])
                    self.update_student_streams(streams)
                    self.update_students_list()
                else:
                    QMessageBox.warning(self, "警告", f"获取学生流失败: {result.get('message')}")
            else:
                QMessageBox.critical(self, "错误", f"请求失败: HTTP {response.status_code}")
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载学生流时出错: {str(e)}")
    
    def update_student_streams(self, streams):
        """更新学生投屏流"""
        # 清除现有流
        for widget in self.display_widgets:
            widget.set_stream({})
        
        # 显示新流（最多4个）
        for i, stream in enumerate(streams[:self.max_streams]):
            self.display_widgets[i].set_stream(stream)
    
    def update_students_list(self):
        """更新学生列表"""
        # 清除现有列表
        for i in reversed(range(self.students_list_layout.count())):
            child = self.students_list_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        try:
            # 获取在线学生列表
            response = requests.get(
                f"http://{config.BACKEND_HOST}:{config.BACKEND_PORT}/api/students/online"
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    students = result.get('students', [])
                    
                    for student in students:
                        student_widget = self.create_student_widget(student)
                        self.students_list_layout.addWidget(student_widget)
                else:
                    error_label = QLabel(f"获取学生列表失败: {result.get('message')}")
                    self.students_list_layout.addWidget(error_label)
            else:
                error_label = QLabel(f"请求失败: HTTP {response.status_code}")
                self.students_list_layout.addWidget(error_label)
        
        except Exception as e:
            error_label = QLabel(f"获取学生列表时出错: {str(e)}")
            self.students_list_layout.addWidget(error_label)
    
    def create_student_widget(self, student):
        """创建学生组件"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(widget)
        
        # 学生信息
        info_label = QLabel(f"{student.get('name', '未知')} ({student.get('group_name', '未分组')})")
        layout.addWidget(info_label)
        
        # 状态标签
        status = student.get('screen_sharing', False)
        status_label = QLabel("投屏中" if status else "未投屏")
        status_label.setStyleSheet(f"color: {'green' if status else 'gray'};")
        layout.addWidget(status_label)
        
        # 操作按钮
        if status:
            stop_btn = QPushButton("停止投屏")
            stop_btn.clicked.connect(lambda: self.stop_student_screen(student.get('id')))
            layout.addWidget(stop_btn)
        else:
            start_btn = QPushButton("开始投屏")
            start_btn.clicked.connect(lambda: self.start_student_screen(student.get('id')))
            layout.addWidget(start_btn)
        
        return widget
    
    def start_student_screen(self, student_id):
        """开始学生投屏"""
        try:
            response = requests.post(
                f"http://{config.BACKEND_HOST}:{config.BACKEND_PORT}/api/students/{student_id}/screen/start"
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    QMessageBox.information(self, "成功", "学生投屏已开始")
                    self.load_student_streams()
                else:
                    QMessageBox.warning(self, "警告", f"开始投屏失败: {result.get('message')}")
            else:
                QMessageBox.critical(self, "错误", f"请求失败: HTTP {response.status_code}")
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"开始学生投屏时出错: {str(e)}")
    
    def stop_student_screen(self, student_id):
        """停止学生投屏"""
        try:
            response = requests.post(
                f"http://{config.BACKEND_HOST}:{config.BACKEND_PORT}/api/students/{student_id}/screen/stop"
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    QMessageBox.information(self, "成功", "学生投屏已停止")
                    self.load_student_streams()
                else:
                    QMessageBox.warning(self, "警告", f"停止投屏失败: {result.get('message')}")
            else:
                QMessageBox.critical(self, "错误", f"请求失败: HTTP {response.status_code}")
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"停止学生投屏时出错: {str(e)}")
    
    def clear_all_streams(self):
        """清除所有投屏"""
        for widget in self.display_widgets:
            widget.set_stream({})
        
        # 发送停止所有投屏请求
        try:
            response = requests.post(
                f"http://{config.BACKEND_HOST}:{config.BACKEND_PORT}/api/students/screen/stop_all"
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    QMessageBox.information(self, "成功", "所有投屏已停止")
                    self.update_students_list()
                else:
                    QMessageBox.warning(self, "警告", f"停止投屏失败: {result.get('message')}")
            else:
                QMessageBox.critical(self, "错误", f"请求失败: HTTP {response.status_code}")
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"停止所有投屏时出错: {str(e)}")
    
    def change_layout(self, layout_name):
        """改变布局"""
        if layout_name == "2x2网格":
            # 2x2网格布局
            for i, widget in enumerate(self.display_widgets):
                row = i // 2
                col = i % 2
                self.display_layout.addWidget(widget, row, col)
        
        elif layout_name == "1+3布局":
            # 1个大屏 + 3个小屏
            self.display_layout.addWidget(self.display_widgets[0], 0, 0, 2, 2)  # 大屏占2x2
            self.display_layout.addWidget(self.display_widgets[1], 0, 2)
            self.display_layout.addWidget(self.display_widgets[2], 1, 2)
            self.display_layout.addWidget(self.display_widgets[3], 2, 0, 1, 3)
        
        elif layout_name == "水平排列":
            # 水平排列
            for i, widget in enumerate(self.display_widgets):
                self.display_layout.addWidget(widget, 0, i)
        
        elif layout_name == "垂直排列":
            # 垂直排列
            for i, widget in enumerate(self.display_widgets):
                self.display_layout.addWidget(widget, i, 0)
    
    def on_stream_clicked(self, stream_id):
        """流点击处理"""
        # 高亮选中的流
        for widget in self.display_widgets:
            widget.set_selected(widget.stream_id == stream_id)

class EnhancedVideoPanel(QWidget):
    """增强的视频面板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：显示布局控制
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        self.layout_widget = DisplayLayoutWidget(self)
        self.layout_widget.layout_changed.connect(self.on_layout_changed)
        left_layout.addWidget(self.layout_widget)
        
        left_layout.addStretch()
        splitter.addWidget(left_panel)
        
        # 右侧：学生投屏面板
        self.student_panel = StudentScreenPanel(self)
        splitter.addWidget(self.student_panel)
        
        # 设置分割器比例
        splitter.setSizes([200, 600])
        
        layout.addWidget(splitter)
        
        # 应用主题
        self.setStyleSheet(theme_manager.get_stylesheet('panel'))
    
    def on_layout_changed(self, layout_type):
        """布局变化处理"""
        # 根据布局类型调整学生面板显示
        if layout_type == "single":
            # 单屏模式，只显示一个流
            pass
        elif layout_type == "split_2":
            # 二分屏模式
            pass
        elif layout_type == "grid_2x2":
            # 四分屏模式
            pass
        
        # 通知学生面板更新布局
        self.student_panel.change_layout(layout_type)