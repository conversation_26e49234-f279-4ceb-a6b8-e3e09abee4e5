#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教师端视频面板
"""

import os
import sys
import requests
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QGridLayout,
    QScrollArea, QFrame, QSplitter, QComboBox, QMessageBox, QMenu, QAction,
    QDialog, QDialogButtonBox, QListWidget, QListWidgetItem, QCheckBox
)
from PyQt5.QtCore import Qt, QSize, QUrl, QTimer, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QIcon, QPixmap
from PyQt5.QtMultimedia import QMediaContent, QMediaPlayer
from PyQt5.QtMultimediaWidgets import QVideoWidget

class VideoStream(QWidget):
    """视频流显示组件"""
    
    def __init__(self, stream_id, source_id, stream_type, url, parent=None):
        super().__init__(parent)
        self.stream_id = stream_id
        self.source_id = source_id
        self.stream_type = stream_type
        self.url = url
        self.parent = parent
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建视频播放器
        self.video_widget = QVideoWidget()
        self.video_widget.setMinimumSize(320, 240)
        
        self.media_player = QMediaPlayer(self)
        self.media_player.setVideoOutput(self.video_widget)
        
        # 设置视频源
        self.media_player.setMedia(QMediaContent(QUrl(self.url)))
        
        # 添加视频控件
        layout.addWidget(self.video_widget)
        
        # 创建控制栏
        control_layout = QHBoxLayout()
        
        # 添加标题标签
        title = f"{self.source_id} ({self.stream_type})"
        title_label = QLabel(title)
        control_layout.addWidget(title_label)
        
        # 添加控制按钮
        control_layout.addStretch()
        
        # 停止按钮
        stop_btn = QPushButton("停止")
        stop_btn.clicked.connect(self.stop_stream)
        control_layout.addWidget(stop_btn)
        
        # 广播按钮
        broadcast_btn = QPushButton("广播")
        broadcast_btn.clicked.connect(self.broadcast_stream)
        control_layout.addWidget(broadcast_btn)
        
        # 添加控制栏到布局
        layout.addLayout(control_layout)
        
        # 开始播放
        self.media_player.play()
    
    def stop_stream(self):
        """停止流"""
        if self.parent:
            self.parent.stop_stream(self.stream_id)
    
    def broadcast_stream(self):
        """广播流"""
        if self.parent:
            self.parent.broadcast_stream(self.stream_id)
    
    def closeEvent(self, event):
        """关闭事件处理"""
        # 停止播放器
        self.media_player.stop()
        event.accept()

class VideoPanel(QWidget):
    """视频面板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.streams = {}  # 存储流信息
        self.stream_widgets = {}  # 存储流组件
        self.current_layout = "grid"  # 当前布局模式：grid或single
        self.current_stream = None  # 当前选中的流
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 创建主布局
        layout = QVBoxLayout(self)
        
        # 创建控制栏
        control_layout = QHBoxLayout()
        
        # 添加布局选择下拉框
        layout_label = QLabel("布局:")
        control_layout.addWidget(layout_label)
        
        self.layout_combo = QComboBox()
        self.layout_combo.addItem("网格布局", "grid")
        self.layout_combo.addItem("单视图", "single")
        self.layout_combo.currentIndexChanged.connect(self.change_layout)
        control_layout.addWidget(self.layout_combo)
        
        # 添加刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh)
        control_layout.addWidget(refresh_btn)
        
        # 添加控制栏到主布局
        layout.addLayout(control_layout)
        
        # 创建滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setFrameShape(QFrame.NoFrame)
        
        # 创建内容区域
        self.content_widget = QWidget()
        self.grid_layout = QGridLayout(self.content_widget)
        self.grid_layout.setContentsMargins(0, 0, 0, 0)
        self.grid_layout.setSpacing(10)
        
        self.scroll_area.setWidget(self.content_widget)
        layout.addWidget(self.scroll_area)
        
        # 创建状态标签
        self.status_label = QLabel("视频流: 0")
        layout.addWidget(self.status_label)
    
    def refresh(self):
        """刷新视频流列表"""
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 发送请求获取流列表
            response = requests.get(f"{api_base_url}/video/streams")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.update_streams(data.get('streams', []))
                else:
                    self.parent.show_error(f"获取视频流列表失败: {data.get('message')}")
            else:
                self.parent.show_error(f"获取视频流列表失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.parent.show_error(f"刷新视频流列表时出错: {str(e)}")
    
    def update_streams(self, streams):
        """更新视频流列表"""
        # 记录当前流ID
        current_stream_ids = set(self.streams.keys())
        new_stream_ids = set()
        
        # 添加或更新流
        for stream in streams:
            stream_id = stream.get('stream_id')
            new_stream_ids.add(stream_id)
            
            if stream_id not in self.streams:
                # 新流
                self.add_stream(stream)
            else:
                # 更新流
                self.streams[stream_id] = stream
        
        # 移除不存在的流
        for stream_id in current_stream_ids - new_stream_ids:
            self.remove_stream(stream_id)
        
        # 更新状态标签
        self.status_label.setText(f"视频流: {len(self.streams)}")
    
    def add_stream(self, stream):
        """添加视频流"""
        stream_id = stream.get('stream_id')
        source_id = stream.get('source_id')
        stream_type = stream.get('stream_type')
        http_url = stream.get('http_url')
        
        # 存储流信息
        self.streams[stream_id] = stream
        
        # 创建视频流组件
        stream_widget = VideoStream(stream_id, source_id, stream_type, http_url, self)
        self.stream_widgets[stream_id] = stream_widget
        
        # 添加到布局
        self.update_layout()
        
        # 更新状态标签
        self.status_label.setText(f"视频流: {len(self.streams)}")
    
    def remove_stream(self, stream_id):
        """移除视频流"""
        if stream_id in self.streams:
            # 移除流信息
            del self.streams[stream_id]
            
            # 移除视频流组件
            if stream_id in self.stream_widgets:
                widget = self.stream_widgets[stream_id]
                widget.close()
                widget.deleteLater()
                del self.stream_widgets[stream_id]
            
            # 更新布局
            self.update_layout()
            
            # 更新状态标签
            self.status_label.setText(f"视频流: {len(self.streams)}")
    
    def change_layout(self, index):
        """切换布局模式"""
        self.current_layout = self.layout_combo.currentData()
        self.update_layout()
    
    def update_layout(self):
        """更新布局"""
        # 清空布局
        while self.grid_layout.count():
            item = self.grid_layout.takeAt(0)
            if item.widget():
                item.widget().setParent(None)
        
        if self.current_layout == "grid":
            # 网格布局
            col_count = 2  # 每行2个视频
            row = 0
            col = 0
            
            for stream_id, widget in self.stream_widgets.items():
                self.grid_layout.addWidget(widget, row, col)
                
                col += 1
                if col >= col_count:
                    col = 0
                    row += 1
        
        elif self.current_layout == "single" and self.stream_widgets:
            # 单视图布局
            if self.current_stream and self.current_stream in self.stream_widgets:
                # 显示当前选中的流
                self.grid_layout.addWidget(self.stream_widgets[self.current_stream], 0, 0)
            else:
                # 显示第一个流
                stream_id = next(iter(self.stream_widgets))
                self.current_stream = stream_id
                self.grid_layout.addWidget(self.stream_widgets[stream_id], 0, 0)
    
    def stop_stream(self, stream_id):
        """停止流"""
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 发送请求停止流
            response = requests.post(f"{api_base_url}/video/streams/{stream_id}/stop")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.parent.status_bar.showMessage(f"流已停止: {stream_id}", 3000)
                else:
                    self.parent.show_error(f"停止流失败: {data.get('message')}")
            else:
                self.parent.show_error(f"停止流失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.parent.show_error(f"停止流时出错: {str(e)}")
    
    def broadcast_stream(self, stream_id):
        """广播流"""
        # 获取小组列表
        groups = self.get_groups()
        
        if not groups:
            self.parent.show_error("没有可用的小组")
            return
        
        # 创建广播对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("广播流")
        dialog.setMinimumWidth(300)
        
        # 创建布局
        layout = QVBoxLayout(dialog)
        
        # 添加说明标签
        layout.addWidget(QLabel("选择要广播到的小组:"))
        
        # 添加小组列表
        group_list = QListWidget()
        
        for group_id, group_name in groups:
            item = QListWidgetItem(group_name)
            item.setData(Qt.UserRole, group_id)
            item.setCheckState(Qt.Checked)
            group_list.addItem(item)
        
        layout.addWidget(group_list)
        
        # 添加按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)
        
        # 显示对话框
        if dialog.exec_() == QDialog.Accepted:
            # 获取选中的小组
            selected_groups = []
            
            for i in range(group_list.count()):
                item = group_list.item(i)
                if item.checkState() == Qt.Checked:
                    selected_groups.append(item.data(Qt.UserRole))
            
            if selected_groups:
                self.broadcast_stream_to_groups(stream_id, selected_groups)
            else:
                self.parent.status_bar.showMessage("未选择任何小组", 3000)
    
    def broadcast_stream_to_groups(self, stream_id, target_groups):
        """广播流到指定小组"""
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 发送请求广播流
            response = requests.post(
                f"{api_base_url}/video/broadcast",
                json={
                    "source_stream_id": stream_id,
                    "target_groups": target_groups
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.parent.status_bar.showMessage(
                        f"流已广播到 {len(target_groups)} 个小组", 3000
                    )
                else:
                    self.parent.show_error(f"广播流失败: {data.get('message')}")
            else:
                self.parent.show_error(f"广播流失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.parent.show_error(f"广播流时出错: {str(e)}")
    
    def get_groups(self):
        """获取小组列表"""
        groups = []
        
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 获取当前课堂ID
            classroom_id = self.get_current_classroom_id()
            if not classroom_id:
                return groups
            
            # 发送请求获取小组列表
            response = requests.get(f"{api_base_url}/groups?classroom_id={classroom_id}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    for group in data.get('groups', []):
                        groups.append((group.get('id'), group.get('name', '未命名小组')))
            
        except Exception as e:
            self.parent.show_error(f"获取小组列表时出错: {str(e)}")
        
        return groups
    
    def get_current_classroom_id(self):
        """获取当前选中的课堂ID"""
        # 从主窗口的课堂选择下拉框获取当前课堂ID
        combo = self.parent.classroom_combo
        return combo.currentData() if combo.currentIndex() >= 0 else None
    
    def start_broadcast(self):
        """开始屏幕广播"""
        # 获取当前设备ID
        device_id = self.get_current_device_id()
        if not device_id:
            self.parent.show_error("请先选择设备")
            return
        
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 发送请求开始屏幕捕获
            response = requests.post(
                f"{api_base_url}/video/screen/start",
                json={"device_id": device_id}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    stream_id = data.get('stream', {}).get('stream_id')
                    self.parent.status_bar.showMessage(f"屏幕捕获已启动: {stream_id}", 3000)
                else:
                    self.parent.show_error(f"开始屏幕捕获失败: {data.get('message')}")
            else:
                self.parent.show_error(f"开始屏幕捕获失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.parent.show_error(f"开始屏幕捕获时出错: {str(e)}")
    
    def stop_broadcast(self):
        """停止屏幕广播"""
        # 获取当前设备ID
        device_id = self.get_current_device_id()
        if not device_id:
            self.parent.show_error("请先选择设备")
            return
        
        # 查找该设备的屏幕流
        screen_streams = []
        for stream_id, stream in self.streams.items():
            if stream.get('source_id') == device_id and stream.get('stream_type') == 'screen':
                screen_streams.append(stream_id)
        
        if not screen_streams:
            self.parent.status_bar.showMessage("没有找到该设备的屏幕流", 3000)
            return
        
        # 停止所有屏幕流
        for stream_id in screen_streams:
            self.stop_stream(stream_id)
    
    def get_current_device_id(self):
        """获取当前选中的设备ID"""
        # 这里应该从设备面板获取当前选中的设备ID
        # 暂时返回一个测试设备ID
        return "teacher_device"
    
    def get_stream_count(self):
        """获取流数量"""
        return len(self.streams)