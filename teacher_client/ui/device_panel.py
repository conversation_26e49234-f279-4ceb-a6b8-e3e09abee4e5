#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教师端设备管理面板
"""

import os
import sys
import requests
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTreeWidget,
    QTreeWidgetItem, QHeaderView, QMenu, QAction, QMessageBox, QGroupBox,
    QComboBox, QLineEdit, QFormLayout
)
from PyQt5.QtCore import Qt, QSize, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QIcon, QColor, QBrush

class DevicePanel(QWidget):
    """设备管理面板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.devices = {}  # 存储设备信息
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 创建主布局
        layout = QVBoxLayout(self)
        
        # 创建操作区域
        action_layout = QHBoxLayout()
        
        # 添加刷新按钮
        refresh_btn = QPushButton("刷新设备")
        refresh_btn.clicked.connect(self.refresh)
        action_layout.addWidget(refresh_btn)
        
        # 添加发现设备按钮
        discover_btn = QPushButton("发现设备")
        discover_btn.clicked.connect(self.discover_devices)
        action_layout.addWidget(discover_btn)
        
        # 添加操作区域到主布局
        layout.addLayout(action_layout)
        
        # 创建设备过滤区域
        filter_group = QGroupBox("设备过滤")
        filter_layout = QFormLayout()
        
        # 设备类型过滤
        self.type_filter = QComboBox()
        self.type_filter.addItem("全部", "all")
        self.type_filter.addItem("小组端", "group")
        self.type_filter.addItem("学生端", "student")
        self.type_filter.currentIndexChanged.connect(self.apply_filter)
        filter_layout.addRow("设备类型:", self.type_filter)
        
        # 设备状态过滤
        self.status_filter = QComboBox()
        self.status_filter.addItem("全部", "all")
        self.status_filter.addItem("在线", "online")
        self.status_filter.addItem("离线", "offline")
        self.status_filter.currentIndexChanged.connect(self.apply_filter)
        filter_layout.addRow("设备状态:", self.status_filter)
        
        # 设备名称搜索
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入设备名称搜索...")
        self.search_input.textChanged.connect(self.apply_filter)
        filter_layout.addRow("搜索:", self.search_input)
        
        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)
        
        # 创建设备树形视图
        self.device_tree = QTreeWidget()
        self.device_tree.setHeaderLabels(["设备名称", "设备类型", "IP地址", "状态", "最后心跳"])
        self.device_tree.setAlternatingRowColors(True)
        self.device_tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.device_tree.customContextMenuRequested.connect(self.show_context_menu)
        
        # 设置列宽
        header = self.device_tree.header()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        layout.addWidget(self.device_tree)
        
        # 创建状态标签
        self.status_label = QLabel("设备总数: 0 | 在线: 0 | 离线: 0")
        layout.addWidget(self.status_label)
        
        # 初始加载设备
        self.refresh()
    
    def refresh(self):
        """刷新设备列表"""
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 发送请求获取设备列表
            response = requests.get(f"{api_base_url}/devices")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.update_devices(data.get('devices', []))
                else:
                    self.parent.show_error(f"获取设备列表失败: {data.get('message')}")
            else:
                self.parent.show_error(f"获取设备列表失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.parent.show_error(f"刷新设备列表时出错: {str(e)}")
    
    def discover_devices(self):
        """发现设备"""
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 发送请求触发设备发现
            response = requests.post(f"{api_base_url}/devices/discover")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.parent.status_bar.showMessage(f"已发送设备发现广播", 3000)
                else:
                    self.parent.show_error(f"发送设备发现广播失败: {data.get('message')}")
            else:
                self.parent.show_error(f"发送设备发现广播失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.parent.show_error(f"发现设备时出错: {str(e)}")
    
    def update_devices(self, devices):
        """更新设备列表"""
        # 清空设备树
        self.device_tree.clear()
        self.devices = {}
        
        # 添加设备到树形视图
        for device in devices:
            self.add_device(device)
        
        # 更新状态标签
        self.update_status_label()
        
        # 应用过滤器
        self.apply_filter()
    
    def add_device(self, device):
        """添加设备到树形视图"""
        device_id = device.get('device_id')
        device_name = device.get('device_name', '未命名设备')
        device_type = device.get('device_type', '未知')
        ip_address = device.get('ip_address', '未知')
        status = device.get('status', 'offline')
        last_heartbeat = device.get('last_heartbeat', '未知')
        
        # 创建树形项
        item = QTreeWidgetItem([device_name, device_type, ip_address, status, last_heartbeat])
        
        # 设置设备ID为用户数据
        item.setData(0, Qt.UserRole, device_id)
        
        # 根据状态设置颜色
        if status == 'online':
            item.setForeground(3, QBrush(QColor(0, 128, 0)))  # 绿色
        else:
            item.setForeground(3, QBrush(QColor(255, 0, 0)))  # 红色
        
        # 添加到树形视图
        self.device_tree.addTopLevelItem(item)
        
        # 存储设备信息
        self.devices[device_id] = device
    
    def update_device(self, device):
        """更新单个设备信息"""
        device_id = device.get('device_id')
        
        # 更新存储的设备信息
        self.devices[device_id] = device
        
        # 查找并更新树形项
        for i in range(self.device_tree.topLevelItemCount()):
            item = self.device_tree.topLevelItem(i)
            if item.data(0, Qt.UserRole) == device_id:
                item.setText(0, device.get('device_name', '未命名设备'))
                item.setText(1, device.get('device_type', '未知'))
                item.setText(2, device.get('ip_address', '未知'))
                item.setText(3, device.get('status', 'offline'))
                item.setText(4, device.get('last_heartbeat', '未知'))
                
                # 根据状态设置颜色
                if device.get('status') == 'online':
                    item.setForeground(3, QBrush(QColor(0, 128, 0)))  # 绿色
                else:
                    item.setForeground(3, QBrush(QColor(255, 0, 0)))  # 红色
                
                break
        
        # 更新状态标签
        self.update_status_label()
        
        # 应用过滤器
        self.apply_filter()
    
    def update_status_label(self):
        """更新状态标签"""
        total = len(self.devices)
        online = sum(1 for device in self.devices.values() if device.get('status') == 'online')
        offline = total - online
        
        self.status_label.setText(f"设备总数: {total} | 在线: {online} | 离线: {offline}")
    
    def apply_filter(self):
        """应用过滤器"""
        device_type = self.type_filter.currentData()
        status = self.status_filter.currentData()
        search_text = self.search_input.text().lower()
        
        # 遍历所有设备项
        for i in range(self.device_tree.topLevelItemCount()):
            item = self.device_tree.topLevelItem(i)
            device_id = item.data(0, Qt.UserRole)
            device = self.devices.get(device_id, {})
            
            # 检查是否符合过滤条件
            type_match = device_type == 'all' or device.get('device_type') == device_type
            status_match = status == 'all' or device.get('status') == status
            name_match = not search_text or search_text in device.get('device_name', '').lower()
            
            # 设置项的可见性
            item.setHidden(not (type_match and status_match and name_match))
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.device_tree.itemAt(position)
        if not item:
            return
        
        device_id = item.data(0, Qt.UserRole)
        device = self.devices.get(device_id, {})
        
        # 创建菜单
        menu = QMenu()
        
        # 添加查看详情选项
        view_action = QAction("查看详情", self)
        view_action.triggered.connect(lambda: self.view_device_details(device_id))
        menu.addAction(view_action)
        
        # 添加开始屏幕捕获选项
        if device.get('status') == 'online':
            capture_action = QAction("开始屏幕捕获", self)
            capture_action.triggered.connect(lambda: self.start_screen_capture(device_id))
            menu.addAction(capture_action)
        
        # 添加远程控制选项
        if device.get('status') == 'online':
            control_action = QAction("远程控制", self)
            control_action.triggered.connect(lambda: self.remote_control(device_id))
            menu.addAction(control_action)
        
        # 添加分隔符
        menu.addSeparator()
        
        # 添加重命名选项
        rename_action = QAction("重命名", self)
        rename_action.triggered.connect(lambda: self.rename_device(device_id))
        menu.addAction(rename_action)
        
        # 显示菜单
        menu.exec_(self.device_tree.viewport().mapToGlobal(position))
    
    def view_device_details(self, device_id):
        """查看设备详情"""
        device = self.devices.get(device_id, {})
        
        # 构建详情消息
        details = f"设备ID: {device_id}\n"
        details += f"设备名称: {device.get('device_name', '未知')}\n"
        details += f"设备类型: {device.get('device_type', '未知')}\n"
        details += f"IP地址: {device.get('ip_address', '未知')}\n"
        details += f"状态: {device.get('status', '未知')}\n"
        details += f"最后心跳: {device.get('last_heartbeat', '未知')}\n"
        
        if 'capabilities' in device:
            details += f"功能: {', '.join(device['capabilities'])}\n"
        
        if 'screen_resolution' in device:
            details += f"屏幕分辨率: {device['screen_resolution']}\n"
        
        if 'os_info' in device:
            details += f"操作系统: {device['os_info']}\n"
        
        # 显示详情对话框
        QMessageBox.information(self, "设备详情", details)
    
    def start_screen_capture(self, device_id):
        """开始屏幕捕获"""
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 发送请求开始屏幕捕获
            response = requests.post(
                f"{api_base_url}/video/screen/start",
                json={"device_id": device_id}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.parent.status_bar.showMessage(f"已开始屏幕捕获", 3000)
                else:
                    self.parent.show_error(f"开始屏幕捕获失败: {data.get('message')}")
            else:
                self.parent.show_error(f"开始屏幕捕获失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.parent.show_error(f"开始屏幕捕获时出错: {str(e)}")
    
    def remote_control(self, device_id):
        """远程控制设备"""
        # 此功能需要集成xdotool实现远程控制
        QMessageBox.information(self, "远程控制", "远程控制功能尚未实现")
    
    def rename_device(self, device_id):
        """重命名设备"""
        device = self.devices.get(device_id, {})
        current_name = device.get('device_name', '')
        
        # 显示重命名对话框
        new_name, ok = QInputDialog.getText(
            self, "重命名设备", "请输入新的设备名称:", 
            QLineEdit.Normal, current_name
        )
        
        if ok and new_name:
            try:
                # 获取API基础URL
                api_base_url = self.parent.api_base_url
                
                # 发送请求重命名设备
                response = requests.put(
                    f"{api_base_url}/devices/{device_id}",
                    json={"device_name": new_name}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        self.parent.status_bar.showMessage(f"设备已重命名", 3000)
                        # 更新设备信息
                        self.refresh()
                    else:
                        self.parent.show_error(f"重命名设备失败: {data.get('message')}")
                else:
                    self.parent.show_error(f"重命名设备失败: HTTP {response.status_code}")
            
            except Exception as e:
                self.parent.show_error(f"重命名设备时出错: {str(e)}")
    
    def get_device_count(self):
        """获取设备数量"""
        return len(self.devices)