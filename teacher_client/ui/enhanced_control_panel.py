#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的教师端控制面板 - 支持画笔批注、电子板书、抢答、投票等功能
"""

import sys
import os
import json
import requests
from datetime import datetime

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QPushButton, QLabel,
    QGroupBox, QSlider, QColorDialog, QComboBox, QSpinBox, QTextEdit,
    QListWidget, QListWidgetItem, QProgressBar, QTabWidget, QFrame,
    QScrollArea, QButtonGroup, QRadioButton, QCheckBox, QLineEdit,
    QMessageBox, QDialog, QDialogButtonBox, QTableWidget, QTableWidgetItem
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QColor, Q<PERSON>ainter, QPen, QPixmap, QIcon

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from shared.ui_themes import theme_manager
from shared.config import get_config

config = get_config()

class AnnotationToolbar(QWidget):
    """批注工具栏"""
    
    tool_changed = pyqtSignal(str)  # 工具变化信号
    color_changed = pyqtSignal(QColor)  # 颜色变化信号
    size_changed = pyqtSignal(int)  # 大小变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_tool = 'pen'
        self.current_color = QColor(255, 0, 0)  # 红色
        self.current_size = 3
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 工具选择
        tools_group = QGroupBox("批注工具")
        tools_layout = QHBoxLayout(tools_group)
        
        self.tool_buttons = QButtonGroup()
        
        # 画笔工具
        pen_btn = QPushButton("画笔")
        pen_btn.setCheckable(True)
        pen_btn.setChecked(True)
        pen_btn.clicked.connect(lambda: self.set_tool('pen'))
        self.tool_buttons.addButton(pen_btn, 0)
        tools_layout.addWidget(pen_btn)
        
        # 高亮工具
        highlight_btn = QPushButton("高亮")
        highlight_btn.setCheckable(True)
        highlight_btn.clicked.connect(lambda: self.set_tool('highlight'))
        self.tool_buttons.addButton(highlight_btn, 1)
        tools_layout.addWidget(highlight_btn)
        
        # 文本工具
        text_btn = QPushButton("文本")
        text_btn.setCheckable(True)
        text_btn.clicked.connect(lambda: self.set_tool('text'))
        self.tool_buttons.addButton(text_btn, 2)
        tools_layout.addWidget(text_btn)
        
        # 橡皮擦
        eraser_btn = QPushButton("橡皮擦")
        eraser_btn.setCheckable(True)
        eraser_btn.clicked.connect(lambda: self.set_tool('eraser'))
        self.tool_buttons.addButton(eraser_btn, 3)
        tools_layout.addWidget(eraser_btn)
        
        layout.addWidget(tools_group)
        
        # 颜色选择
        color_group = QGroupBox("颜色")
        color_layout = QHBoxLayout(color_group)
        
        self.color_btn = QPushButton()
        self.color_btn.setFixedSize(40, 30)
        self.color_btn.setStyleSheet(f"background-color: {self.current_color.name()};")
        self.color_btn.clicked.connect(self.choose_color)
        color_layout.addWidget(self.color_btn)
        
        # 预设颜色
        preset_colors = [
            QColor(255, 0, 0),    # 红色
            QColor(0, 255, 0),    # 绿色
            QColor(0, 0, 255),    # 蓝色
            QColor(255, 255, 0),  # 黄色
            QColor(255, 0, 255),  # 紫色
            QColor(0, 0, 0),      # 黑色
        ]
        
        for color in preset_colors:
            color_preset_btn = QPushButton()
            color_preset_btn.setFixedSize(25, 25)
            color_preset_btn.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #ccc;")
            color_preset_btn.clicked.connect(lambda checked, c=color: self.set_color(c))
            color_layout.addWidget(color_preset_btn)
        
        layout.addWidget(color_group)
        
        # 大小选择
        size_group = QGroupBox("粗细")
        size_layout = QHBoxLayout(size_group)
        
        self.size_slider = QSlider(Qt.Horizontal)
        self.size_slider.setRange(1, 20)
        self.size_slider.setValue(self.current_size)
        self.size_slider.valueChanged.connect(self.set_size)
        size_layout.addWidget(self.size_slider)
        
        self.size_label = QLabel(str(self.current_size))
        size_layout.addWidget(self.size_label)
        
        layout.addWidget(size_group)
        
        # 操作按钮
        actions_group = QGroupBox("操作")
        actions_layout = QHBoxLayout(actions_group)
        
        clear_btn = QPushButton("清除")
        clear_btn.clicked.connect(self.clear_annotations)
        actions_layout.addWidget(clear_btn)
        
        undo_btn = QPushButton("撤销")
        undo_btn.clicked.connect(self.undo_annotation)
        actions_layout.addWidget(undo_btn)
        
        save_btn = QPushButton("保存")
        save_btn.clicked.connect(self.save_annotations)
        actions_layout.addWidget(save_btn)
        
        layout.addWidget(actions_group)
        
        # 应用主题
        self.setStyleSheet(theme_manager.get_stylesheet('panel'))
    
    def set_tool(self, tool):
        """设置当前工具"""
        self.current_tool = tool
        self.tool_changed.emit(tool)
    
    def choose_color(self):
        """选择颜色"""
        color = QColorDialog.getColor(self.current_color, self)
        if color.isValid():
            self.set_color(color)
    
    def set_color(self, color):
        """设置颜色"""
        self.current_color = color
        self.color_btn.setStyleSheet(f"background-color: {color.name()};")
        self.color_changed.emit(color)
    
    def set_size(self, size):
        """设置大小"""
        self.current_size = size
        self.size_label.setText(str(size))
        self.size_changed.emit(size)
    
    def clear_annotations(self):
        """清除批注"""
        # 发送清除批注请求
        self.send_annotation_command('clear')
    
    def undo_annotation(self):
        """撤销批注"""
        self.send_annotation_command('undo')
    
    def save_annotations(self):
        """保存批注"""
        self.send_annotation_command('save')
    
    def send_annotation_command(self, command):
        """发送批注命令"""
        try:
            response = requests.post(
                f"http://{config.BACKEND_HOST}:{config.BACKEND_PORT}/api/whiteboard/annotation",
                json={
                    'command': command,
                    'tool': self.current_tool,
                    'color': self.current_color.name(),
                    'size': self.current_size,
                    'timestamp': datetime.now().isoformat()
                }
            )
            
            if response.status_code != 200:
                QMessageBox.warning(self, "警告", f"批注命令执行失败: {response.status_code}")
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"发送批注命令时出错: {str(e)}")

class QuizPanel(QWidget):
    """互动答题面板"""
    
    quiz_started = pyqtSignal(dict)
    quiz_ended = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_quiz = None
        self.quiz_timer = QTimer()
        self.quiz_timer.timeout.connect(self.update_quiz_timer)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 题目创建区域
        question_group = QGroupBox("创建题目")
        question_layout = QVBoxLayout(question_group)
        
        # 题目类型选择
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("题目类型:"))
        
        self.question_type = QComboBox()
        self.question_type.addItems(["单选题", "多选题", "判断题", "文本题"])
        self.question_type.currentTextChanged.connect(self.on_question_type_changed)
        type_layout.addWidget(self.question_type)
        
        question_layout.addLayout(type_layout)
        
        # 题目内容
        question_layout.addWidget(QLabel("题目内容:"))
        self.question_content = QTextEdit()
        self.question_content.setMaximumHeight(100)
        question_layout.addWidget(self.question_content)
        
        # 选项区域
        self.options_widget = QWidget()
        self.options_layout = QVBoxLayout(self.options_widget)
        self.option_inputs = []
        self.create_option_inputs()
        question_layout.addWidget(self.options_widget)
        
        # 答题时间设置
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("答题时间(秒):"))
        
        self.quiz_time = QSpinBox()
        self.quiz_time.setRange(10, 600)
        self.quiz_time.setValue(60)
        time_layout.addWidget(self.quiz_time)
        
        question_layout.addLayout(time_layout)
        
        # 操作按钮
        buttons_layout = QHBoxLayout()
        
        self.start_quiz_btn = QPushButton("开始答题")
        self.start_quiz_btn.clicked.connect(self.start_quiz)
        buttons_layout.addWidget(self.start_quiz_btn)
        
        self.end_quiz_btn = QPushButton("结束答题")
        self.end_quiz_btn.clicked.connect(self.end_quiz)
        self.end_quiz_btn.setEnabled(False)
        buttons_layout.addWidget(self.end_quiz_btn)
        
        question_layout.addLayout(buttons_layout)
        
        layout.addWidget(question_group)
        
        # 答题状态区域
        status_group = QGroupBox("答题状态")
        status_layout = QVBoxLayout(status_group)
        
        # 进度显示
        progress_layout = QHBoxLayout()
        progress_layout.addWidget(QLabel("答题进度:"))
        
        self.progress_bar = QProgressBar()
        progress_layout.addWidget(self.progress_bar)
        
        self.progress_label = QLabel("0/0")
        progress_layout.addWidget(self.progress_label)
        
        status_layout.addLayout(progress_layout)
        
        # 时间显示
        timer_layout = QHBoxLayout()
        timer_layout.addWidget(QLabel("剩余时间:"))
        
        self.timer_label = QLabel("00:00")
        self.timer_label.setStyleSheet("font-size: 18px; font-weight: bold; color: red;")
        timer_layout.addWidget(self.timer_label)
        
        status_layout.addLayout(timer_layout)
        
        # 结果显示按钮
        self.show_results_btn = QPushButton("显示结果")
        self.show_results_btn.clicked.connect(self.show_quiz_results)
        self.show_results_btn.setEnabled(False)
        status_layout.addWidget(self.show_results_btn)
        
        layout.addWidget(status_group)
        
        # 应用主题
        self.setStyleSheet(theme_manager.get_stylesheet('panel'))
    
    def create_option_inputs(self):
        """创建选项输入框"""
        # 清除现有选项
        for input_widget in self.option_inputs:
            input_widget.setParent(None)
        self.option_inputs.clear()
        
        question_type = self.question_type.currentText()
        
        if question_type in ["单选题", "多选题"]:
            # 创建4个选项
            for i in range(4):
                option_layout = QHBoxLayout()
                option_layout.addWidget(QLabel(f"选项{chr(65+i)}:"))
                
                option_input = QLineEdit()
                option_input.setPlaceholderText(f"请输入选项{chr(65+i)}内容")
                option_layout.addWidget(option_input)
                
                # 正确答案标记
                if question_type == "单选题":
                    correct_radio = QRadioButton("正确答案")
                    if i == 0:  # 默认第一个选项为正确答案
                        correct_radio.setChecked(True)
                    option_layout.addWidget(correct_radio)
                else:  # 多选题
                    correct_check = QCheckBox("正确答案")
                    option_layout.addWidget(correct_check)
                
                option_widget = QWidget()
                option_widget.setLayout(option_layout)
                self.options_layout.addWidget(option_widget)
                self.option_inputs.append(option_widget)
        
        elif question_type == "判断题":
            # 判断题只有对错两个选项
            for i, option_text in enumerate(["正确", "错误"]):
                option_layout = QHBoxLayout()
                option_layout.addWidget(QLabel(f"{option_text}:"))
                
                correct_radio = QRadioButton("正确答案")
                if i == 0:  # 默认"正确"为正确答案
                    correct_radio.setChecked(True)
                option_layout.addWidget(correct_radio)
                
                option_widget = QWidget()
                option_widget.setLayout(option_layout)
                self.options_layout.addWidget(option_widget)
                self.option_inputs.append(option_widget)
    
    def on_question_type_changed(self):
        """题目类型变化处理"""
        self.create_option_inputs()
    
    def start_quiz(self):
        """开始答题"""
        # 验证题目内容
        if not self.question_content.toPlainText().strip():
            QMessageBox.warning(self, "警告", "请输入题目内容")
            return
        
        # 构建题目数据
        quiz_data = {
            'type': self.question_type.currentText(),
            'content': self.question_content.toPlainText().strip(),
            'time_limit': self.quiz_time.value(),
            'options': [],
            'correct_answers': []
        }
        
        # 获取选项和正确答案
        question_type = self.question_type.currentText()
        
        if question_type in ["单选题", "多选题"]:
            for i, option_widget in enumerate(self.option_inputs):
                layout = option_widget.layout()
                option_input = layout.itemAt(1).widget()  # QLineEdit
                option_text = option_input.text().strip()
                
                if option_text:
                    quiz_data['options'].append({
                        'id': chr(65+i),
                        'text': option_text
                    })
                    
                    # 检查是否为正确答案
                    if question_type == "单选题":
                        correct_radio = layout.itemAt(2).widget()  # QRadioButton
                        if correct_radio.isChecked():
                            quiz_data['correct_answers'] = [chr(65+i)]
                    else:  # 多选题
                        correct_check = layout.itemAt(2).widget()  # QCheckBox
                        if correct_check.isChecked():
                            quiz_data['correct_answers'].append(chr(65+i))
        
        elif question_type == "判断题":
            quiz_data['options'] = [
                {'id': 'T', 'text': '正确'},
                {'id': 'F', 'text': '错误'}
            ]
            
            # 获取正确答案
            for i, option_widget in enumerate(self.option_inputs):
                layout = option_widget.layout()
                correct_radio = layout.itemAt(1).widget()  # QRadioButton
                if correct_radio.isChecked():
                    quiz_data['correct_answers'] = ['T' if i == 0 else 'F']
                    break
        
        # 验证选项
        if not quiz_data['options']:
            QMessageBox.warning(self, "警告", "请至少添加一个选项")
            return
        
        if not quiz_data['correct_answers']:
            QMessageBox.warning(self, "警告", "请设置正确答案")
            return
        
        # 发送开始答题请求
        try:
            response = requests.post(
                f"http://{config.BACKEND_HOST}:{config.BACKEND_PORT}/api/quiz/start",
                json=quiz_data
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.current_quiz = result.get('quiz')
                    self.start_quiz_timer()
                    
                    self.start_quiz_btn.setEnabled(False)
                    self.end_quiz_btn.setEnabled(True)
                    
                    self.quiz_started.emit(quiz_data)
                    QMessageBox.information(self, "成功", "答题已开始")
                else:
                    QMessageBox.warning(self, "警告", f"开始答题失败: {result.get('message')}")
            else:
                QMessageBox.critical(self, "错误", f"请求失败: HTTP {response.status_code}")
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"开始答题时出错: {str(e)}")
    
    def start_quiz_timer(self):
        """开始答题计时器"""
        self.remaining_time = self.quiz_time.value()
        self.quiz_timer.start(1000)  # 每秒更新
        self.update_quiz_timer()
    
    def update_quiz_timer(self):
        """更新答题计时器"""
        if self.remaining_time > 0:
            minutes = self.remaining_time // 60
            seconds = self.remaining_time % 60
            self.timer_label.setText(f"{minutes:02d}:{seconds:02d}")
            self.remaining_time -= 1
        else:
            self.quiz_timer.stop()
            self.timer_label.setText("00:00")
            self.end_quiz()
    
    def end_quiz(self):
        """结束答题"""
        if self.current_quiz:
            try:
                response = requests.post(
                    f"http://{config.BACKEND_HOST}:{config.BACKEND_PORT}/api/quiz/{self.current_quiz['id']}/end"
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        self.quiz_timer.stop()
                        self.current_quiz = None
                        
                        self.start_quiz_btn.setEnabled(True)
                        self.end_quiz_btn.setEnabled(False)
                        self.show_results_btn.setEnabled(True)
                        
                        self.quiz_ended.emit()
                        QMessageBox.information(self, "成功", "答题已结束")
                    else:
                        QMessageBox.warning(self, "警告", f"结束答题失败: {result.get('message')}")
                else:
                    QMessageBox.critical(self, "错误", f"请求失败: HTTP {response.status_code}")
            
            except Exception as e:
                QMessageBox.critical(self, "错误", f"结束答题时出错: {str(e)}")
    
    def show_quiz_results(self):
        """显示答题结果"""
        dialog = QuizResultsDialog(self)
        dialog.exec_()

class QuizResultsDialog(QDialog):
    """答题结果对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("答题结果")
        self.setMinimumSize(800, 600)
        
        self.init_ui()
        self.load_results()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 结果表格
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(4)
        self.results_table.setHorizontalHeaderLabels(["学生", "答案", "是否正确", "提交时间"])
        layout.addWidget(self.results_table)
        
        # 统计信息
        stats_layout = QHBoxLayout()
        
        self.total_label = QLabel("总人数: 0")
        stats_layout.addWidget(self.total_label)
        
        self.correct_label = QLabel("正确: 0")
        stats_layout.addWidget(self.correct_label)
        
        self.accuracy_label = QLabel("正确率: 0%")
        stats_layout.addWidget(self.accuracy_label)
        
        layout.addLayout(stats_layout)
        
        # 按钮
        buttons = QDialogButtonBox(QDialogButtonBox.Ok)
        buttons.accepted.connect(self.accept)
        layout.addWidget(buttons)
    
    def load_results(self):
        """加载答题结果"""
        try:
            response = requests.get(
                f"http://{config.BACKEND_HOST}:{config.BACKEND_PORT}/api/quiz/latest/results"
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    results = result.get('results', [])
                    self.display_results(results)
                else:
                    QMessageBox.warning(self, "警告", f"获取结果失败: {result.get('message')}")
            else:
                QMessageBox.critical(self, "错误", f"请求失败: HTTP {response.status_code}")
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载结果时出错: {str(e)}")
    
    def display_results(self, results):
        """显示结果"""
        self.results_table.setRowCount(len(results))
        
        total_count = len(results)
        correct_count = 0
        
        for i, result in enumerate(results):
            # 学生姓名
            self.results_table.setItem(i, 0, QTableWidgetItem(result.get('student_name', '未知')))
            
            # 答案
            answer = result.get('answer', '')
            if isinstance(answer, list):
                answer = ', '.join(answer)
            self.results_table.setItem(i, 1, QTableWidgetItem(str(answer)))
            
            # 是否正确
            is_correct = result.get('is_correct', False)
            if is_correct:
                correct_count += 1
            
            correct_item = QTableWidgetItem("正确" if is_correct else "错误")
            correct_item.setBackground(QColor(144, 238, 144) if is_correct else QColor(255, 182, 193))
            self.results_table.setItem(i, 2, correct_item)
            
            # 提交时间
            submit_time = result.get('submit_time', '')
            self.results_table.setItem(i, 3, QTableWidgetItem(submit_time))
        
        # 更新统计信息
        self.total_label.setText(f"总人数: {total_count}")
        self.correct_label.setText(f"正确: {correct_count}")
        
        accuracy = (correct_count / total_count * 100) if total_count > 0 else 0
        self.accuracy_label.setText(f"正确率: {accuracy:.1f}%")
        
        # 调整列宽
        self.results_table.resizeColumnsToContents()

class EnhancedControlPanel(QWidget):
    """增强的控制面板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        
        # 批注工具选项卡
        self.annotation_toolbar = AnnotationToolbar(self)
        tab_widget.addTab(self.annotation_toolbar, "批注工具")
        
        # 互动答题选项卡
        self.quiz_panel = QuizPanel(self)
        tab_widget.addTab(self.quiz_panel, "互动答题")
        
        # 投票面板选项卡
        vote_panel = QWidget()
        tab_widget.addTab(vote_panel, "投票调查")
        
        # 抢答面板选项卡
        rush_answer_panel = QWidget()
        tab_widget.addTab(rush_answer_panel, "抢答活动")
        
        layout.addWidget(tab_widget)
        
        # 应用主题
        self.setStyleSheet(theme_manager.get_stylesheet('tab_widget'))