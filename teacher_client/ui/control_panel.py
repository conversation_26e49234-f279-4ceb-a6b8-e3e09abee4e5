#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教师端控制面板
"""

import os
import sys
import requests
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QGroupBox,
    QFormLayout, QComboBox, QLineEdit, QTextEdit, QCheckBox, QTabWidget,
    QMessageBox, QFileDialog, QProgressBar
)
from PyQt5.QtCore import Qt, QSize, QTimer, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QIcon, QPixmap

class ControlPanel(QWidget):
    """控制面板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 创建主布局
        layout = QVBoxLayout(self)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 创建课堂控制标签页
        classroom_tab = self.create_classroom_tab()
        tab_widget.addTab(classroom_tab, "课堂控制")
        
        # 创建文件分发标签页
        file_tab = self.create_file_tab()
        tab_widget.addTab(file_tab, "文件分发")
        
        # 创建题目发布标签页
        question_tab = self.create_question_tab()
        tab_widget.addTab(question_tab, "题目发布")
        
        # 创建系统设置标签页
        settings_tab = self.create_settings_tab()
        tab_widget.addTab(settings_tab, "系统设置")
        
        # 添加标签页到主布局
        layout.addWidget(tab_widget)
    
    def create_classroom_tab(self):
        """创建课堂控制标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 创建课堂状态组
        status_group = QGroupBox("课堂状态")
        status_layout = QFormLayout()
        
        # 课堂状态下拉框
        self.classroom_status = QComboBox()
        self.classroom_status.addItem("未开始", "not_started")
        self.classroom_status.addItem("进行中", "in_progress")
        self.classroom_status.addItem("已结束", "ended")
        status_layout.addRow("当前状态:", self.classroom_status)
        
        # 开始/结束课堂按钮
        status_btn_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始课堂")
        self.start_btn.clicked.connect(self.start_classroom)
        status_btn_layout.addWidget(self.start_btn)
        
        self.end_btn = QPushButton("结束课堂")
        self.end_btn.clicked.connect(self.end_classroom)
        status_btn_layout.addWidget(self.end_btn)
        
        status_layout.addRow("操作:", status_btn_layout)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        # 创建签到控制组
        attendance_group = QGroupBox("签到控制")
        attendance_layout = QFormLayout()
        
        # 签到方式下拉框
        self.attendance_type = QComboBox()
        self.attendance_type.addItem("二维码签到", "qrcode")
        self.attendance_type.addItem("位置签到", "location")
        self.attendance_type.addItem("人脸识别", "face")
        attendance_layout.addRow("签到方式:", self.attendance_type)
        
        # 签到时长输入框
        self.attendance_duration = QLineEdit()
        self.attendance_duration.setText("5")  # 默认5分钟
        attendance_layout.addRow("签到时长(分钟):", self.attendance_duration)
        
        # 开始签到按钮
        self.start_attendance_btn = QPushButton("开始签到")
        self.start_attendance_btn.clicked.connect(self.start_attendance)
        attendance_layout.addRow("操作:", self.start_attendance_btn)
        
        attendance_group.setLayout(attendance_layout)
        layout.addWidget(attendance_group)
        
        # 创建屏幕控制组
        screen_group = QGroupBox("屏幕控制")
        screen_layout = QVBoxLayout()
        
        # 锁定学生屏幕按钮
        self.lock_screen_btn = QPushButton("锁定学生屏幕")
        self.lock_screen_btn.clicked.connect(self.lock_student_screens)
        screen_layout.addWidget(self.lock_screen_btn)
        
        # 解锁学生屏幕按钮
        self.unlock_screen_btn = QPushButton("解锁学生屏幕")
        self.unlock_screen_btn.clicked.connect(self.unlock_student_screens)
        screen_layout.addWidget(self.unlock_screen_btn)
        
        screen_group.setLayout(screen_layout)
        layout.addWidget(screen_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        return tab
    
    def create_file_tab(self):
        """创建文件分发标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 创建文件选择组
        file_group = QGroupBox("文件选择")
        file_layout = QHBoxLayout()
        
        # 文件路径输入框
        self.file_path = QLineEdit()
        self.file_path.setReadOnly(True)
        file_layout.addWidget(self.file_path)
        
        # 浏览按钮
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self.browse_file)
        file_layout.addWidget(browse_btn)
        
        file_group.setLayout(file_layout)
        layout.addWidget(file_group)
        
        # 创建分发目标组
        target_group = QGroupBox("分发目标")
        target_layout = QVBoxLayout()
        
        # 全部学生选项
        self.all_students_cb = QCheckBox("全部学生")
        self.all_students_cb.setChecked(True)
        target_layout.addWidget(self.all_students_cb)
        
        # 指定小组选项
        self.specific_groups_cb = QCheckBox("指定小组")
        target_layout.addWidget(self.specific_groups_cb)
        
        # 小组选择下拉框
        self.group_combo = QComboBox()
        self.group_combo.setEnabled(False)
        target_layout.addWidget(self.group_combo)
        
        # 连接复选框信号
        self.all_students_cb.toggled.connect(self.toggle_target_selection)
        self.specific_groups_cb.toggled.connect(self.toggle_target_selection)
        
        target_group.setLayout(target_layout)
        layout.addWidget(target_group)
        
        # 创建分发控制组
        distribute_group = QGroupBox("分发控制")
        distribute_layout = QVBoxLayout()
        
        # 分发按钮
        self.distribute_btn = QPushButton("开始分发")
        self.distribute_btn.clicked.connect(self.distribute_file)
        distribute_layout.addWidget(self.distribute_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        distribute_layout.addWidget(self.progress_bar)
        
        distribute_group.setLayout(distribute_layout)
        layout.addWidget(distribute_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        return tab
    
    def create_question_tab(self):
        """创建题目发布标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 创建题目内容组
        question_group = QGroupBox("题目内容")
        question_layout = QVBoxLayout()
        
        # 题目类型下拉框
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("题目类型:"))
        
        self.question_type = QComboBox()
        self.question_type.addItem("单选题", "single_choice")
        self.question_type.addItem("多选题", "multiple_choice")
        self.question_type.addItem("判断题", "true_false")
        self.question_type.addItem("简答题", "short_answer")
        type_layout.addWidget(self.question_type)
        
        question_layout.addLayout(type_layout)
        
        # 题目内容输入框
        question_layout.addWidget(QLabel("题目内容:"))
        self.question_content = QTextEdit()
        question_layout.addWidget(self.question_content)
        
        # 选项输入框
        self.options_group = QGroupBox("选项")
        self.options_layout = QVBoxLayout()
        
        # 添加4个默认选项
        self.option_inputs = []
        for i in range(4):
            option_layout = QHBoxLayout()
            option_layout.addWidget(QLabel(f"选项{chr(65+i)}:"))
            
            option_input = QLineEdit()
            option_layout.addWidget(option_input)
            self.option_inputs.append(option_input)
            
            self.options_layout.addLayout(option_layout)
        
        self.options_group.setLayout(self.options_layout)
        question_layout.addWidget(self.options_group)
        
        # 正确答案输入框
        answer_layout = QHBoxLayout()
        answer_layout.addWidget(QLabel("正确答案:"))
        
        self.correct_answer = QLineEdit()
        answer_layout.addWidget(self.correct_answer)
        
        question_layout.addLayout(answer_layout)
        
        question_group.setLayout(question_layout)
        layout.addWidget(question_group)
        
        # 创建发布控制组
        publish_group = QGroupBox("发布控制")
        publish_layout = QVBoxLayout()
        
        # 答题时间输入框
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("答题时间(分钟):"))
        
        self.answer_time = QLineEdit()
        self.answer_time.setText("5")  # 默认5分钟
        time_layout.addWidget(self.answer_time)
        
        publish_layout.addLayout(time_layout)
        
        # 发布按钮
        self.publish_btn = QPushButton("发布题目")
        self.publish_btn.clicked.connect(self.publish_question)
        publish_layout.addWidget(self.publish_btn)
        
        publish_group.setLayout(publish_layout)
        layout.addWidget(publish_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        # 连接题目类型变更信号
        self.question_type.currentIndexChanged.connect(self.update_question_form)
        
        return tab
    
    def create_settings_tab(self):
        """创建系统设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 创建服务器设置组
        server_group = QGroupBox("服务器设置")
        server_layout = QFormLayout()
        
        # 后端服务器地址
        self.backend_host = QLineEdit()
        self.backend_host.setText(self.parent.config.BACKEND_HOST)
        server_layout.addRow("后端服务器地址:", self.backend_host)
        
        # 后端服务器端口
        self.backend_port = QLineEdit()
        self.backend_port.setText(str(self.parent.config.BACKEND_PORT))
        server_layout.addRow("后端服务器端口:", self.backend_port)
        
        server_group.setLayout(server_layout)
        layout.addWidget(server_group)
        
        # 创建按钮组
        button_layout = QHBoxLayout()
        
        # 保存按钮
        save_btn = QPushButton("保存设置")
        save_btn.clicked.connect(self.save_settings)
        button_layout.addWidget(save_btn)
        
        # 重置按钮
        reset_btn = QPushButton("重置")
        reset_btn.clicked.connect(self.reset_settings)
        button_layout.addWidget(reset_btn)
        
        layout.addLayout(button_layout)
        
        # 添加弹性空间
        layout.addStretch()
        
        return tab
    
    # 以下是各种功能的实现方法
    def start_classroom(self):
        """开始课堂"""
        self.parent.status_bar.showMessage("课堂已开始", 3000)
    
    def end_classroom(self):
        """结束课堂"""
        self.parent.status_bar.showMessage("课堂已结束", 3000)
    
    def start_attendance(self):
        """开始签到"""
        duration = self.attendance_duration.text()
        self.parent.status_bar.showMessage(f"签到已开始，持续{duration}分钟", 3000)
    
    def lock_student_screens(self):
        """锁定学生屏幕"""
        self.parent.status_bar.showMessage("已锁定学生屏幕", 3000)
    
    def unlock_student_screens(self):
        """解锁学生屏幕"""
        self.parent.status_bar.showMessage("已解锁学生屏幕", 3000)
    
    def browse_file(self):
        """浏览文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择文件", "", "所有文件 (*.*)"
        )
        
        if file_path:
            self.file_path.setText(file_path)
    
    def toggle_target_selection(self):
        """切换分发目标选择"""
        if self.sender() == self.all_students_cb:
            if self.all_students_cb.isChecked():
                self.specific_groups_cb.setChecked(False)
                self.group_combo.setEnabled(False)
        elif self.sender() == self.specific_groups_cb:
            if self.specific_groups_cb.isChecked():
                self.all_students_cb.setChecked(False)
                self.group_combo.setEnabled(True)
    
    def distribute_file(self):
        """分发文件"""
        file_path = self.file_path.text()
        if not file_path:
            self.parent.show_error("请先选择文件")
            return
        
        self.progress_bar.setValue(100)
        self.parent.status_bar.showMessage("文件分发成功", 3000)
    
    def update_question_form(self):
        """根据题目类型更新表单"""
        question_type = self.question_type.currentData()
        
        # 更新选项组可见性
        if question_type in ["single_choice", "multiple_choice"]:
            self.options_group.setVisible(True)
        else:
            self.options_group.setVisible(False)
    
    def publish_question(self):
        """发布题目"""
        self.parent.status_bar.showMessage("题目发布成功", 3000)
    
    def save_settings(self):
        """保存设置"""
        self.parent.status_bar.showMessage("设置已保存，重启应用后生效", 3000)
    
    def reset_settings(self):
        """重置设置"""
        self.parent.status_bar.showMessage("设置已重置", 3000)
    
    def get_current_classroom_id(self):
        """获取当前选中的课堂ID"""
        # 从主窗口的课堂选择下拉框获取当前课堂ID
        combo = self.parent.classroom_combo
        return combo.currentData() if combo.currentIndex() >= 0 else None