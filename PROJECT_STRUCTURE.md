# 智慧课堂系统项目结构

## 项目概览

智慧课堂系统是一个基于统信UOS的多端协作教学平台，采用分布式架构设计。

## 目录结构

```
smart_classroom/
├── teacher_client/              # 教师端PyQt5应用
│   ├── __init__.py
│   ├── main.py                 # 教师端入口程序
│   └── ui/                     # 用户界面模块
│       ├── __init__.py
│       └── main_window.py      # 主窗口
├── group_client/               # 小组端PyQt5应用
│   ├── __init__.py
│   ├── main.py                 # 小组端入口程序
│   └── ui/                     # 用户界面模块
│       ├── __init__.py
│       └── main_window.py      # 主窗口
├── backend/                    # Flask后端服务
│   ├── __init__.py
│   ├── app.py                  # Flask应用入口
│   ├── models/                 # 数据模型
│   │   └── __init__.py
│   ├── api/                    # API路由
│   │   └── __init__.py
│   └── services/               # 业务服务
│       └── __init__.py
├── web_client/                 # 学生端Web界面
│   ├── index.html              # 主页面
│   ├── css/
│   │   └── style.css           # 样式文件
│   └── js/
│       └── app.js              # JavaScript应用
├── shared/                     # 共享组件和工具
│   ├── __init__.py
│   ├── config.py               # 配置管理
│   └── utils.py                # 工具函数
├── tests/                      # 测试文件
│   ├── __init__.py
│   ├── test_config.py          # 配置测试
│   ├── test_utils.py           # 工具测试
│   └── test_structure.py       # 结构测试
├── docs/                       # 项目文档
│   ├── README.md               # 文档索引
│   └── installation.md         # 安装指南
├── scripts/                    # 部署和工具脚本
│   ├── install_dependencies.sh # 依赖安装脚本
│   ├── start_services.sh       # 服务启动脚本
│   └── stop_services.sh        # 服务停止脚本
├── config/                     # 配置文件
│   ├── development.yml         # 开发环境配置
│   └── production.yml          # 生产环境配置
├── README.md                   # 项目说明
├── requirements.txt            # Python依赖
├── setup.py                    # 项目安装配置
├── pyproject.toml             # 项目配置
├── .gitignore                 # Git忽略文件
└── .flake8                    # 代码规范配置
```

## 技术栈

### 前端应用
- **PyQt5**: 教师端和小组端GUI框架
- **HTML5/CSS3/JavaScript**: 学生端Web界面
- **WebSocket**: 实时通信

### 后端服务
- **Flask**: Web框架
- **Flask-SocketIO**: WebSocket支持
- **SQLAlchemy**: ORM数据库操作
- **SQLite**: 本地数据库

### 视频处理
- **MediaMTX**: 流媒体服务器
- **FFmpeg**: 视频编码处理
- **python-vlc**: 视频播放

### 开发工具
- **pytest**: 单元测试框架
- **black**: 代码格式化
- **flake8**: 代码规范检查

## 核心功能模块

1. **设备管理**: UDP广播发现、连接管理、状态监控
2. **视频流处理**: 屏幕捕获、流媒体传输、多路显示
3. **协同白板**: 基于Excalidraw的多人协作
4. **互动答题**: 题目发布、答案收集、统计分析
5. **文件分发**: 多格式支持、进度监控、版本管理
6. **分组管理**: 拖拽分组、随机分组、状态同步

## 开发规范

### 代码规范
- 使用Black进行代码格式化
- 使用Flake8进行代码检查
- 遵循PEP 8编码规范
- 中文注释和文档

### 测试规范
- 单元测试覆盖率 > 80%
- 集成测试验证核心功能
- 性能测试确保延迟要求

### 版本控制
- 使用Git进行版本控制
- 分支策略：main(生产) / develop(开发) / feature(功能)
- 提交信息使用中文描述

## 部署要求

### 系统要求
- 操作系统：统信UOS 20+
- Python：3.8+
- 内存：8GB+
- 存储：50GB+
- 网络：千兆局域网

### 性能指标
- 屏幕共享延迟 < 1.0秒
- 广播延迟 < 1.5秒
- 支持75台终端并发
- 支持8个小组同时研讨

## 快速开始

1. 安装依赖：`./scripts/install_dependencies.sh`
2. 启动服务：`./scripts/start_services.sh`
3. 运行测试：`python -m pytest tests/`
4. 启动客户端：`python teacher_client/main.py`

## 项目状态

✅ 第一阶段：项目基础架构搭建完成
- [x] 目录结构创建
- [x] 核心模块框架
- [x] 配置管理系统
- [x] 开发工具配置
- [x] 测试框架搭建
- [x] 部署脚本编写
- [x] 文档结构建立

✅ 第二阶段：核心数据模型实现完成
- [x] 数据模型层 (7个核心模型)
- [x] 业务服务层 (3个主要服务)
- [x] 数据仓库层 (4个仓库类)
- [x] API接口层 (REST API基础)
- [x] 测试验证 (50+测试用例)
- [x] 完整的分层架构设计
- [x] 数据库设计和关系映射

下一步：实现设备发现和连接管理