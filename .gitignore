# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyQt
*.ui~

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Uploads
uploads/
data/
tests
backend/tests

# OS
.DS_Store
Thumbs.db

# MediaMTX
mediamtx.yml

# Config
config/local.yml
config/secret.yml

# Test
.coverage
htmlcov/
.pytest_cache/
.tox/

# Temporary files
*.tmp
*.temp
.cache/
exports