<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧课堂系统 - API测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
        }
        h2 {
            margin-top: 0;
            color: #444;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        input, select {
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
            box-sizing: border-box;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .result {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>智慧课堂系统 - API测试页面</h1>
    
    <div class="section">
        <h2>健康检查</h2>
        <button onclick="healthCheck()">检查服务器状态</button>
        <div id="health-result" class="result"></div>
    </div>
    
    <div class="section">
        <h2>用户认证</h2>
        <div class="form-group">
            <label for="user-id">用户ID:</label>
            <input type="text" id="user-id" value="teacher1">
        </div>
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="password123">
        </div>
        <button onclick="login()">登录</button>
        <button onclick="logout()">登出</button>
        <button onclick="verifyToken()">验证令牌</button>
        <div id="auth-result" class="result"></div>
    </div>
    
    <div class="section">
        <h2>课堂管理</h2>
        <div class="form-group">
            <label for="classroom-name">课堂名称:</label>
            <input type="text" id="classroom-name" value="测试课堂">
        </div>
        <div class="form-group">
            <label for="teacher-name">教师姓名:</label>
            <input type="text" id="teacher-name" value="张老师">
        </div>
        <button onclick="createClassroom()">创建课堂</button>
        <div id="classroom-result" class="result"></div>
        
        <div class="form-group" style="margin-top: 15px;">
            <label for="classroom-id">课堂ID:</label>
            <input type="text" id="classroom-id" placeholder="创建课堂后自动填充">
        </div>
        <button onclick="startClassroom()">开始课堂</button>
        <button onclick="getClassroomStatus()">获取课堂状态</button>
        <div id="classroom-status-result" class="result"></div>
    </div>
    
    <div class="section">
        <h2>小组管理</h2>
        <div class="form-group">
            <label for="group-classroom-id">课堂ID:</label>
            <input type="text" id="group-classroom-id" placeholder="使用上面创建的课堂ID">
        </div>
        <div class="form-group">
            <label for="group-count">小组数量:</label>
            <input type="number" id="group-count" value="3" min="1" max="10">
        </div>
        <button onclick="createGroups()">创建小组</button>
        <button onclick="getGroups()">获取小组列表</button>
        <div id="group-result" class="result"></div>
    </div>
    
    <div class="section">
        <h2>互动答题</h2>
        <div class="form-group">
            <label for="question-classroom-id">课堂ID:</label>
            <input type="text" id="question-classroom-id" placeholder="使用上面创建的课堂ID">
        </div>
        <div class="form-group">
            <label for="question-content">题目内容:</label>
            <input type="text" id="question-content" value="1+1=?">
        </div>
        <div class="form-group">
            <label for="question-type">题目类型:</label>
            <select id="question-type">
                <option value="single_choice">单选题</option>
                <option value="multiple_choice">多选题</option>
                <option value="true_false">判断题</option>
                <option value="short_answer">简答题</option>
            </select>
        </div>
        <div class="form-group">
            <label for="question-options">选项 (用逗号分隔):</label>
            <input type="text" id="question-options" value="1,2,3,4">
        </div>
        <div class="form-group">
            <label for="correct-answer">正确答案:</label>
            <input type="text" id="correct-answer" value="2">
        </div>
        <button onclick="createQuestion()">创建题目</button>
        <div id="question-result" class="result"></div>
        
        <div class="form-group" style="margin-top: 15px;">
            <label for="question-id">题目ID:</label>
            <input type="text" id="question-id" placeholder="创建题目后自动填充">
        </div>
        <button onclick="publishQuestion()">发布题目</button>
        <button onclick="getQuestionResults()">获取答题结果</button>
        <div id="question-status-result" class="result"></div>
    </div>
    
    <script>
        // 存储令牌
        let authToken = '';
        
        // 基础URL
        const BASE_URL = '';
        
        // 显示结果
        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = isSuccess ? 'result success' : 'result error';
        }
        
        // 健康检查
        async function healthCheck() {
            try {
                const response = await fetch(`${BASE_URL}/api/health`);
                const data = await response.json();
                showResult('health-result', data);
            } catch (error) {
                showResult('health-result', { error: error.message }, false);
            }
        }
        
        // 登录
        async function login() {
            try {
                const userId = document.getElementById('user-id').value;
                const password = document.getElementById('password').value;
                
                const response = await fetch(`${BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ user_id: userId, password: password })
                });
                
                const data = await response.json();
                
                if (data.success && data.token) {
                    authToken = data.token;
                }
                
                showResult('auth-result', data);
            } catch (error) {
                showResult('auth-result', { error: error.message }, false);
            }
        }
        
        // 登出
        async function logout() {
            if (!authToken) {
                showResult('auth-result', { error: '未登录，请先登录' }, false);
                return;
            }
            
            try {
                const response = await fetch(`${BASE_URL}/api/auth/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    authToken = '';
                }
                
                showResult('auth-result', data);
            } catch (error) {
                showResult('auth-result', { error: error.message }, false);
            }
        }
        
        // 验证令牌
        async function verifyToken() {
            if (!authToken) {
                showResult('auth-result', { error: '未登录，请先登录' }, false);
                return;
            }
            
            try {
                const response = await fetch(`${BASE_URL}/api/auth/verify`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                showResult('auth-result', data);
            } catch (error) {
                showResult('auth-result', { error: error.message }, false);
            }
        }
        
        // 创建课堂
        async function createClassroom() {
            try {
                const name = document.getElementById('classroom-name').value;
                const teacherName = document.getElementById('teacher-name').value;
                const userId = document.getElementById('user-id').value;
                
                const response = await fetch(`${BASE_URL}/api/classroom/create`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        teacher_id: userId,
                        teacher_name: teacherName,
                        name: name,
                        description: '通过API测试页面创建的课堂'
                    })
                });
                
                const data = await response.json();
                
                if (data.success && data.data && data.data.id) {
                    document.getElementById('classroom-id').value = data.data.id;
                    document.getElementById('group-classroom-id').value = data.data.id;
                    document.getElementById('question-classroom-id').value = data.data.id;
                }
                
                showResult('classroom-result', data);
            } catch (error) {
                showResult('classroom-result', { error: error.message }, false);
            }
        }
        
        // 开始课堂
        async function startClassroom() {
            try {
                const classroomId = document.getElementById('classroom-id').value;
                const userId = document.getElementById('user-id').value;
                
                if (!classroomId) {
                    showResult('classroom-status-result', { error: '请先创建课堂或输入课堂ID' }, false);
                    return;
                }
                
                const response = await fetch(`${BASE_URL}/api/classroom/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        classroom_id: classroomId,
                        teacher_id: userId
                    })
                });
                
                const data = await response.json();
                showResult('classroom-status-result', data);
            } catch (error) {
                showResult('classroom-status-result', { error: error.message }, false);
            }
        }
        
        // 获取课堂状态
        async function getClassroomStatus() {
            try {
                const classroomId = document.getElementById('classroom-id').value;
                
                if (!classroomId) {
                    showResult('classroom-status-result', { error: '请先创建课堂或输入课堂ID' }, false);
                    return;
                }
                
                const response = await fetch(`${BASE_URL}/api/classroom/status/${classroomId}`);
                const data = await response.json();
                showResult('classroom-status-result', data);
            } catch (error) {
                showResult('classroom-status-result', { error: error.message }, false);
            }
        }
        
        // 创建小组
        async function createGroups() {
            try {
                const classroomId = document.getElementById('group-classroom-id').value;
                const groupCount = document.getElementById('group-count').value;
                
                if (!classroomId) {
                    showResult('group-result', { error: '请先创建课堂或输入课堂ID' }, false);
                    return;
                }
                
                const response = await fetch(`${BASE_URL}/api/classroom/groups/create`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        classroom_id: classroomId,
                        group_count: parseInt(groupCount),
                        group_prefix: '测试小组'
                    })
                });
                
                const data = await response.json();
                showResult('group-result', data);
            } catch (error) {
                showResult('group-result', { error: error.message }, false);
            }
        }
        
        // 获取小组列表
        async function getGroups() {
            try {
                const classroomId = document.getElementById('group-classroom-id').value;
                
                if (!classroomId) {
                    showResult('group-result', { error: '请先创建课堂或输入课堂ID' }, false);
                    return;
                }
                
                const response = await fetch(`${BASE_URL}/api/groups/?classroom_id=${classroomId}`);
                const data = await response.json();
                showResult('group-result', data);
            } catch (error) {
                showResult('group-result', { error: error.message }, false);
            }
        }
        
        // 创建题目
        async function createQuestion() {
            try {
                const classroomId = document.getElementById('question-classroom-id').value;
                const content = document.getElementById('question-content').value;
                const questionType = document.getElementById('question-type').value;
                const options = document.getElementById('question-options').value.split(',');
                const correctAnswer = document.getElementById('correct-answer').value;
                
                if (!classroomId) {
                    showResult('question-result', { error: '请先创建课堂或输入课堂ID' }, false);
                    return;
                }
                
                const response = await fetch(`${BASE_URL}/api/questions/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        classroom_id: classroomId,
                        question_type: questionType,
                        content: content,
                        options: options,
                        correct_answer: correctAnswer
                    })
                });
                
                const data = await response.json();
                
                if (data.success && data.question && data.question.id) {
                    document.getElementById('question-id').value = data.question.id;
                }
                
                showResult('question-result', data);
            } catch (error) {
                showResult('question-result', { error: error.message }, false);
            }
        }
        
        // 发布题目
        async function publishQuestion() {
            try {
                const questionId = document.getElementById('question-id').value;
                
                if (!questionId) {
                    showResult('question-status-result', { error: '请先创建题目或输入题目ID' }, false);
                    return;
                }
                
                const response = await fetch(`${BASE_URL}/api/questions/${questionId}/publish`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        target_groups: []
                    })
                });
                
                const data = await response.json();
                showResult('question-status-result', data);
            } catch (error) {
                showResult('question-status-result', { error: error.message }, false);
            }
        }
        
        // 获取答题结果
        async function getQuestionResults() {
            try {
                const questionId = document.getElementById('question-id').value;
                
                if (!questionId) {
                    showResult('question-status-result', { error: '请先创建题目或输入题目ID' }, false);
                    return;
                }
                
                const response = await fetch(`${BASE_URL}/api/questions/${questionId}/results`);
                const data = await response.json();
                showResult('question-status-result', data);
            } catch (error) {
                showResult('question-status-result', { error: error.message }, false);
            }
        }
        
        // 页面加载时执行健康检查
        window.onload = function() {
            healthCheck();
        };
    </script>
</body>
</html>