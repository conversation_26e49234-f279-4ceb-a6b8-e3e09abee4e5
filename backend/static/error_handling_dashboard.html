<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误处理和异常恢复监控面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .metric-label {
            font-weight: 500;
        }

        .metric-value {
            font-weight: bold;
            color: #2980b9;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online { background-color: #27ae60; }
        .status-offline { background-color: #e74c3c; }
        .status-warning { background-color: #f39c12; }
        .status-error { background-color: #e74c3c; }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-danger {
            background: #e74c3c;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .alert {
            padding: 12px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }

        .alert-info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }

        .alert-warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }

        .alert-error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .alert-critical {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
            font-weight: bold;
        }

        .log-container {
            max-height: 300px;
            overflow-y: auto;
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin: 5px 0;
            padding: 2px 0;
        }

        .log-timestamp {
            color: #95a5a6;
        }

        .log-level-info { color: #3498db; }
        .log-level-warning { color: #f39c12; }
        .log-level-error { color: #e74c3c; }
        .log-level-critical { color: #e74c3c; font-weight: bold; }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            transition: width 0.3s ease;
        }

        .tabs {
            display: flex;
            background: white;
            border-radius: 10px 10px 0 0;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            background: #ecf0f1;
            border: none;
            transition: background 0.2s;
        }

        .tab.active {
            background: white;
            color: #3498db;
            font-weight: bold;
        }

        .tab-content {
            background: white;
            padding: 20px;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .hidden {
            display: none;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }

        .error-item {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #e74c3c;
        }

        .error-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .error-title {
            font-weight: bold;
            color: #2c3e50;
        }

        .error-time {
            color: #7f8c8d;
            font-size: 12px;
        }

        .error-message {
            color: #555;
            margin: 5px 0;
        }

        .error-actions {
            margin-top: 10px;
        }

        .health-score {
            font-size: 48px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }

        .health-excellent { color: #27ae60; }
        .health-good { color: #2ecc71; }
        .health-fair { color: #f39c12; }
        .health-poor { color: #e67e22; }
        .health-critical { color: #e74c3c; }
    </style>
</head>
<body>
    <div class="header">
        <h1>智慧课堂系统 - 错误处理和异常恢复监控面板</h1>
        <p>实时监控系统状态、错误处理和恢复情况</p>
    </div>

    <div class="container">
        <!-- 系统健康状态 -->
        <div class="card">
            <div class="card-title">系统健康状态</div>
            <div id="health-score" class="health-score">--</div>
            <div id="health-status" style="text-align: center; font-size: 18px; margin-bottom: 20px;">检查中...</div>
            <div id="system-metrics">
                <div class="metric">
                    <span class="metric-label">CPU使用率</span>
                    <span id="cpu-usage" class="metric-value">--%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">内存使用率</span>
                    <span id="memory-usage" class="metric-value">--%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">活动告警</span>
                    <span id="active-alerts" class="metric-value">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">监控状态</span>
                    <span id="monitoring-status" class="metric-value">--</span>
                </div>
            </div>
        </div>

        <!-- 主要功能区域 -->
        <div class="tabs">
            <button class="tab active" onclick="showTab('errors')">错误管理</button>
            <button class="tab" onclick="showTab('network')">网络监控</button>
            <button class="tab" onclick="showTab('backup')">备份管理</button>
            <button class="tab" onclick="showTab('alerts')">系统告警</button>
            <button class="tab" onclick="showTab('guidance')">用户指导</button>
        </div>

        <div class="tab-content">
            <!-- 错误管理 -->
            <div id="errors-tab" class="tab-panel">
                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-title">错误统计</div>
                        <div id="error-stats">
                            <div class="metric">
                                <span class="metric-label">总错误数</span>
                                <span id="total-errors" class="metric-value">--</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">活动错误</span>
                                <span id="active-errors-count" class="metric-value">--</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">网络错误</span>
                                <span id="network-errors" class="metric-value">--</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">系统错误</span>
                                <span id="system-errors" class="metric-value">--</span>
                            </div>
                        </div>
                        <button class="btn" onclick="refreshErrorStats()">刷新统计</button>
                    </div>

                    <div class="card">
                        <div class="card-title">活动错误</div>
                        <div id="active-errors-list">
                            <div class="loading">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 网络监控 -->
            <div id="network-tab" class="tab-panel hidden">
                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-title">网络统计</div>
                        <div id="network-stats">
                            <div class="metric">
                                <span class="metric-label">总端点数</span>
                                <span id="total-endpoints" class="metric-value">--</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">在线端点</span>
                                <span id="online-endpoints" class="metric-value">--</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">成功率</span>
                                <span id="success-rate" class="metric-value">--%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">重连成功率</span>
                                <span id="reconnection-rate" class="metric-value">--%</span>
                            </div>
                        </div>
                        <button class="btn" onclick="refreshNetworkStats()">刷新统计</button>
                    </div>

                    <div class="card">
                        <div class="card-title">网络端点</div>
                        <div id="network-endpoints">
                            <div class="loading">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 备份管理 -->
            <div id="backup-tab" class="tab-panel hidden">
                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-title">备份统计</div>
                        <div id="backup-stats">
                            <div class="metric">
                                <span class="metric-label">总备份数</span>
                                <span id="total-backups" class="metric-value">--</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">成功备份</span>
                                <span id="successful-backups" class="metric-value">--</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">备份成功率</span>
                                <span id="backup-success-rate" class="metric-value">--%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">总备份大小</span>
                                <span id="total-backup-size" class="metric-value">-- MB</span>
                            </div>
                        </div>
                        <button class="btn btn-success" onclick="createTestBackup()">创建测试备份</button>
                    </div>

                    <div class="card">
                        <div class="card-title">备份任务</div>
                        <div id="backup-tasks">
                            <div class="loading">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统告警 -->
            <div id="alerts-tab" class="tab-panel hidden">
                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-title">告警统计</div>
                        <div id="alert-stats">
                            <div class="metric">
                                <span class="metric-label">严重告警</span>
                                <span id="critical-alerts-count" class="metric-value">--</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">错误告警</span>
                                <span id="error-alerts-count" class="metric-value">--</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">警告告警</span>
                                <span id="warning-alerts-count" class="metric-value">--</span>
                            </div>
                        </div>
                        <button class="btn" onclick="refreshAlerts()">刷新告警</button>
                    </div>

                    <div class="card">
                        <div class="card-title">活动告警</div>
                        <div id="active-alerts-list">
                            <div class="loading">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户指导 -->
            <div id="guidance-tab" class="tab-panel hidden">
                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-title">错误指导测试</div>
                        <div>
                            <label>错误类型:</label>
                            <select id="error-type-select">
                                <option value="network_connection_failed">网络连接失败</option>
                                <option value="device_offline">设备离线</option>
                                <option value="video_stream_failed">视频流失败</option>
                                <option value="system_overload">系统过载</option>
                            </select>
                        </div>
                        <div style="margin: 10px 0;">
                            <label>用户角色:</label>
                            <select id="user-role-select">
                                <option value="teacher">教师</option>
                                <option value="admin">管理员</option>
                                <option value="student">学生</option>
                            </select>
                        </div>
                        <button class="btn" onclick="getErrorGuidance()">获取错误指导</button>
                    </div>

                    <div class="card">
                        <div class="card-title">指导内容</div>
                        <div id="guidance-content">
                            <div class="loading">请选择错误类型获取指导</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时日志 -->
        <div class="card" style="margin-top: 20px;">
            <div class="card-title">实时系统日志</div>
            <div id="system-logs" class="log-container">
                <div class="log-entry">
                    <span class="log-timestamp">[2024-01-20 10:00:00]</span>
                    <span class="log-level-info">[INFO]</span>
                    系统监控面板已启动
                </div>
            </div>
            <button class="btn" onclick="clearLogs()" style="margin-top: 10px;">清空日志</button>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        // WebSocket连接
        const socket = io();
        
        // 全局变量
        let currentTab = 'errors';
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeWebSocket();
            loadInitialData();
            startPeriodicUpdates();
        });

        // WebSocket事件处理
        function initializeWebSocket() {
            socket.on('connect', function() {
                addLog('WebSocket连接已建立', 'info');
                socket.emit('subscribe_error_updates');
                socket.emit('subscribe_system_status');
            });

            socket.on('disconnect', function() {
                addLog('WebSocket连接已断开', 'warning');
            });

            socket.on('system_status_update', function(data) {
                updateSystemStatus(data);
            });

            socket.on('error_alert', function(alert) {
                addLog(`新告警: ${alert.message}`, alert.level);
                if (currentTab === 'alerts') {
                    refreshAlerts();
                }
            });
        }

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页
            document.querySelectorAll('.tab-panel').forEach(panel => {
                panel.classList.add('hidden');
            });
            
            // 移除所有活动状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName + '-tab').classList.remove('hidden');
            event.target.classList.add('active');
            
            currentTab = tabName;
            
            // 加载对应数据
            switch(tabName) {
                case 'errors':
                    loadErrorData();
                    break;
                case 'network':
                    loadNetworkData();
                    break;
                case 'backup':
                    loadBackupData();
                    break;
                case 'alerts':
                    loadAlertsData();
                    break;
                case 'guidance':
                    // 指导页面不需要自动加载数据
                    break;
            }
        }

        // 加载初始数据
        function loadInitialData() {
            loadSystemStatus();
            loadErrorData();
        }

        // 加载系统状态
        function loadSystemStatus() {
            fetch('/api/error-handling/system/status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateSystemStatus(data.data);
                    }
                })
                .catch(error => {
                    addLog('加载系统状态失败: ' + error.message, 'error');
                });
        }

        // 更新系统状态显示
        function updateSystemStatus(status) {
            const healthScore = document.getElementById('health-score');
            const healthStatus = document.getElementById('health-status');
            
            healthScore.textContent = status.health_score;
            healthStatus.textContent = getHealthStatusText(status.health_status);
            
            // 设置健康分数颜色
            healthScore.className = 'health-score health-' + status.health_status;
            
            // 更新指标
            document.getElementById('cpu-usage').textContent = status.metrics.cpu_percent.toFixed(1) + '%';
            document.getElementById('memory-usage').textContent = status.metrics.memory_percent.toFixed(1) + '%';
            document.getElementById('active-alerts').textContent = status.active_alerts_count;
            document.getElementById('monitoring-status').textContent = status.monitoring_enabled ? '运行中' : '已停止';
        }

        function getHealthStatusText(status) {
            const statusMap = {
                'excellent': '优秀',
                'good': '良好',
                'fair': '一般',
                'poor': '较差',
                'critical': '严重'
            };
            return statusMap[status] || status;
        }

        // 加载错误数据
        function loadErrorData() {
            // 加载错误统计
            fetch('/api/error-handling/errors/statistics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateErrorStats(data.data);
                    }
                })
                .catch(error => {
                    addLog('加载错误统计失败: ' + error.message, 'error');
                });

            // 加载活动错误
            fetch('/api/error-handling/errors/active')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateActiveErrors(data.data);
                    }
                })
                .catch(error => {
                    addLog('加载活动错误失败: ' + error.message, 'error');
                });
        }

        function updateErrorStats(stats) {
            document.getElementById('total-errors').textContent = stats.total_errors;
            document.getElementById('active-errors-count').textContent = stats.active_errors;
            document.getElementById('network-errors').textContent = stats.error_types.network_error || 0;
            document.getElementById('system-errors').textContent = stats.error_types.system_error || 0;
        }

        function updateActiveErrors(errors) {
            const container = document.getElementById('active-errors-list');
            
            if (errors.length === 0) {
                container.innerHTML = '<div class="alert alert-info">暂无活动错误</div>';
                return;
            }

            container.innerHTML = errors.map(error => `
                <div class="error-item">
                    <div class="error-header">
                        <div class="error-title">${error.type}</div>
                        <div class="error-time">${new Date(error.timestamp).toLocaleString()}</div>
                    </div>
                    <div class="error-message">${error.message}</div>
                    <div class="error-actions">
                        <button class="btn btn-success" onclick="resolveError('${error.error_id}')">解决</button>
                    </div>
                </div>
            `).join('');
        }

        // 解决错误
        function resolveError(errorId) {
            fetch(`/api/error-handling/errors/${errorId}/resolve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    resolution_note: '手动解决'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addLog('错误已解决: ' + errorId, 'info');
                    loadErrorData(); // 刷新错误列表
                } else {
                    addLog('解决错误失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                addLog('解决错误失败: ' + error.message, 'error');
            });
        }

        // 加载网络数据
        function loadNetworkData() {
            // 加载网络统计
            fetch('/api/error-handling/network/statistics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateNetworkStats(data.data);
                    }
                })
                .catch(error => {
                    addLog('加载网络统计失败: ' + error.message, 'error');
                });

            // 加载网络端点
            fetch('/api/error-handling/network/endpoints')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateNetworkEndpoints(data.data);
                    }
                })
                .catch(error => {
                    addLog('加载网络端点失败: ' + error.message, 'error');
                });
        }

        function updateNetworkStats(stats) {
            document.getElementById('total-endpoints').textContent = stats.total_endpoints;
            document.getElementById('online-endpoints').textContent = stats.connected_endpoints;
            document.getElementById('success-rate').textContent = stats.success_rate.toFixed(1) + '%';
            document.getElementById('reconnection-rate').textContent = stats.reconnection_success_rate.toFixed(1) + '%';
        }

        function updateNetworkEndpoints(endpoints) {
            const container = document.getElementById('network-endpoints');
            
            if (endpoints.length === 0) {
                container.innerHTML = '<div class="alert alert-info">暂无网络端点</div>';
                return;
            }

            container.innerHTML = endpoints.map(endpoint => `
                <div class="metric">
                    <span class="metric-label">
                        <span class="status-indicator status-${endpoint.status === 'connected' ? 'online' : 'offline'}"></span>
                        ${endpoint.description}
                    </span>
                    <span class="metric-value">${endpoint.host}:${endpoint.port}</span>
                </div>
            `).join('');
        }

        // 加载备份数据
        function loadBackupData() {
            // 加载备份统计
            fetch('/api/error-handling/backup/statistics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateBackupStats(data.data);
                    }
                })
                .catch(error => {
                    addLog('加载备份统计失败: ' + error.message, 'error');
                });

            // 加载备份任务
            fetch('/api/error-handling/backup/tasks')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateBackupTasks(data.data);
                    }
                })
                .catch(error => {
                    addLog('加载备份任务失败: ' + error.message, 'error');
                });
        }

        function updateBackupStats(stats) {
            document.getElementById('total-backups').textContent = stats.total_backups;
            document.getElementById('successful-backups').textContent = stats.completed_backups;
            document.getElementById('backup-success-rate').textContent = stats.success_rate.toFixed(1) + '%';
            document.getElementById('total-backup-size').textContent = stats.total_backup_size_mb + ' MB';
        }

        function updateBackupTasks(tasks) {
            const container = document.getElementById('backup-tasks');
            
            if (tasks.length === 0) {
                container.innerHTML = '<div class="alert alert-info">暂无备份任务</div>';
                return;
            }

            container.innerHTML = tasks.slice(0, 10).map(task => `
                <div class="metric">
                    <span class="metric-label">${task.description}</span>
                    <span class="metric-value">${task.status}</span>
                </div>
            `).join('');
        }

        // 创建测试备份
        function createTestBackup() {
            const taskData = {
                task_id: 'test_backup_' + Date.now(),
                backup_type: 'full',
                source_paths: ['instance/*.db'],
                description: '测试备份任务'
            };

            fetch('/api/error-handling/backup/tasks', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(taskData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addLog('测试备份任务已创建: ' + data.data.task_id, 'info');
                    setTimeout(() => loadBackupData(), 2000); // 2秒后刷新
                } else {
                    addLog('创建备份任务失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                addLog('创建备份任务失败: ' + error.message, 'error');
            });
        }

        // 加载告警数据
        function loadAlertsData() {
            fetch('/api/error-handling/system/alerts?active_only=true')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateAlerts(data.data);
                    }
                })
                .catch(error => {
                    addLog('加载告警数据失败: ' + error.message, 'error');
                });
        }

        function updateAlerts(alerts) {
            const container = document.getElementById('active-alerts-list');
            
            // 统计不同级别的告警
            const criticalCount = alerts.filter(a => a.alert_level === 'critical').length;
            const errorCount = alerts.filter(a => a.alert_level === 'error').length;
            const warningCount = alerts.filter(a => a.alert_level === 'warning').length;
            
            document.getElementById('critical-alerts-count').textContent = criticalCount;
            document.getElementById('error-alerts-count').textContent = errorCount;
            document.getElementById('warning-alerts-count').textContent = warningCount;
            
            if (alerts.length === 0) {
                container.innerHTML = '<div class="alert alert-info">暂无活动告警</div>';
                return;
            }

            container.innerHTML = alerts.map(alert => `
                <div class="alert alert-${alert.alert_level}">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <strong>${alert.alert_level.toUpperCase()}</strong>: ${alert.message}
                            <br><small>${new Date(alert.timestamp).toLocaleString()}</small>
                        </div>
                        <button class="btn" onclick="acknowledgeAlert('${alert.alert_id}')">确认</button>
                    </div>
                </div>
            `).join('');
        }

        // 确认告警
        function acknowledgeAlert(alertId) {
            fetch(`/api/error-handling/system/alerts/${alertId}/acknowledge`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: 'admin'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addLog('告警已确认: ' + alertId, 'info');
                    loadAlertsData(); // 刷新告警列表
                } else {
                    addLog('确认告警失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                addLog('确认告警失败: ' + error.message, 'error');
            });
        }

        // 获取错误指导
        function getErrorGuidance() {
            const errorType = document.getElementById('error-type-select').value;
            const userRole = document.getElementById('user-role-select').value;
            
            fetch(`/api/error-handling/guidance/error/${errorType}?user_role=${userRole}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayGuidance(data.data);
                    } else {
                        addLog('获取错误指导失败: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    addLog('获取错误指导失败: ' + error.message, 'error');
                });
        }

        function displayGuidance(guidanceData) {
            const container = document.getElementById('guidance-content');
            const errorInfo = guidanceData.error_info;
            const guidance = guidanceData.guidance;
            
            let html = `
                <div class="alert alert-info">
                    <h4>${errorInfo.title}</h4>
                    <p>${errorInfo.user_message}</p>
                </div>
            `;
            
            if (guidance && guidance.length > 0) {
                html += '<h4>解决方案:</h4>';
                guidance.forEach(guide => {
                    html += `
                        <div style="margin: 15px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                            <h5>${guide.title}</h5>
                            <p>${guide.description}</p>
                            <h6>操作步骤:</h6>
                            <ol>
                                ${guide.steps.map(step => `<li>${step}</li>`).join('')}
                            </ol>
                            ${guide.tips.length > 0 ? `
                                <h6>提示:</h6>
                                <ul>
                                    ${guide.tips.map(tip => `<li>${tip}</li>`).join('')}
                                </ul>
                            ` : ''}
                        </div>
                    `;
                });
            }
            
            container.innerHTML = html;
        }

        // 刷新函数
        function refreshErrorStats() {
            loadErrorData();
            addLog('错误统计已刷新', 'info');
        }

        function refreshNetworkStats() {
            loadNetworkData();
            addLog('网络统计已刷新', 'info');
        }

        function refreshAlerts() {
            loadAlertsData();
            addLog('告警数据已刷新', 'info');
        }

        // 日志管理
        function addLog(message, level = 'info') {
            const logsContainer = document.getElementById('system-logs');
            const timestamp = new Date().toLocaleString();
            
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-level-${level}">[${level.toUpperCase()}]</span>
                ${message}
            `;
            
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
            
            // 限制日志条数
            const logEntries = logsContainer.querySelectorAll('.log-entry');
            if (logEntries.length > 100) {
                logEntries[0].remove();
            }
        }

        function clearLogs() {
            document.getElementById('system-logs').innerHTML = '';
            addLog('日志已清空', 'info');
        }

        // 定期更新
        function startPeriodicUpdates() {
            // 每30秒更新一次系统状态
            setInterval(() => {
                if (currentTab === 'errors') {
                    loadErrorData();
                } else if (currentTab === 'network') {
                    loadNetworkData();
                } else if (currentTab === 'backup') {
                    loadBackupData();
                } else if (currentTab === 'alerts') {
                    loadAlertsData();
                }
            }, 30000);
        }
    </script>
</body>
</html>