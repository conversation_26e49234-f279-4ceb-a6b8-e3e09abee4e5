<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧课堂 - 课堂报告仪表板</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card h3 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .stat-card p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .chart-container h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3em;
            text-align: center;
        }

        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-group label {
            font-weight: 500;
            color: #333;
        }

        .control-group select,
        .control-group input {
            padding: 8px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .control-group select:focus,
        .control-group input:focus {
            outline: none;
            border-color: #4facfe;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: transform 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .data-table th,
        .data-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 500;
        }

        .data-table tr:hover {
            background-color: #f8f9fa;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background-color: #fee;
            color: #c33;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #c33;
        }

        .success {
            background-color: #efe;
            color: #3c3;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #3c3;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>课堂报告仪表板</h1>
            <p>智慧课堂数据分析与可视化</p>
        </div>

        <div class="content">
            <!-- 控制面板 -->
            <div class="controls">
                <div class="control-group">
                    <label for="classSelect">选择班级:</label>
                    <select id="classSelect">
                        <option value="">所有班级</option>
                    </select>
                </div>
                <div class="control-group">
                    <label for="dateRange">日期范围:</label>
                    <input type="date" id="startDate">
                    <span>至</span>
                    <input type="date" id="endDate">
                </div>
                <div class="control-group">
                    <button class="btn" onclick="loadReportData()">生成报告</button>
                    <button class="btn" onclick="exportReport()">导出报告</button>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 id="totalStudents">0</h3>
                    <p>总学生数</p>
                </div>
                <div class="stat-card">
                    <h3 id="avgAttendance">0%</h3>
                    <p>平均出勤率</p>
                </div>
                <div class="stat-card">
                    <h3 id="totalClasses">0</h3>
                    <p>总课程数</p>
                </div>
                <div class="stat-card">
                    <h3 id="avgParticipation">0%</h3>
                    <p>平均参与度</p>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="charts-grid">
                <div class="chart-container">
                    <h3>出勤率趋势</h3>
                    <canvas id="attendanceChart"></canvas>
                </div>
                <div class="chart-container">
                    <h3>参与度分析</h3>
                    <canvas id="participationChart"></canvas>
                </div>
                <div class="chart-container">
                    <h3>班级对比</h3>
                    <canvas id="classComparisonChart"></canvas>
                </div>
                <div class="chart-container">
                    <h3>活动类型分布</h3>
                    <canvas id="activityChart"></canvas>
                </div>
            </div>

            <!-- 详细数据表格 -->
            <div class="chart-container">
                <h3>详细数据</h3>
                <div id="loadingIndicator" class="loading" style="display: none;">
                    正在加载数据...
                </div>
                <div id="errorMessage" class="error" style="display: none;"></div>
                <table id="dataTable" class="data-table" style="display: none;">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>班级</th>
                            <th>学生数</th>
                            <th>出勤率</th>
                            <th>参与度</th>
                            <th>活动数</th>
                        </tr>
                    </thead>
                    <tbody id="dataTableBody">
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let attendanceChart, participationChart, classComparisonChart, activityChart;
        let reportData = [];

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            loadClassList();
            setDefaultDates();
            loadReportData();
        });

        // 初始化页面
        function initializePage() {
            // 初始化图表
            initializeCharts();
        }

        // 设置默认日期
        function setDefaultDates() {
            const today = new Date();
            const oneWeekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
            document.getElementById('startDate').value = oneWeekAgo.toISOString().split('T')[0];
        }

        // 加载班级列表
        async function loadClassList() {
            try {
                const response = await fetch('/api/classes');
                const classes = await response.json();
                
                const classSelect = document.getElementById('classSelect');
                classSelect.innerHTML = '<option value="">所有班级</option>';
                
                classes.forEach(cls => {
                    const option = document.createElement('option');
                    option.value = cls.id;
                    option.textContent = cls.name;
                    classSelect.appendChild(option);
                });
            } catch (error) {
                console.error('加载班级列表失败:', error);
            }
        }

        // 加载报告数据
        async function loadReportData() {
            showLoading(true);
            hideError();

            try {
                const classId = document.getElementById('classSelect').value;
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;

                const params = new URLSearchParams();
                if (classId) params.append('class_id', classId);
                if (startDate) params.append('start_date', startDate);
                if (endDate) params.append('end_date', endDate);

                const response = await fetch(`/api/reports/dashboard?${params}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                reportData = data;

                updateStatistics(data.statistics);
                updateCharts(data);
                updateDataTable(data.details);

            } catch (error) {
                console.error('加载报告数据失败:', error);
                showError('加载数据失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 更新统计数据
        function updateStatistics(stats) {
            document.getElementById('totalStudents').textContent = stats.total_students || 0;
            document.getElementById('avgAttendance').textContent = (stats.avg_attendance || 0).toFixed(1) + '%';
            document.getElementById('totalClasses').textContent = stats.total_classes || 0;
            document.getElementById('avgParticipation').textContent = (stats.avg_participation || 0).toFixed(1) + '%';
        }

        // 初始化图表
        function initializeCharts() {
            // 出勤率趋势图
            const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
            attendanceChart = new Chart(attendanceCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '出勤率',
                        data: [],
                        borderColor: '#4facfe',
                        backgroundColor: 'rgba(79, 172, 254, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });

            // 参与度分析图
            const participationCtx = document.getElementById('participationChart').getContext('2d');
            participationChart = new Chart(participationCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: '参与度',
                        data: [],
                        backgroundColor: '#f093fb'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });

            // 班级对比图
            const classCtx = document.getElementById('classComparisonChart').getContext('2d');
            classComparisonChart = new Chart(classCtx, {
                type: 'radar',
                data: {
                    labels: ['出勤率', '参与度', '活跃度', '完成率', '互动率'],
                    datasets: []
                },
                options: {
                    responsive: true,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });

            // 活动类型分布图
            const activityCtx = document.getElementById('activityChart').getContext('2d');
            activityChart = new Chart(activityCtx, {
                type: 'doughnut',
                data: {
                    labels: [],
                    datasets: [{
                        data: [],
                        backgroundColor: [
                            '#4facfe',
                            '#f093fb',
                            '#43e97b',
                            '#38f9d7',
                            '#ffecd2'
                        ]
                    }]
                },
                options: {
                    responsive: true
                }
            });
        }

        // 更新图表
        function updateCharts(data) {
            // 更新出勤率趋势图
            if (data.attendance_trend) {
                attendanceChart.data.labels = data.attendance_trend.dates;
                attendanceChart.data.datasets[0].data = data.attendance_trend.rates;
                attendanceChart.update();
            }

            // 更新参与度分析图
            if (data.participation_analysis) {
                participationChart.data.labels = data.participation_analysis.classes;
                participationChart.data.datasets[0].data = data.participation_analysis.rates;
                participationChart.update();
            }

            // 更新班级对比图
            if (data.class_comparison) {
                classComparisonChart.data.datasets = data.class_comparison.map((cls, index) => ({
                    label: cls.name,
                    data: cls.metrics,
                    borderColor: ['#4facfe', '#f093fb', '#43e97b', '#38f9d7', '#ffecd2'][index % 5],
                    backgroundColor: ['#4facfe', '#f093fb', '#43e97b', '#38f9d7', '#ffecd2'][index % 5] + '20'
                }));
                classComparisonChart.update();
            }

            // 更新活动类型分布图
            if (data.activity_distribution) {
                activityChart.data.labels = data.activity_distribution.types;
                activityChart.data.datasets[0].data = data.activity_distribution.counts;
                activityChart.update();
            }
        }

        // 更新数据表格
        function updateDataTable(details) {
            const tbody = document.getElementById('dataTableBody');
            tbody.innerHTML = '';

            if (details && details.length > 0) {
                details.forEach(row => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>${row.date}</td>
                        <td>${row.class_name}</td>
                        <td>${row.student_count}</td>
                        <td>${row.attendance_rate.toFixed(1)}%</td>
                        <td>${row.participation_rate.toFixed(1)}%</td>
                        <td>${row.activity_count}</td>
                    `;
                    tbody.appendChild(tr);
                });
                document.getElementById('dataTable').style.display = 'table';
            } else {
                document.getElementById('dataTable').style.display = 'none';
            }
        }

        // 导出报告
        async function exportReport() {
            try {
                const classId = document.getElementById('classSelect').value;
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;

                const params = new URLSearchParams();
                if (classId) params.append('class_id', classId);
                if (startDate) params.append('start_date', startDate);
                if (endDate) params.append('end_date', endDate);

                const response = await fetch(`/api/reports/export?${params}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `classroom_report_${new Date().toISOString().split('T')[0]}.csv`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                showSuccess('报告导出成功！');
            } catch (error) {
                console.error('导出报告失败:', error);
                showError('导出失败: ' + error.message);
            }
        }

        // 显示加载状态
        function showLoading(show) {
            document.getElementById('loadingIndicator').style.display = show ? 'block' : 'none';
        }

        // 显示错误信息
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // 隐藏错误信息
        function hideError() {
            document.getElementById('errorMessage').style.display = 'none';
        }

        // 显示成功信息
        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = message;
            document.querySelector('.content').insertBefore(successDiv, document.querySelector('.controls'));
            
            setTimeout(() => {
                successDiv.remove();
            }, 3000);
        }
    </script>
</body>
</html>