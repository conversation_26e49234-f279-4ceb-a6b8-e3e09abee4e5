<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧课堂系统 - 简化版API测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
        }
        h2 {
            margin-top: 0;
            color: #444;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        .result {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>智慧课堂系统 - 简化版API测试</h1>
    
    <div class="section">
        <h2>健康检查</h2>
        <button onclick="healthCheck()">检查服务器状态</button>
        <div id="health-result" class="result"></div>
    </div>
    
    <div class="section">
        <h2>测试API</h2>
        <button onclick="testApi()">测试API接口</button>
        <div id="api-result" class="result"></div>
    </div>
    
    <div class="section">
        <h2>模拟登录</h2>
        <button onclick="mockLogin()">模拟登录</button>
        <div id="login-result" class="result"></div>
    </div>
    
    <div class="section">
        <h2>WebSocket测试</h2>
        <button onclick="connectWebSocket()">连接WebSocket</button>
        <button onclick="disconnectWebSocket()" disabled id="disconnect-btn">断开WebSocket</button>
        <div id="websocket-result" class="result"></div>
    </div>
    
    <script src="https://cdn.socket.io/4.4.1/socket.io.min.js"></script>
    <script>
        // 基础URL
        const BASE_URL = '';
        let socket = null;
        
        // 显示结果
        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = isSuccess ? 'result success' : 'result error';
        }
        
        // 健康检查
        async function healthCheck() {
            try {
                const response = await fetch(`${BASE_URL}/api/health`);
                const data = await response.json();
                showResult('health-result', data);
            } catch (error) {
                showResult('health-result', { error: error.message }, false);
            }
        }
        
        // 测试API
        async function testApi() {
            try {
                const response = await fetch(`${BASE_URL}/api/test`);
                const data = await response.json();
                showResult('api-result', data);
            } catch (error) {
                showResult('api-result', { error: error.message }, false);
            }
        }
        
        // 模拟登录
        async function mockLogin() {
            try {
                const response = await fetch(`${BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: 'teacher1',
                        password: 'password123'
                    })
                });
                const data = await response.json();
                showResult('login-result', data);
            } catch (error) {
                showResult('login-result', { error: error.message }, false);
            }
        }
        
        // 连接WebSocket
        function connectWebSocket() {
            try {
                socket = io();
                
                socket.on('connect', () => {
                    showResult('websocket-result', {
                        status: 'connected',
                        id: socket.id,
                        timestamp: new Date().toISOString()
                    });
                    document.getElementById('disconnect-btn').disabled = false;
                });
                
                socket.on('disconnect', () => {
                    showResult('websocket-result', {
                        status: 'disconnected',
                        timestamp: new Date().toISOString()
                    });
                    document.getElementById('disconnect-btn').disabled = true;
                });
                
                socket.on('error', (error) => {
                    showResult('websocket-result', {
                        status: 'error',
                        error: error,
                        timestamp: new Date().toISOString()
                    }, false);
                });
            } catch (error) {
                showResult('websocket-result', { error: error.message }, false);
            }
        }
        
        // 断开WebSocket
        function disconnectWebSocket() {
            if (socket) {
                socket.disconnect();
                socket = null;
            }
        }
        
        // 页面加载时执行健康检查
        window.onload = function() {
            healthCheck();
        };
    </script>
</body>
</html>