<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧课堂系统 - 视频流测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .video-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        .video-item {
            flex: 1;
            min-width: 300px;
            margin-bottom: 20px;
        }
        .video-wrapper {
            position: relative;
            width: 100%;
            background-color: #000;
            border-radius: 5px;
            overflow: hidden;
        }
        video {
            width: 100%;
            display: block;
        }
        .video-controls {
            margin-top: 10px;
        }
        .stream-info {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        button:hover {
            background-color: #45a049;
        }
        button.stop {
            background-color: #f44336;
        }
        button.stop:hover {
            background-color: #d32f2f;
        }
        .control-panel {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .log-container {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border: 1px solid #ddd;
            height: 200px;
            overflow-y: auto;
        }
        .log-entry {
            margin-bottom: 5px;
            font-family: monospace;
            font-size: 14px;
        }
        .log-time {
            color: #666;
            margin-right: 10px;
        }
        .log-info {
            color: #2196F3;
        }
        .log-error {
            color: #f44336;
        }
        .log-success {
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>智慧课堂系统 - 视频流测试</h1>
        
        <div class="control-panel">
            <h2>控制面板</h2>
            <div class="form-group">
                <label for="device-id">设备ID:</label>
                <input type="text" id="device-id" value="test_device">
            </div>
            <button id="start-screen">开始屏幕捕获</button>
            <button id="start-camera">开始摄像头捕获</button>
            <button id="list-streams">获取流列表</button>
        </div>
        
        <div class="video-container" id="video-container">
            <!-- 视频将在这里动态添加 -->
        </div>
        
        <div class="log-container" id="log-container">
            <div class="log-entry">
                <span class="log-time">[00:00:00]</span>
                <span class="log-info">系统初始化完成，等待操作...</span>
            </div>
        </div>
    </div>

    <script src="https://cdn.socket.io/4.4.1/socket.io.min.js"></script>
    <script>
        // 全局变量
        const streams = new Map();
        const socket = io();
        
        // DOM元素
        const deviceIdInput = document.getElementById('device-id');
        const startScreenButton = document.getElementById('start-screen');
        const startCameraButton = document.getElementById('start-camera');
        const listStreamsButton = document.getElementById('list-streams');
        const videoContainer = document.getElementById('video-container');
        const logContainer = document.getElementById('log-container');
        
        // 日志函数
        function log(message, type = 'info') {
            const now = new Date();
            const timeStr = now.toTimeString().split(' ')[0];
            
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            
            const timeSpan = document.createElement('span');
            timeSpan.className = 'log-time';
            timeSpan.textContent = `[${timeStr}]`;
            
            const messageSpan = document.createElement('span');
            messageSpan.className = `log-${type}`;
            messageSpan.textContent = message;
            
            logEntry.appendChild(timeSpan);
            logEntry.appendChild(messageSpan);
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // 添加视频元素
        function addVideoElement(streamId, sourceId, streamType, url) {
            // 检查是否已存在
            if (streams.has(streamId)) {
                log(`流 ${streamId} 已存在`, 'error');
                return;
            }
            
            // 创建视频项容器
            const videoItem = document.createElement('div');
            videoItem.className = 'video-item';
            videoItem.id = `video-item-${streamId}`;
            
            // 创建视频包装器
            const videoWrapper = document.createElement('div');
            videoWrapper.className = 'video-wrapper';
            
            // 创建视频元素
            const video = document.createElement('video');
            video.id = `video-${streamId}`;
            video.controls = true;
            video.autoplay = true;
            video.muted = true;
            video.src = url;
            
            // 创建流信息
            const streamInfo = document.createElement('div');
            streamInfo.className = 'stream-info';
            streamInfo.innerHTML = `
                <div>流ID: ${streamId}</div>
                <div>源ID: ${sourceId}</div>
                <div>类型: ${streamType}</div>
                <div>URL: <a href="${url}" target="_blank">${url}</a></div>
            `;
            
            // 创建控制按钮
            const videoControls = document.createElement('div');
            videoControls.className = 'video-controls';
            
            const stopButton = document.createElement('button');
            stopButton.className = 'stop';
            stopButton.textContent = '停止流';
            stopButton.onclick = () => stopStream(streamId);
            
            const broadcastButton = document.createElement('button');
            broadcastButton.textContent = '广播流';
            broadcastButton.onclick = () => broadcastStream(streamId);
            
            // 组装DOM
            videoControls.appendChild(stopButton);
            videoControls.appendChild(broadcastButton);
            
            videoWrapper.appendChild(video);
            
            videoItem.appendChild(videoWrapper);
            videoItem.appendChild(streamInfo);
            videoItem.appendChild(videoControls);
            
            videoContainer.appendChild(videoItem);
            
            // 保存流信息
            streams.set(streamId, {
                sourceId,
                streamType,
                url,
                element: videoItem
            });
            
            log(`添加流: ${streamId} (${streamType})`, 'success');
            
            // 处理视频错误
            video.onerror = () => {
                log(`视频加载失败: ${streamId}`, 'error');
            };
        }
        
        // 移除视频元素
        function removeVideoElement(streamId) {
            if (!streams.has(streamId)) {
                log(`找不到流: ${streamId}`, 'error');
                return;
            }
            
            const stream = streams.get(streamId);
            videoContainer.removeChild(stream.element);
            streams.delete(streamId);
            
            log(`移除流: ${streamId}`, 'info');
        }
        
        // 开始屏幕捕获
        async function startScreenCapture() {
            const deviceId = deviceIdInput.value.trim();
            if (!deviceId) {
                log('请输入设备ID', 'error');
                return;
            }
            
            log(`正在启动屏幕捕获 (设备ID: ${deviceId})...`, 'info');
            
            try {
                const response = await fetch('/api/video/screen/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ device_id: deviceId })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`屏幕捕获已启动: ${data.stream.stream_id}`, 'success');
                    addVideoElement(
                        data.stream.stream_id,
                        data.stream.source_id,
                        data.stream.stream_type,
                        data.stream.http_url
                    );
                } else {
                    log(`启动屏幕捕获失败: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`请求错误: ${error.message}`, 'error');
            }
        }
        
        // 开始摄像头捕获
        async function startCameraCapture() {
            const deviceId = deviceIdInput.value.trim();
            if (!deviceId) {
                log('请输入设备ID', 'error');
                return;
            }
            
            log(`正在启动摄像头捕获 (设备ID: ${deviceId})...`, 'info');
            
            try {
                const response = await fetch('/api/video/camera/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ device_id: deviceId })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`摄像头捕获已启动: ${data.stream.stream_id}`, 'success');
                    addVideoElement(
                        data.stream.stream_id,
                        data.stream.source_id,
                        data.stream.stream_type,
                        data.stream.http_url
                    );
                } else {
                    log(`启动摄像头捕获失败: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`请求错误: ${error.message}`, 'error');
            }
        }
        
        // 停止流
        async function stopStream(streamId) {
            log(`正在停止流: ${streamId}...`, 'info');
            
            try {
                const response = await fetch(`/api/video/streams/${streamId}/stop`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`流已停止: ${streamId}`, 'success');
                    removeVideoElement(streamId);
                } else {
                    log(`停止流失败: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`请求错误: ${error.message}`, 'error');
            }
        }
        
        // 广播流
        async function broadcastStream(streamId) {
            log(`正在广播流: ${streamId}...`, 'info');
            
            try {
                const response = await fetch('/api/video/broadcast', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        source_stream_id: streamId,
                        target_groups: ['group1', 'group2']
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`流已广播到 ${data.targets.length} 个小组`, 'success');
                } else {
                    log(`广播流失败: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`请求错误: ${error.message}`, 'error');
            }
        }
        
        // 获取流列表
        async function listStreams() {
            log('正在获取流列表...', 'info');
            
            try {
                const response = await fetch('/api/video/streams');
                const data = await response.json();
                
                if (data.success) {
                    log(`获取到 ${data.streams.length} 个流`, 'success');
                    
                    // 清空现有视频
                    streams.forEach((_, streamId) => {
                        removeVideoElement(streamId);
                    });
                    
                    // 添加所有流
                    data.streams.forEach(stream => {
                        if (stream.status === 'active') {
                            addVideoElement(
                                stream.stream_id,
                                stream.source_id,
                                stream.stream_type,
                                stream.http_url
                            );
                        }
                    });
                } else {
                    log(`获取流列表失败: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`请求错误: ${error.message}`, 'error');
            }
        }
        
        // WebSocket事件处理
        socket.on('connect', () => {
            log('已连接到WebSocket服务器', 'success');
        });
        
        socket.on('disconnect', () => {
            log('已断开与WebSocket服务器的连接', 'error');
        });
        
        socket.on('stream_started', (data) => {
            log(`收到新流通知: ${data.stream_id} (${data.stream_type})`, 'info');
            addVideoElement(
                data.stream_id,
                data.source_id,
                data.stream_type,
                data.http_url
            );
        });
        
        socket.on('stream_stopped', (data) => {
            log(`收到流停止通知: ${data.stream_id}`, 'info');
            if (streams.has(data.stream_id)) {
                removeVideoElement(data.stream_id);
            }
        });
        
        socket.on('stream_broadcast', (data) => {
            log(`收到流广播通知: ${data.stream_id} 到小组 ${data.target_group}`, 'info');
        });
        
        // 事件监听
        startScreenButton.addEventListener('click', startScreenCapture);
        startCameraButton.addEventListener('click', startCameraCapture);
        listStreamsButton.addEventListener('click', listStreams);
        
        // 初始化
        log('页面加载完成', 'info');
    </script>
</body>
</html>