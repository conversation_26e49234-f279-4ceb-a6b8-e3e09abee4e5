<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧课堂系统 - 安全监控面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .card h3 {
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .stat-item:last-child {
            border-bottom: none;
        }

        .stat-label {
            color: #666;
        }

        .stat-value {
            font-weight: bold;
            color: #333;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background-color: #4CAF50;
        }

        .status-offline {
            background-color: #f44336;
        }

        .status-warning {
            background-color: #ff9800;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #5a6fd8;
        }

        .btn-danger {
            background: #f44336;
        }

        .btn-danger:hover {
            background: #d32f2f;
        }

        .btn-success {
            background: #4CAF50;
        }

        .btn-success:hover {
            background: #45a049;
        }

        .log-container {
            max-height: 400px;
            overflow-y: auto;
            background: #f5f5f5;
            border-radius: 5px;
            padding: 10px;
        }

        .log-entry {
            padding: 8px;
            margin-bottom: 5px;
            background: white;
            border-radius: 3px;
            border-left: 4px solid #667eea;
            font-size: 14px;
        }

        .log-entry.error {
            border-left-color: #f44336;
        }

        .log-entry.warning {
            border-left-color: #ff9800;
        }

        .log-entry.success {
            border-left-color: #4CAF50;
        }

        .timestamp {
            color: #999;
            font-size: 12px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }

        .table tr:hover {
            background-color: #f5f5f5;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }

        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }

        .alert-warning {
            color: #856404;
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }

        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            border: none;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s;
        }

        .refresh-btn:hover {
            background: #5a6fd8;
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 智慧课堂系统 - 安全监控面板</h1>
            <p>实时监控系统安全状态，管理用户权限和审计日志</p>
        </div>

        <div id="alerts-container"></div>

        <div class="dashboard-grid">
            <!-- 系统安全状态 -->
            <div class="card">
                <h3>🛡️ 系统安全状态</h3>
                <div id="security-status">
                    <div class="loading">加载中...</div>
                </div>
            </div>

            <!-- 用户统计 -->
            <div class="card">
                <h3>👥 用户统计</h3>
                <div id="user-stats">
                    <div class="loading">加载中...</div>
                </div>
            </div>

            <!-- 会话统计 -->
            <div class="card">
                <h3>🔑 会话统计</h3>
                <div id="session-stats">
                    <div class="loading">加载中...</div>
                </div>
            </div>

            <!-- 加密状态 -->
            <div class="card">
                <h3>🔐 数据加密状态</h3>
                <div id="encryption-status">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>

        <!-- 活跃用户 -->
        <div class="card">
            <h3>👤 活跃用户</h3>
            <div id="active-users">
                <div class="loading">加载中...</div>
            </div>
        </div>

        <!-- 审计日志 -->
        <div class="card">
            <h3>📋 最近审计日志</h3>
            <div class="log-container" id="audit-logs">
                <div class="loading">加载中...</div>
            </div>
        </div>

        <!-- 安全扫描 -->
        <div class="card">
            <h3>🔍 安全扫描</h3>
            <div>
                <button class="btn" onclick="performSecurityScan('basic')">基础扫描</button>
                <button class="btn" onclick="performSecurityScan('full')">完整扫描</button>
                <div id="scan-results" style="margin-top: 15px;"></div>
            </div>
        </div>
    </div>

    <button class="refresh-btn" onclick="refreshDashboard()" title="刷新数据">
        🔄
    </button>

    <script>
        let authToken = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 尝试从localStorage获取token
            authToken = localStorage.getItem('admin_token');
            
            if (!authToken) {
                showAlert('请先登录管理员账户', 'warning');
                // 这里可以重定向到登录页面
                return;
            }

            loadDashboard();
            
            // 每30秒自动刷新
            setInterval(loadDashboard, 30000);
        });

        async function loadDashboard() {
            try {
                await Promise.all([
                    loadSecurityStatus(),
                    loadUserStats(),
                    loadSessionStats(),
                    loadEncryptionStatus(),
                    loadActiveUsers(),
                    loadAuditLogs()
                ]);
            } catch (error) {
                console.error('加载仪表板数据失败:', error);
                showAlert('加载数据失败，请检查网络连接', 'danger');
            }
        }

        async function loadSecurityStatus() {
            try {
                const response = await fetch('/api/security/security-config', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displaySecurityStatus(data.config);
                } else {
                    throw new Error('获取安全配置失败');
                }
            } catch (error) {
                document.getElementById('security-status').innerHTML = 
                    '<div class="alert alert-danger">加载安全状态失败</div>';
            }
        }

        function displaySecurityStatus(config) {
            const container = document.getElementById('security-status');
            
            const html = `
                <div class="stat-item">
                    <span class="stat-label">密码策略</span>
                    <span class="stat-value">
                        <span class="status-indicator status-online"></span>
                        已启用
                    </span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">会话超时</span>
                    <span class="stat-value">${config.session_policy?.max_duration_hours || 24}小时</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">登录失败锁定</span>
                    <span class="stat-value">${config.login_policy?.max_failed_attempts || 5}次</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">数据加密</span>
                    <span class="stat-value">
                        <span class="status-indicator ${config.encryption_policy?.encrypt_sensitive_data ? 'status-online' : 'status-offline'}"></span>
                        ${config.encryption_policy?.encrypt_sensitive_data ? '已启用' : '已禁用'}
                    </span>
                </div>
            `;
            
            container.innerHTML = html;
        }

        async function loadUserStats() {
            try {
                const response = await fetch('/api/security/users?per_page=1000', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayUserStats(data.users);
                } else {
                    throw new Error('获取用户统计失败');
                }
            } catch (error) {
                document.getElementById('user-stats').innerHTML = 
                    '<div class="alert alert-danger">加载用户统计失败</div>';
            }
        }

        function displayUserStats(users) {
            const container = document.getElementById('user-stats');
            
            const stats = {
                total: users.length,
                admin: users.filter(u => u.role === 'admin').length,
                teacher: users.filter(u => u.role === 'teacher').length,
                student: users.filter(u => u.role === 'student').length,
                active: users.filter(u => u.is_active).length,
                locked: users.filter(u => u.is_locked).length
            };
            
            const html = `
                <div class="stat-item">
                    <span class="stat-label">总用户数</span>
                    <span class="stat-value">${stats.total}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">管理员</span>
                    <span class="stat-value">${stats.admin}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">教师</span>
                    <span class="stat-value">${stats.teacher}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">学生</span>
                    <span class="stat-value">${stats.student}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">活跃用户</span>
                    <span class="stat-value">${stats.active}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">锁定用户</span>
                    <span class="stat-value ${stats.locked > 0 ? 'status-warning' : ''}">${stats.locked}</span>
                </div>
            `;
            
            container.innerHTML = html;
        }

        async function loadSessionStats() {
            // 模拟会话统计数据
            const container = document.getElementById('session-stats');
            
            const html = `
                <div class="stat-item">
                    <span class="stat-label">活跃会话</span>
                    <span class="stat-value">12</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">今日登录</span>
                    <span class="stat-value">45</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">失败登录</span>
                    <span class="stat-value">3</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">平均会话时长</span>
                    <span class="stat-value">2.5小时</span>
                </div>
            `;
            
            container.innerHTML = html;
        }

        async function loadEncryptionStatus() {
            const container = document.getElementById('encryption-status');
            
            const html = `
                <div class="stat-item">
                    <span class="stat-label">加密算法</span>
                    <span class="stat-value">AES-256</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">已加密数据</span>
                    <span class="stat-value">1,234项</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">密钥状态</span>
                    <span class="stat-value">
                        <span class="status-indicator status-online"></span>
                        正常
                    </span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">上次密钥轮换</span>
                    <span class="stat-value">7天前</span>
                </div>
            `;
            
            container.innerHTML = html;
        }

        async function loadActiveUsers() {
            const container = document.getElementById('active-users');
            
            // 模拟活跃用户数据
            const activeUsers = [
                { username: 'admin', name: '系统管理员', role: 'admin', last_activity: '2分钟前', ip: '*************' },
                { username: 'teacher1', name: '张老师', role: 'teacher', last_activity: '5分钟前', ip: '*************' },
                { username: 'teacher2', name: '李老师', role: 'teacher', last_activity: '10分钟前', ip: '*************' },
                { username: 'student1', name: '王同学', role: 'student', last_activity: '1分钟前', ip: '*************' }
            ];
            
            const html = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>用户名</th>
                            <th>姓名</th>
                            <th>角色</th>
                            <th>最后活动</th>
                            <th>IP地址</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${activeUsers.map(user => `
                            <tr>
                                <td>${user.username}</td>
                                <td>${user.name}</td>
                                <td>${getRoleLabel(user.role)}</td>
                                <td>${user.last_activity}</td>
                                <td>${user.ip}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = html;
        }

        async function loadAuditLogs() {
            try {
                const response = await fetch('/api/security/audit-logs?limit=20', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayAuditLogs(data.logs);
                } else {
                    throw new Error('获取审计日志失败');
                }
            } catch (error) {
                document.getElementById('audit-logs').innerHTML = 
                    '<div class="alert alert-danger">加载审计日志失败</div>';
            }
        }

        function displayAuditLogs(logs) {
            const container = document.getElementById('audit-logs');
            
            if (!logs || logs.length === 0) {
                container.innerHTML = '<div class="log-entry">暂无审计日志</div>';
                return;
            }
            
            const html = logs.map(log => {
                const logClass = getLogClass(log.result);
                const timestamp = new Date(log.created_at).toLocaleString();
                
                return `
                    <div class="log-entry ${logClass}">
                        <div>
                            <strong>${log.action}</strong> - ${log.username || '系统'}
                            <span class="timestamp">${timestamp}</span>
                        </div>
                        <div>${log.details ? JSON.stringify(log.details) : ''}</div>
                        ${log.error_message ? `<div style="color: #f44336;">${log.error_message}</div>` : ''}
                    </div>
                `;
            }).join('');
            
            container.innerHTML = html;
        }

        async function performSecurityScan(scanType) {
            const resultsContainer = document.getElementById('scan-results');
            resultsContainer.innerHTML = '<div class="loading">正在执行安全扫描...</div>';
            
            try {
                const response = await fetch('/api/security/security-scan', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ scan_type: scanType })
                });

                if (response.ok) {
                    const data = await response.json();
                    displayScanResults(data.scan_results);
                } else {
                    throw new Error('安全扫描失败');
                }
            } catch (error) {
                resultsContainer.innerHTML = '<div class="alert alert-danger">安全扫描失败</div>';
            }
        }

        function displayScanResults(results) {
            const container = document.getElementById('scan-results');
            
            if (!results.results || results.results.length === 0) {
                container.innerHTML = '<div class="alert alert-success">✅ 未发现安全问题</div>';
                return;
            }
            
            const html = results.results.map(result => {
                const alertClass = getSeverityClass(result.severity);
                return `
                    <div class="alert ${alertClass}">
                        <strong>${result.type}</strong>: ${result.message}
                        ${result.details ? `<br><small>${JSON.stringify(result.details)}</small>` : ''}
                    </div>
                `;
            }).join('');
            
            container.innerHTML = html;
        }

        function refreshDashboard() {
            showAlert('正在刷新数据...', 'success');
            loadDashboard();
        }

        function showAlert(message, type) {
            const alertsContainer = document.getElementById('alerts-container');
            const alertClass = `alert-${type}`;
            
            const alertHtml = `
                <div class="alert ${alertClass}">
                    ${message}
                    <button style="float: right; background: none; border: none; font-size: 18px; cursor: pointer;" onclick="this.parentElement.remove()">×</button>
                </div>
            `;
            
            alertsContainer.innerHTML = alertHtml;
            
            // 3秒后自动消失
            setTimeout(() => {
                alertsContainer.innerHTML = '';
            }, 3000);
        }

        function getRoleLabel(role) {
            const roleLabels = {
                'admin': '管理员',
                'teacher': '教师',
                'student': '学生',
                'guest': '访客'
            };
            return roleLabels[role] || role;
        }

        function getLogClass(result) {
            const classMap = {
                'success': 'success',
                'failure': 'error',
                'error': 'error'
            };
            return classMap[result] || '';
        }

        function getSeverityClass(severity) {
            const classMap = {
                'high': 'alert-danger',
                'medium': 'alert-warning',
                'low': 'alert-success',
                'info': 'alert-success'
            };
            return classMap[severity] || 'alert-success';
        }
    </script>
</body>
</html>