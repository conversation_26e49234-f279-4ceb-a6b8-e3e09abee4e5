<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧课堂系统 - 系统监控面板</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 1.8rem;
            font-weight: 300;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #2c3e50;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 0.5rem;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
        }

        .metric-item {
            text-align: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .metric-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .metric-label {
            font-size: 0.9rem;
            color: #7f8c8d;
            margin-top: 0.5rem;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-running { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-error { background-color: #e74c3c; }
        .status-stopped { background-color: #95a5a6; }

        .service-list {
            list-style: none;
        }

        .service-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            border-radius: 6px;
            transition: background-color 0.2s ease;
        }

        .service-item:hover {
            background: #e9ecef;
        }

        .service-name {
            font-weight: 500;
        }

        .service-status {
            display: flex;
            align-items: center;
            font-size: 0.9rem;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 1rem;
        }

        .controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .alert {
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
        }

        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #7f8c8d;
        }

        .timestamp {
            font-size: 0.8rem;
            color: #7f8c8d;
            text-align: right;
            margin-top: 1rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>智慧课堂系统 - 系统监控面板</h1>
    </div>

    <div class="container">
        <div class="controls">
            <button class="btn btn-primary" onclick="refreshData()">刷新数据</button>
            <button class="btn btn-success" onclick="startMonitoring()">开始监控</button>
            <button class="btn btn-warning" onclick="stopMonitoring()">停止监控</button>
            <button class="btn btn-danger" onclick="runStressTest()">压力测试</button>
        </div>

        <div id="alerts"></div>

        <div class="dashboard-grid">
            <!-- 系统概览 -->
            <div class="card">
                <div class="card-title">系统概览</div>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="uptime">--</div>
                        <div class="metric-label">运行时间(小时)</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="active-connections">--</div>
                        <div class="metric-label">活跃连接</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="response-time">--</div>
                        <div class="metric-label">响应时间(ms)</div>
                    </div>
                </div>
            </div>

            <!-- 服务状态 -->
            <div class="card">
                <div class="card-title">服务状态</div>
                <ul class="service-list" id="service-list">
                    <li class="loading">加载中...</li>
                </ul>
            </div>

            <!-- 性能指标 -->
            <div class="card">
                <div class="card-title">性能指标</div>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="cpu-usage">--</div>
                        <div class="metric-label">CPU使用率(%)</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="memory-usage">--</div>
                        <div class="metric-label">内存使用率(%)</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="disk-usage">--</div>
                        <div class="metric-label">磁盘使用率(%)</div>
                    </div>
                </div>
            </div>

            <!-- 视频流指标 -->
            <div class="card">
                <div class="card-title">视频流指标</div>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="video-delay">--</div>
                        <div class="metric-label">视频延迟(ms)</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="broadcast-delay">--</div>
                        <div class="metric-label">广播延迟(ms)</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="frame-rate">--</div>
                        <div class="metric-label">帧率(fps)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 性能图表 -->
        <div class="card">
            <div class="card-title">性能趋势</div>
            <div class="chart-container">
                <canvas id="performance-chart"></canvas>
            </div>
        </div>

        <div class="timestamp" id="last-update">
            最后更新: --
        </div>
    </div>

    <script>
        let socket;
        let performanceChart;
        let monitoringActive = false;
        let chartData = {
            labels: [],
            datasets: [
                {
                    label: 'CPU使用率',
                    data: [],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    tension: 0.4
                },
                {
                    label: '内存使用率',
                    data: [],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4
                },
                {
                    label: '响应时间(ms)',
                    data: [],
                    borderColor: '#f39c12',
                    backgroundColor: 'rgba(243, 156, 18, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }
            ]
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeChart();
            initializeWebSocket();
            refreshData();
        });

        function initializeChart() {
            const ctx = document.getElementById('performance-chart').getContext('2d');
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: '使用率 (%)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '响应时间 (ms)'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });
        }

        function initializeWebSocket() {
            socket = io();
            
            socket.on('connect', function() {
                console.log('WebSocket连接已建立');
                showAlert('WebSocket连接已建立', 'info');
            });

            socket.on('disconnect', function() {
                console.log('WebSocket连接已断开');
                showAlert('WebSocket连接已断开', 'warning');
            });

            socket.on('system_status_update', function(data) {
                if (data.success) {
                    updateSystemStatus(data.data);
                }
            });

            socket.on('performance_metrics_update', function(data) {
                if (data.success) {
                    updatePerformanceMetrics(data.data);
                }
            });
        }

        async function refreshData() {
            try {
                // 获取系统状态
                const systemResponse = await fetch('/api/system/status');
                const systemData = await systemResponse.json();
                
                if (systemData.success) {
                    updateSystemStatus(systemData.data.system);
                    updatePerformanceMetrics(systemData.data.performance.metrics);
                }

                document.getElementById('last-update').textContent = 
                    `最后更新: ${new Date().toLocaleString()}`;

            } catch (error) {
                console.error('刷新数据失败:', error);
                showAlert('刷新数据失败: ' + error.message, 'danger');
            }
        }

        function updateSystemStatus(data) {
            // 更新运行时间
            const uptime = Math.round(data.uptime / 3600);
            document.getElementById('uptime').textContent = uptime;

            // 更新活跃连接数
            const connections = data.metrics.active_connections || 0;
            document.getElementById('active-connections').textContent = connections;

            // 更新响应时间
            const responseTime = Math.round((data.metrics.response_time || 0) * 1000);
            document.getElementById('response-time').textContent = responseTime;

            // 更新服务列表
            updateServiceList(data.services);
        }

        function updateServiceList(services) {
            const serviceList = document.getElementById('service-list');
            serviceList.innerHTML = '';

            for (const [name, info] of Object.entries(services)) {
                const li = document.createElement('li');
                li.className = 'service-item';
                
                const statusClass = getStatusClass(info.status);
                
                li.innerHTML = `
                    <span class="service-name">${name}</span>
                    <span class="service-status">
                        <span class="status-indicator ${statusClass}"></span>
                        ${info.status}
                        ${info.restart_count > 0 ? `(重启${info.restart_count}次)` : ''}
                    </span>
                `;
                
                serviceList.appendChild(li);
            }
        }

        function updatePerformanceMetrics(metrics) {
            // 更新CPU使用率
            const cpuUsage = Math.round(metrics.cpu_usage || 0);
            document.getElementById('cpu-usage').textContent = cpuUsage;
            updateMetricColor('cpu-usage', cpuUsage, 70, 85);

            // 更新内存使用率
            const memoryUsage = Math.round(metrics.memory_usage || 0);
            document.getElementById('memory-usage').textContent = memoryUsage;
            updateMetricColor('memory-usage', memoryUsage, 75, 90);

            // 更新磁盘使用率
            const diskUsage = Math.round(metrics.disk_usage || 0);
            document.getElementById('disk-usage').textContent = diskUsage;
            updateMetricColor('disk-usage', diskUsage, 80, 95);

            // 更新视频指标
            const videoDelay = Math.round((metrics.video_stream_delay || 0) * 1000);
            document.getElementById('video-delay').textContent = videoDelay;

            const broadcastDelay = Math.round((metrics.broadcast_delay || 0) * 1000);
            document.getElementById('broadcast-delay').textContent = broadcastDelay;

            const frameRate = Math.round(metrics.frame_rate || 0);
            document.getElementById('frame-rate').textContent = frameRate;

            // 更新图表
            updateChart(cpuUsage, memoryUsage, Math.round((metrics.response_time || 0) * 1000));
        }

        function updateMetricColor(elementId, value, warningThreshold, criticalThreshold) {
            const element = document.getElementById(elementId);
            element.style.color = value >= criticalThreshold ? '#e74c3c' : 
                                 value >= warningThreshold ? '#f39c12' : '#27ae60';
        }

        function updateChart(cpu, memory, responseTime) {
            const now = new Date().toLocaleTimeString();
            
            // 保持最近20个数据点
            if (chartData.labels.length >= 20) {
                chartData.labels.shift();
                chartData.datasets[0].data.shift();
                chartData.datasets[1].data.shift();
                chartData.datasets[2].data.shift();
            }
            
            chartData.labels.push(now);
            chartData.datasets[0].data.push(cpu);
            chartData.datasets[1].data.push(memory);
            chartData.datasets[2].data.push(responseTime);
            
            performanceChart.update('none');
        }

        function getStatusClass(status) {
            switch (status) {
                case 'running': return 'status-running';
                case 'error': return 'status-error';
                case 'warning': return 'status-warning';
                default: return 'status-stopped';
            }
        }

        function startMonitoring() {
            if (!monitoringActive) {
                monitoringActive = true;
                socket.emit('system_status_subscribe');
                socket.emit('performance_metrics_subscribe');
                
                // 定期刷新数据
                setInterval(() => {
                    if (monitoringActive) {
                        socket.emit('system_status_subscribe');
                        socket.emit('performance_metrics_subscribe');
                    }
                }, 5000);
                
                showAlert('监控已启动', 'success');
            }
        }

        function stopMonitoring() {
            monitoringActive = false;
            showAlert('监控已停止', 'info');
        }

        async function runStressTest() {
            try {
                showAlert('正在启动压力测试...', 'info');
                
                const response = await fetch('/api/system/stress-test/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        test_type: 'api',
                        concurrent_users: 10,
                        test_duration: 30
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showAlert('压力测试已完成', 'success');
                } else {
                    showAlert('压力测试失败: ' + data.error, 'danger');
                }
                
            } catch (error) {
                console.error('压力测试失败:', error);
                showAlert('压力测试失败: ' + error.message, 'danger');
            }
        }

        function showAlert(message, type) {
            const alertsContainer = document.getElementById('alerts');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            alertsContainer.appendChild(alert);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 3000);
        }
    </script>
</body>
</html>