<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧课堂 - 协同白板</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .toolbar {
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .toolbar-group {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 0 10px;
            border-right: 1px solid #e0e0e0;
        }
        
        .toolbar-group:last-child {
            border-right: none;
        }
        
        .tool-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .tool-btn:hover {
            background: #f0f0f0;
        }
        
        .tool-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .color-picker {
            width: 30px;
            height: 30px;
            border: 2px solid #ddd;
            border-radius: 50%;
            cursor: pointer;
            margin: 0 2px;
        }
        
        .size-slider {
            width: 100px;
        }
        
        .whiteboard-container {
            position: relative;
            width: 100%;
            height: calc(100vh - 60px);
            background: white;
            overflow: hidden;
        }
        
        .canvas-container {
            width: 100%;
            height: 100%;
            position: relative;
        }
        
        #whiteboard-canvas {
            width: 100%;
            height: 100%;
            cursor: crosshair;
        }
        
        .status-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #333;
            color: white;
            padding: 5px 15px;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .user-cursors {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }
        
        .user-cursor {
            position: absolute;
            width: 20px;
            height: 20px;
            pointer-events: none;
            transition: all 0.1s ease;
        }
        
        .cursor-pointer {
            width: 0;
            height: 0;
            border-left: 10px solid;
            border-right: 5px solid transparent;
            border-bottom: 5px solid transparent;
            border-top: 5px solid transparent;
        }
        
        .cursor-label {
            position: absolute;
            top: 20px;
            left: 0;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="toolbar">
        <div class="toolbar-group">
            <button class="tool-btn active" data-tool="pen">画笔</button>
            <button class="tool-btn" data-tool="highlighter">荧光笔</button>
            <button class="tool-btn" data-tool="eraser">橡皮擦</button>
            <button class="tool-btn" data-tool="text">文本</button>
        </div>
        
        <div class="toolbar-group">
            <button class="tool-btn" data-tool="rectangle">矩形</button>
            <button class="tool-btn" data-tool="ellipse">椭圆</button>
            <button class="tool-btn" data-tool="arrow">箭头</button>
            <button class="tool-btn" data-tool="line">直线</button>
        </div>
        
        <div class="toolbar-group">
            <span>颜色:</span>
            <div class="color-picker" data-color="#000000" style="background-color: #000000;"></div>
            <div class="color-picker" data-color="#FF0000" style="background-color: #FF0000;"></div>
            <div class="color-picker" data-color="#00FF00" style="background-color: #00FF00;"></div>
            <div class="color-picker" data-color="#0000FF" style="background-color: #0000FF;"></div>
            <div class="color-picker" data-color="#FFFF00" style="background-color: #FFFF00;"></div>
            <div class="color-picker" data-color="#FF00FF" style="background-color: #FF00FF;"></div>
            <div class="color-picker" data-color="#00FFFF" style="background-color: #00FFFF;"></div>
        </div>
        
        <div class="toolbar-group">
            <span>粗细:</span>
            <input type="range" class="size-slider" min="1" max="20" value="2" id="brush-size">
            <span id="size-display">2px</span>
        </div>
        
        <div class="toolbar-group">
            <button class="tool-btn" onclick="undo()">撤销</button>
            <button class="tool-btn" onclick="redo()">重做</button>
            <button class="tool-btn" onclick="clearCanvas()">清空</button>
        </div>
        
        <div class="toolbar-group">
            <button class="tool-btn" onclick="saveWhiteboard()">保存</button>
            <button class="tool-btn" onclick="loadWhiteboard()">加载</button>
        </div>
    </div>
    
    <div class="whiteboard-container">
        <div class="canvas-container">
            <canvas id="whiteboard-canvas"></canvas>
            <div class="user-cursors" id="user-cursors"></div>
        </div>
    </div>
    
    <div class="status-bar">
        <div>
            <span id="connection-status">连接状态: 未连接</span>
            <span id="room-info">房间: 未加入</span>
        </div>
        <div>
            <span id="user-count">在线用户: 0</span>
            <span id="version-info">版本: 1</span>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script>
        // 白板应用类
        class WhiteboardApp {
            constructor() {
                this.canvas = document.getElementById('whiteboard-canvas');
                this.ctx = this.canvas.getContext('2d');
                this.socket = null;
                this.whiteboardId = null;
                this.userId = null;
                this.currentTool = 'pen';
                this.currentColor = '#000000';
                this.currentSize = 2;
                this.isDrawing = false;
                this.lastX = 0;
                this.lastY = 0;
                this.elements = [];
                this.undoStack = [];
                this.redoStack = [];
                this.userCursors = new Map();
                
                this.initCanvas();
                this.initEventListeners();
                this.initSocket();
                this.parseUrlParams();
            }
            
            initCanvas() {
                // 设置画布大小
                this.resizeCanvas();
                window.addEventListener('resize', () => this.resizeCanvas());
                
                // 设置画布样式
                this.ctx.lineCap = 'round';
                this.ctx.lineJoin = 'round';
                this.ctx.strokeStyle = this.currentColor;
                this.ctx.lineWidth = this.currentSize;
            }
            
            resizeCanvas() {
                const container = this.canvas.parentElement;
                this.canvas.width = container.clientWidth;
                this.canvas.height = container.clientHeight;
                this.redrawCanvas();
            }
            
            initEventListeners() {
                // 工具选择
                document.querySelectorAll('.tool-btn[data-tool]').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        this.selectTool(e.target.dataset.tool);
                    });
                });
                
                // 颜色选择
                document.querySelectorAll('.color-picker').forEach(picker => {
                    picker.addEventListener('click', (e) => {
                        this.selectColor(e.target.dataset.color);
                    });
                });
                
                // 笔刷大小
                const sizeSlider = document.getElementById('brush-size');
                sizeSlider.addEventListener('input', (e) => {
                    this.selectSize(parseInt(e.target.value));
                });
                
                // 画布事件
                this.canvas.addEventListener('mousedown', (e) => this.startDrawing(e));
                this.canvas.addEventListener('mousemove', (e) => this.draw(e));
                this.canvas.addEventListener('mouseup', () => this.stopDrawing());
                this.canvas.addEventListener('mouseout', () => this.stopDrawing());
                
                // 触摸事件（移动端支持）
                this.canvas.addEventListener('touchstart', (e) => {
                    e.preventDefault();
                    const touch = e.touches[0];
                    const mouseEvent = new MouseEvent('mousedown', {
                        clientX: touch.clientX,
                        clientY: touch.clientY
                    });
                    this.canvas.dispatchEvent(mouseEvent);
                });
                
                this.canvas.addEventListener('touchmove', (e) => {
                    e.preventDefault();
                    const touch = e.touches[0];
                    const mouseEvent = new MouseEvent('mousemove', {
                        clientX: touch.clientX,
                        clientY: touch.clientY
                    });
                    this.canvas.dispatchEvent(mouseEvent);
                });
                
                this.canvas.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    const mouseEvent = new MouseEvent('mouseup', {});
                    this.canvas.dispatchEvent(mouseEvent);
                });
                
                // 光标移动事件
                this.canvas.addEventListener('mousemove', (e) => {
                    if (!this.isDrawing) {
                        this.sendCursorPosition(e);
                    }
                });
            }
            
            initSocket() {
                this.socket = io();
                
                this.socket.on('connect', () => {
                    document.getElementById('connection-status').textContent = '连接状态: 已连接';
                    if (this.whiteboardId) {
                        this.joinWhiteboard();
                    }
                });
                
                this.socket.on('disconnect', () => {
                    document.getElementById('connection-status').textContent = '连接状态: 已断开';
                });
                
                // 白板相关事件
                this.socket.on('whiteboard_updated', (data) => {
                    this.handleWhiteboardUpdate(data);
                });
                
                this.socket.on('element_changed', (data) => {
                    this.handleElementChange(data);
                });
                
                this.socket.on('cursor_moved', (data) => {
                    this.handleCursorMove(data);
                });
                
                this.socket.on('user_joined_whiteboard', (data) => {
                    this.handleUserJoined(data);
                });
                
                this.socket.on('user_left_whiteboard', (data) => {
                    this.handleUserLeft(data);
                });
            }
            
            parseUrlParams() {
                const params = new URLSearchParams(window.location.search);
                this.whiteboardId = params.get('whiteboard_id') || 'default';
                this.userId = params.get('user_id') || 'user_' + Math.random().toString(36).substr(2, 9);
                
                document.getElementById('room-info').textContent = `房间: ${this.whiteboardId}`;
                
                if (this.socket && this.socket.connected) {
                    this.joinWhiteboard();
                }
            }
            
            joinWhiteboard() {
                this.socket.emit('join_whiteboard', {
                    whiteboard_id: this.whiteboardId,
                    user_id: this.userId
                });
            }
            
            selectTool(tool) {
                this.currentTool = tool;
                
                // 更新UI
                document.querySelectorAll('.tool-btn[data-tool]').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelector(`[data-tool="${tool}"]`).classList.add('active');
                
                // 更新光标样式
                this.updateCursor();
            }
            
            selectColor(color) {
                this.currentColor = color;
                this.ctx.strokeStyle = color;
                this.ctx.fillStyle = color;
            }
            
            selectSize(size) {
                this.currentSize = size;
                this.ctx.lineWidth = size;
                document.getElementById('size-display').textContent = size + 'px';
            }
            
            updateCursor() {
                const cursors = {
                    pen: 'crosshair',
                    highlighter: 'crosshair',
                    eraser: 'crosshair',
                    text: 'text',
                    rectangle: 'crosshair',
                    ellipse: 'crosshair',
                    arrow: 'crosshair',
                    line: 'crosshair'
                };
                
                this.canvas.style.cursor = cursors[this.currentTool] || 'default';
            }
            
            getMousePos(e) {
                const rect = this.canvas.getBoundingClientRect();
                return {
                    x: e.clientX - rect.left,
                    y: e.clientY - rect.top
                };
            }
            
            startDrawing(e) {
                this.isDrawing = true;
                const pos = this.getMousePos(e);
                this.lastX = pos.x;
                this.lastY = pos.y;
                
                if (this.currentTool === 'pen' || this.currentTool === 'highlighter') {
                    this.ctx.beginPath();
                    this.ctx.moveTo(this.lastX, this.lastY);
                }
            }
            
            draw(e) {
                if (!this.isDrawing) return;
                
                const pos = this.getMousePos(e);
                
                switch (this.currentTool) {
                    case 'pen':
                    case 'highlighter':
                        this.drawLine(pos.x, pos.y);
                        break;
                    case 'eraser':
                        this.erase(pos.x, pos.y);
                        break;
                }
                
                this.lastX = pos.x;
                this.lastY = pos.y;
            }
            
            drawLine(x, y) {
                this.ctx.globalCompositeOperation = this.currentTool === 'highlighter' ? 'multiply' : 'source-over';
                this.ctx.globalAlpha = this.currentTool === 'highlighter' ? 0.5 : 1.0;
                
                this.ctx.lineTo(x, y);
                this.ctx.stroke();
                this.ctx.beginPath();
                this.ctx.moveTo(x, y);
            }
            
            erase(x, y) {
                this.ctx.globalCompositeOperation = 'destination-out';
                this.ctx.beginPath();
                this.ctx.arc(x, y, this.currentSize, 0, Math.PI * 2);
                this.ctx.fill();
                this.ctx.globalCompositeOperation = 'source-over';
            }
            
            stopDrawing() {
                if (!this.isDrawing) return;
                this.isDrawing = false;
                
                // 保存当前状态到历史记录
                this.saveState();
                
                // 发送变更到其他用户
                this.broadcastChange();
            }
            
            saveState() {
                this.undoStack.push(this.canvas.toDataURL());
                this.redoStack = []; // 清空重做栈
                
                // 限制历史记录数量
                if (this.undoStack.length > 50) {
                    this.undoStack.shift();
                }
            }
            
            broadcastChange() {
                if (this.socket) {
                    this.socket.emit('whiteboard_element_change', {
                        whiteboard_id: this.whiteboardId,
                        user_id: this.userId,
                        element_data: this.canvas.toDataURL(),
                        change_type: 'update',
                        timestamp: Date.now()
                    });
                }
            }
            
            sendCursorPosition(e) {
                if (this.socket) {
                    const pos = this.getMousePos(e);
                    this.socket.emit('whiteboard_cursor_move', {
                        whiteboard_id: this.whiteboardId,
                        user_id: this.userId,
                        cursor_position: { x: pos.x, y: pos.y }
                    });
                }
            }
            
            handleWhiteboardUpdate(data) {
                // 处理白板内容更新
                if (data.content && data.content.elements) {
                    this.elements = data.content.elements;
                    this.redrawCanvas();
                }
            }
            
            handleElementChange(data) {
                // 处理元素变更
                if (data.user_id !== this.userId && data.element_data) {
                    const img = new Image();
                    img.onload = () => {
                        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                        this.ctx.drawImage(img, 0, 0);
                    };
                    img.src = data.element_data;
                }
            }
            
            handleCursorMove(data) {
                // 处理其他用户光标移动
                if (data.user_id !== this.userId) {
                    this.updateUserCursor(data.user_id, data.cursor_position);
                }
            }
            
            handleUserJoined(data) {
                console.log(`用户 ${data.user_id} 加入白板`);
                this.updateUserCount();
            }
            
            handleUserLeft(data) {
                console.log(`用户 ${data.user_id} 离开白板`);
                this.removeUserCursor(data.user_id);
                this.updateUserCount();
            }
            
            updateUserCursor(userId, position) {
                const cursorsContainer = document.getElementById('user-cursors');
                let cursor = cursorsContainer.querySelector(`[data-user="${userId}"]`);
                
                if (!cursor) {
                    cursor = document.createElement('div');
                    cursor.className = 'user-cursor';
                    cursor.dataset.user = userId;
                    cursor.innerHTML = `
                        <div class="cursor-pointer" style="border-left-color: ${this.getUserColor(userId)};"></div>
                        <div class="cursor-label">${userId}</div>
                    `;
                    cursorsContainer.appendChild(cursor);
                }
                
                cursor.style.left = position.x + 'px';
                cursor.style.top = position.y + 'px';
            }
            
            removeUserCursor(userId) {
                const cursor = document.querySelector(`[data-user="${userId}"]`);
                if (cursor) {
                    cursor.remove();
                }
            }
            
            getUserColor(userId) {
                // 为用户生成固定颜色
                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
                const hash = userId.split('').reduce((a, b) => {
                    a = ((a << 5) - a) + b.charCodeAt(0);
                    return a & a;
                }, 0);
                return colors[Math.abs(hash) % colors.length];
            }
            
            updateUserCount() {
                const cursors = document.querySelectorAll('.user-cursor');
                document.getElementById('user-count').textContent = `在线用户: ${cursors.length + 1}`;
            }
            
            redrawCanvas() {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                // 这里可以重绘所有元素
            }
        }
        
        // 全局函数
        let app;
        
        function undo() {
            if (app && app.undoStack.length > 0) {
                app.redoStack.push(app.canvas.toDataURL());
                const prevState = app.undoStack.pop();
                const img = new Image();
                img.onload = () => {
                    app.ctx.clearRect(0, 0, app.canvas.width, app.canvas.height);
                    app.ctx.drawImage(img, 0, 0);
                };
                img.src = prevState;
            }
        }
        
        function redo() {
            if (app && app.redoStack.length > 0) {
                app.undoStack.push(app.canvas.toDataURL());
                const nextState = app.redoStack.pop();
                const img = new Image();
                img.onload = () => {
                    app.ctx.clearRect(0, 0, app.canvas.width, app.canvas.height);
                    app.ctx.drawImage(img, 0, 0);
                };
                img.src = nextState;
            }
        }
        
        function clearCanvas() {
            if (app && confirm('确定要清空白板吗？')) {
                app.saveState();
                app.ctx.clearRect(0, 0, app.canvas.width, app.canvas.height);
                app.broadcastChange();
            }
        }
        
        function saveWhiteboard() {
            if (app) {
                const link = document.createElement('a');
                link.download = `whiteboard_${app.whiteboardId}_${Date.now()}.png`;
                link.href = app.canvas.toDataURL();
                link.click();
            }
        }
        
        function loadWhiteboard() {
            // 这里可以实现加载白板功能
            alert('加载功能待实现');
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            app = new WhiteboardApp();
        });
    </script>
</body>
</html>