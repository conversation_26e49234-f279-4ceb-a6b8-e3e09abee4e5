#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 视频流API
"""

from flask import Blueprint, jsonify, request, current_app
from flask_socketio import emit
import logging

from backend.services.video_service import get_video_service

# 创建蓝图
video_api = Blueprint('video_api', __name__)

# 配置日志
logger = logging.getLogger('video_api')

@video_api.route('/streams', methods=['GET'])
def list_streams():
    """获取所有流列表"""
    video_service = get_video_service()
    streams = video_service.list_streams()
    
    return jsonify({
        'success': True,
        'streams': streams
    })

@video_api.route('/streams/<stream_id>', methods=['GET'])
def get_stream(stream_id):
    """获取流信息"""
    video_service = get_video_service()
    stream_info = video_service.get_stream_info(stream_id)
    
    if stream_info is None:
        return jsonify({
            'success': False,
            'message': f'找不到流: {stream_id}'
        }), 404
    
    return jsonify({
        'success': True,
        'stream': stream_info
    })

@video_api.route('/streams/source/<source_id>', methods=['GET'])
def get_streams_by_source(source_id):
    """获取指定源的流"""
    video_service = get_video_service()
    streams = video_service.get_active_streams_by_source(source_id)
    
    return jsonify({
        'success': True,
        'streams': streams
    })

@video_api.route('/screen/start', methods=['POST'])
def start_screen_capture():
    """开始屏幕捕获"""
    data = request.json
    device_id = data.get('device_id')
    display = data.get('display', ':0.0')
    
    if not device_id:
        return jsonify({
            'success': False,
            'message': '缺少设备ID'
        }), 400
    
    video_service = get_video_service()
    stream_info = video_service.start_screen_capture(device_id, display)
    
    if stream_info is None:
        return jsonify({
            'success': False,
            'message': '启动屏幕捕获失败'
        }), 500
    
    # 通过WebSocket通知新流可用
    socketio = current_app.extensions['socketio']
    socketio.emit('stream_started', {
        'stream_id': stream_info.stream_id,
        'source_id': stream_info.source_id,
        'stream_type': stream_info.stream_type,
        'http_url': stream_info.http_url
    })
    
    return jsonify({
        'success': True,
        'message': '屏幕捕获已启动',
        'stream': stream_info.to_dict()
    })

@video_api.route('/camera/start', methods=['POST'])
def start_camera_capture():
    """开始摄像头捕获"""
    data = request.json
    device_id = data.get('device_id')
    camera_device = data.get('camera_device', '/dev/video0')
    
    if not device_id:
        return jsonify({
            'success': False,
            'message': '缺少设备ID'
        }), 400
    
    video_service = get_video_service()
    stream_info = video_service.start_camera_capture(device_id, camera_device)
    
    if stream_info is None:
        return jsonify({
            'success': False,
            'message': '启动摄像头捕获失败'
        }), 500
    
    # 通过WebSocket通知新流可用
    socketio = current_app.extensions['socketio']
    socketio.emit('stream_started', {
        'stream_id': stream_info.stream_id,
        'source_id': stream_info.source_id,
        'stream_type': stream_info.stream_type,
        'http_url': stream_info.http_url
    })
    
    return jsonify({
        'success': True,
        'message': '摄像头捕获已启动',
        'stream': stream_info.to_dict()
    })

@video_api.route('/streams/<stream_id>/stop', methods=['POST'])
def stop_stream(stream_id):
    """停止流"""
    video_service = get_video_service()
    success = video_service.stop_stream(stream_id)
    
    if not success:
        return jsonify({
            'success': False,
            'message': f'停止流失败: {stream_id}'
        }), 404
    
    # 通过WebSocket通知流已停止
    socketio = current_app.extensions['socketio']
    socketio.emit('stream_stopped', {
        'stream_id': stream_id
    })
    
    return jsonify({
        'success': True,
        'message': f'流已停止: {stream_id}'
    })

@video_api.route('/streams/<stream_id>/viewers/<viewer_id>', methods=['POST'])
def add_viewer(stream_id, viewer_id):
    """添加观看者"""
    video_service = get_video_service()
    success = video_service.add_viewer(stream_id, viewer_id)
    
    if not success:
        return jsonify({
            'success': False,
            'message': f'添加观看者失败: {stream_id}'
        }), 404
    
    return jsonify({
        'success': True,
        'message': f'已添加观看者 {viewer_id} 到流 {stream_id}'
    })

@video_api.route('/streams/<stream_id>/viewers/<viewer_id>', methods=['DELETE'])
def remove_viewer(stream_id, viewer_id):
    """移除观看者"""
    video_service = get_video_service()
    success = video_service.remove_viewer(stream_id, viewer_id)
    
    if not success:
        return jsonify({
            'success': False,
            'message': f'移除观看者失败: {stream_id}'
        }), 404
    
    return jsonify({
        'success': True,
        'message': f'已从流 {stream_id} 移除观看者 {viewer_id}'
    })

@video_api.route('/broadcast', methods=['POST'])
def broadcast_stream():
    """广播流到多个目标"""
    data = request.json
    source_stream_id = data.get('source_stream_id')
    target_groups = data.get('target_groups', [])
    
    if not source_stream_id:
        return jsonify({
            'success': False,
            'message': '缺少源流ID'
        }), 400
    
    if not target_groups:
        return jsonify({
            'success': False,
            'message': '缺少目标小组'
        }), 400
    
    video_service = get_video_service()
    source_stream = video_service.get_stream_info(source_stream_id)
    
    if source_stream is None:
        return jsonify({
            'success': False,
            'message': f'找不到源流: {source_stream_id}'
        }), 404
    
    # 通过WebSocket通知目标小组有新的广播流
    socketio = current_app.extensions['socketio']
    for group_id in target_groups:
        socketio.emit('stream_broadcast', {
            'stream_id': source_stream_id,
            'source_id': source_stream['source_id'],
            'stream_type': source_stream['stream_type'],
            'http_url': source_stream['http_url'],
            'target_group': group_id
        }, room=f'group_{group_id}')
    
    return jsonify({
        'success': True,
        'message': f'流 {source_stream_id} 已广播到 {len(target_groups)} 个小组',
        'targets': target_groups
    })

# WebSocket事件处理
def register_socket_events(socketio):
    """注册WebSocket事件处理器"""
    
    @socketio.on('join_group')
    def handle_join_group(data):
        """加入小组房间"""
        group_id = data.get('group_id')
        if group_id:
            room = f'group_{group_id}'
            socketio.join_room(room)
            logger.info(f"客户端加入房间: {room}")
    
    @socketio.on('leave_group')
    def handle_leave_group(data):
        """离开小组房间"""
        group_id = data.get('group_id')
        if group_id:
            room = f'group_{group_id}'
            socketio.leave_room(room)
            logger.info(f"客户端离开房间: {room}")
    
    @socketio.on('start_viewing')
    def handle_start_viewing(data):
        """开始观看流"""
        stream_id = data.get('stream_id')
        viewer_id = data.get('viewer_id')
        
        if stream_id and viewer_id:
            video_service = get_video_service()
            success = video_service.add_viewer(stream_id, viewer_id)
            
            if success:
                logger.info(f"观看者 {viewer_id} 开始观看流 {stream_id}")
            else:
                logger.warning(f"添加观看者失败: {stream_id}, {viewer_id}")
    
    @socketio.on('stop_viewing')
    def handle_stop_viewing(data):
        """停止观看流"""
        stream_id = data.get('stream_id')
        viewer_id = data.get('viewer_id')
        
        if stream_id and viewer_id:
            video_service = get_video_service()
            success = video_service.remove_viewer(stream_id, viewer_id)
            
            if success:
                logger.info(f"观看者 {viewer_id} 停止观看流 {stream_id}")
            else:
                logger.warning(f"移除观看者失败: {stream_id}, {viewer_id}")