#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
白板API接口
"""

import logging
from flask import Blueprint, request, jsonify, current_app
from flask_socketio import emit, join_room, leave_room

from backend.services.whiteboard_service import WhiteboardService

logger = logging.getLogger(__name__)

# 创建蓝图
whiteboard_bp = Blueprint('whiteboard', __name__, url_prefix='/api/whiteboard')

@whiteboard_bp.route('/create', methods=['POST'])
def create_whiteboard():
    """创建白板"""
    try:
        data = request.get_json()
        
        name = data.get('name')
        created_by = data.get('created_by')
        group_id = data.get('group_id')
        classroom_id = data.get('classroom_id')
        
        if not name or not created_by:
            return jsonify({
                'success': False,
                'message': '白板名称和创建者不能为空'
            }), 400
        
        success, message, whiteboard_data = WhiteboardService.create_whiteboard(
            name=name,
            created_by=created_by,
            group_id=group_id,
            classroom_id=classroom_id
        )
        
        if success:
            # 通过WebSocket通知相关用户
            socketio = current_app.extensions.get('socketio')
            if socketio:
                room = f'group_{group_id}' if group_id else f'classroom_{classroom_id}'
                socketio.emit('whiteboard_created', {
                    'whiteboard': whiteboard_data
                }, room=room)
            
            return jsonify({
                'success': True,
                'message': message,
                'whiteboard': whiteboard_data
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 400
            
    except Exception as e:
        logger.error(f"创建白板API错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }), 500

@whiteboard_bp.route('/<whiteboard_id>', methods=['GET'])
def get_whiteboard(whiteboard_id):
    """获取白板信息"""
    try:
        success, message, whiteboard_data = WhiteboardService.get_whiteboard(whiteboard_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': message,
                'whiteboard': whiteboard_data
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 404
            
    except Exception as e:
        logger.error(f"获取白板API错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }), 500

@whiteboard_bp.route('/<whiteboard_id>/content', methods=['PUT'])
def update_whiteboard_content(whiteboard_id):
    """更新白板内容"""
    try:
        data = request.get_json()
        
        content = data.get('content')
        updated_by = data.get('updated_by')
        comment = data.get('comment')
        
        if not content or not updated_by:
            return jsonify({
                'success': False,
                'message': '内容和更新者不能为空'
            }), 400
        
        success, message, version_data = WhiteboardService.update_whiteboard_content(
            whiteboard_id=whiteboard_id,
            content=content,
            updated_by=updated_by,
            comment=comment
        )
        
        if success:
            # 通过WebSocket实时同步内容
            socketio = current_app.extensions.get('socketio')
            if socketio:
                # 获取白板信息以确定房间
                _, _, whiteboard_info = WhiteboardService.get_whiteboard(whiteboard_id)
                if whiteboard_info:
                    room = f'whiteboard_{whiteboard_id}'
                    socketio.emit('whiteboard_updated', {
                        'whiteboard_id': whiteboard_id,
                        'content': content,
                        'updated_by': updated_by,
                        'version': version_data.get('version'),
                        'timestamp': version_data.get('updated_at')
                    }, room=room)
            
            return jsonify({
                'success': True,
                'message': message,
                'version': version_data
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 400
            
    except Exception as e:
        logger.error(f"更新白板内容API错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }), 500

@whiteboard_bp.route('/<whiteboard_id>/versions', methods=['GET'])
def get_whiteboard_versions(whiteboard_id):
    """获取白板版本历史"""
    try:
        limit = request.args.get('limit', 10, type=int)
        
        success, message, versions = WhiteboardService.get_whiteboard_versions(
            whiteboard_id=whiteboard_id,
            limit=limit
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': message,
                'versions': versions
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 404
            
    except Exception as e:
        logger.error(f"获取白板版本API错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }), 500

@whiteboard_bp.route('/version/<version_id>', methods=['GET'])
def get_version_content(version_id):
    """获取指定版本的内容"""
    try:
        success, message, version_data = WhiteboardService.get_version_content(version_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': message,
                'version': version_data
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 404
            
    except Exception as e:
        logger.error(f"获取版本内容API错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }), 500

@whiteboard_bp.route('/list', methods=['GET'])
def list_whiteboards():
    """列出白板"""
    try:
        group_id = request.args.get('group_id')
        classroom_id = request.args.get('classroom_id')
        created_by = request.args.get('created_by')
        
        success, message, whiteboards = WhiteboardService.list_whiteboards(
            group_id=group_id,
            classroom_id=classroom_id,
            created_by=created_by
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': message,
                'whiteboards': whiteboards
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 400
            
    except Exception as e:
        logger.error(f"列出白板API错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }), 500

@whiteboard_bp.route('/<whiteboard_id>', methods=['DELETE'])
def delete_whiteboard(whiteboard_id):
    """删除白板"""
    try:
        data = request.get_json()
        deleted_by = data.get('deleted_by')
        
        if not deleted_by:
            return jsonify({
                'success': False,
                'message': '删除者不能为空'
            }), 400
        
        success, message = WhiteboardService.delete_whiteboard(
            whiteboard_id=whiteboard_id,
            deleted_by=deleted_by
        )
        
        if success:
            # 通过WebSocket通知白板已删除
            socketio = current_app.extensions.get('socketio')
            if socketio:
                room = f'whiteboard_{whiteboard_id}'
                socketio.emit('whiteboard_deleted', {
                    'whiteboard_id': whiteboard_id,
                    'deleted_by': deleted_by
                }, room=room)
            
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 400
            
    except Exception as e:
        logger.error(f"删除白板API错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }), 500

@whiteboard_bp.route('/version/<version_id>/thumbnail', methods=['POST'])
def save_thumbnail(version_id):
    """保存版本缩略图"""
    try:
        data = request.get_json()
        thumbnail_data = data.get('thumbnail')
        
        if not thumbnail_data:
            return jsonify({
                'success': False,
                'message': '缩略图数据不能为空'
            }), 400
        
        success, message = WhiteboardService.save_thumbnail(
            version_id=version_id,
            thumbnail_data=thumbnail_data
        )
        
        return jsonify({
            'success': success,
            'message': message
        })
        
    except Exception as e:
        logger.error(f"保存缩略图API错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }), 500

# WebSocket事件处理
def register_whiteboard_socket_events(socketio):
    """注册白板WebSocket事件处理器"""
    
    @socketio.on('join_whiteboard')
    def handle_join_whiteboard(data):
        """加入白板房间"""
        whiteboard_id = data.get('whiteboard_id')
        user_id = data.get('user_id')
        
        if whiteboard_id:
            room = f'whiteboard_{whiteboard_id}'
            join_room(room)
            
            # 通知其他用户有新用户加入
            emit('user_joined_whiteboard', {
                'whiteboard_id': whiteboard_id,
                'user_id': user_id
            }, room=room, include_self=False)
            
            logger.info(f"用户 {user_id} 加入白板房间: {room}")
    
    @socketio.on('leave_whiteboard')
    def handle_leave_whiteboard(data):
        """离开白板房间"""
        whiteboard_id = data.get('whiteboard_id')
        user_id = data.get('user_id')
        
        if whiteboard_id:
            room = f'whiteboard_{whiteboard_id}'
            leave_room(room)
            
            # 通知其他用户有用户离开
            emit('user_left_whiteboard', {
                'whiteboard_id': whiteboard_id,
                'user_id': user_id
            }, room=room, include_self=False)
            
            logger.info(f"用户 {user_id} 离开白板房间: {room}")
    
    @socketio.on('whiteboard_cursor_move')
    def handle_cursor_move(data):
        """处理光标移动"""
        whiteboard_id = data.get('whiteboard_id')
        user_id = data.get('user_id')
        cursor_position = data.get('cursor_position')
        
        if whiteboard_id and cursor_position:
            room = f'whiteboard_{whiteboard_id}'
            emit('cursor_moved', {
                'whiteboard_id': whiteboard_id,
                'user_id': user_id,
                'cursor_position': cursor_position
            }, room=room, include_self=False)
    
    @socketio.on('whiteboard_element_change')
    def handle_element_change(data):
        """处理元素变化（实时协作）"""
        whiteboard_id = data.get('whiteboard_id')
        user_id = data.get('user_id')
        element_data = data.get('element_data')
        change_type = data.get('change_type')  # add, update, delete
        
        if whiteboard_id and element_data:
            room = f'whiteboard_{whiteboard_id}'
            emit('element_changed', {
                'whiteboard_id': whiteboard_id,
                'user_id': user_id,
                'element_data': element_data,
                'change_type': change_type,
                'timestamp': data.get('timestamp')
            }, room=room, include_self=False)
    
    @socketio.on('whiteboard_selection_change')
    def handle_selection_change(data):
        """处理选择变化"""
        whiteboard_id = data.get('whiteboard_id')
        user_id = data.get('user_id')
        selected_elements = data.get('selected_elements', [])
        
        if whiteboard_id:
            room = f'whiteboard_{whiteboard_id}'
            emit('selection_changed', {
                'whiteboard_id': whiteboard_id,
                'user_id': user_id,
                'selected_elements': selected_elements
            }, room=room, include_self=False)