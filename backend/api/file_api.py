# -*- coding: utf-8 -*-
"""
文件管理API
"""

import os
import uuid
import json
import mimetypes
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app, send_from_directory
from werkzeug.utils import secure_filename
from models import db
from services.file_service import (
    FileVersionService, ChunkedUploadService, FilePreviewService,
    FilePermissionService, FileAuditService, FileTagService,
    FileBatchService, FileDistributionService
)

# 创建蓝图
file_api = Blueprint('file_api', __name__, url_prefix='/api/files')

# 文件存储配置
UPLOAD_FOLDER = 'instance/uploads'
ALLOWED_EXTENSIONS = {
    'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx',
    'jpg', 'jpeg', 'png', 'gif', 'mp4', 'avi', 'mp3', 'wav'
}

# 确保上传目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# 创建文件记录表
class FileRecord(db.Model):
    """文件记录模型"""
    __tablename__ = 'file_records'
    
    id = db.Column(db.String(36), primary_key=True)
    original_filename = db.Column(db.String(255), nullable=False)
    stored_filename = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(50))
    file_size = db.Column(db.Integer)
    upload_time = db.Column(db.DateTime, default=datetime.utcnow)
    modified_time = db.Column(db.DateTime, default=datetime.utcnow)
    uploader_id = db.Column(db.String(36))
    classroom_id = db.Column(db.String(36))
    description = db.Column(db.Text)
    file_hash = db.Column(db.String(32))  # MD5 hash
    mime_type = db.Column(db.String(100))
    is_public = db.Column(db.Boolean, default=False)
    download_count = db.Column(db.Integer, default=0)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'original_filename': self.original_filename,
            'file_type': self.file_type,
            'file_size': self.file_size,
            'upload_time': self.upload_time.isoformat(),
            'modified_time': self.modified_time.isoformat() if self.modified_time else None,
            'uploader_id': self.uploader_id,
            'classroom_id': self.classroom_id,
            'description': self.description,
            'file_hash': self.file_hash,
            'mime_type': self.mime_type,
            'is_public': self.is_public,
            'download_count': self.download_count,
            'download_url': f'/api/files/download/{self.id}'
        }


class FileVersion(db.Model):
    """文件版本模型"""
    __tablename__ = 'file_versions'
    
    id = db.Column(db.String(36), primary_key=True)
    file_id = db.Column(db.String(36), db.ForeignKey('file_records.id'), nullable=False)
    version_number = db.Column(db.Integer, nullable=False)
    file_hash = db.Column(db.String(32), nullable=False)
    file_size = db.Column(db.Integer, nullable=False)
    stored_path = db.Column(db.String(500), nullable=False)
    version_note = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.String(36))
    
    def to_dict(self):
        return {
            'id': self.id,
            'file_id': self.file_id,
            'version_number': self.version_number,
            'file_hash': self.file_hash,
            'file_size': self.file_size,
            'version_note': self.version_note,
            'created_at': self.created_at.isoformat(),
            'created_by': self.created_by
        }


class ChunkedUpload(db.Model):
    """分块上传会话模型"""
    __tablename__ = 'chunked_uploads'
    
    id = db.Column(db.String(36), primary_key=True)
    original_filename = db.Column(db.String(255), nullable=False)
    total_size = db.Column(db.BigInteger, nullable=False)
    total_chunks = db.Column(db.Integer, nullable=False)
    uploaded_chunks = db.Column(db.JSON)  # 已上传的块编号列表
    uploader_id = db.Column(db.String(36))
    classroom_id = db.Column(db.String(36))
    status = db.Column(db.String(20), default='initialized')  # initialized, uploading, completed, failed
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_chunk_time = db.Column(db.DateTime)
    
    def to_dict(self):
        return {
            'id': self.id,
            'original_filename': self.original_filename,
            'total_size': self.total_size,
            'total_chunks': self.total_chunks,
            'uploaded_chunks': len(self.uploaded_chunks or []),
            'uploader_id': self.uploader_id,
            'classroom_id': self.classroom_id,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'last_chunk_time': self.last_chunk_time.isoformat() if self.last_chunk_time else None
        }


class FilePermission(db.Model):
    """文件权限模型"""
    __tablename__ = 'file_permissions'
    
    id = db.Column(db.String(36), primary_key=True)
    file_id = db.Column(db.String(36), db.ForeignKey('file_records.id'), nullable=False)
    user_id = db.Column(db.String(36), nullable=False)
    can_read = db.Column(db.Boolean, default=True)
    can_write = db.Column(db.Boolean, default=False)
    can_delete = db.Column(db.Boolean, default=False)
    can_share = db.Column(db.Boolean, default=False)
    granted_by = db.Column(db.String(36))
    granted_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'file_id': self.file_id,
            'user_id': self.user_id,
            'can_read': self.can_read,
            'can_write': self.can_write,
            'can_delete': self.can_delete,
            'can_share': self.can_share,
            'granted_by': self.granted_by,
            'granted_at': self.granted_at.isoformat()
        }


class FileAccessLog(db.Model):
    """文件访问日志模型"""
    __tablename__ = 'file_access_logs'
    
    id = db.Column(db.String(36), primary_key=True)
    file_id = db.Column(db.String(36), db.ForeignKey('file_records.id'), nullable=False)
    user_id = db.Column(db.String(36), nullable=False)
    action = db.Column(db.String(50), nullable=False)  # view, download, edit, delete, share
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'file_id': self.file_id,
            'user_id': self.user_id,
            'action': self.action,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'timestamp': self.timestamp.isoformat()
        }


class FileTag(db.Model):
    """文件标签模型"""
    __tablename__ = 'file_tags'
    
    id = db.Column(db.String(36), primary_key=True)
    file_id = db.Column(db.String(36), db.ForeignKey('file_records.id'), nullable=False)
    tag_name = db.Column(db.String(50), nullable=False)
    category = db.Column(db.String(50))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'file_id': self.file_id,
            'tag_name': self.tag_name,
            'category': self.category,
            'created_at': self.created_at.isoformat()
        }


class BatchOperation(db.Model):
    """批量操作模型"""
    __tablename__ = 'batch_operations'
    
    id = db.Column(db.String(36), primary_key=True)
    operation_type = db.Column(db.String(50), nullable=False)  # delete, move, tag, permission, compress
    file_ids = db.Column(db.JSON, nullable=False)
    user_id = db.Column(db.String(36), nullable=False)
    parameters = db.Column(db.JSON)
    status = db.Column(db.String(20), default='pending')  # pending, running, completed, failed
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    result = db.Column(db.JSON)
    error_message = db.Column(db.Text)
    
    def to_dict(self):
        return {
            'id': self.id,
            'operation_type': self.operation_type,
            'file_count': len(json.loads(self.file_ids)) if self.file_ids else 0,
            'user_id': self.user_id,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'result': json.loads(self.result) if self.result else None,
            'error_message': self.error_message
        }


class FileDistribution(db.Model):
    """文件分发模型"""
    __tablename__ = 'file_distributions'
    
    id = db.Column(db.String(36), primary_key=True)
    file_ids = db.Column(db.JSON, nullable=False)
    target_type = db.Column(db.String(20), nullable=False)  # all, group, student
    target_ids = db.Column(db.JSON)
    distributor_id = db.Column(db.String(36), nullable=False)
    distribution_note = db.Column(db.Text)
    status = db.Column(db.String(20), default='pending')  # pending, distributing, completed, failed
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    result = db.Column(db.JSON)
    error_message = db.Column(db.Text)
    
    def to_dict(self):
        return {
            'id': self.id,
            'file_count': len(json.loads(self.file_ids)) if self.file_ids else 0,
            'target_type': self.target_type,
            'target_count': len(json.loads(self.target_ids)) if self.target_ids else 0,
            'distributor_id': self.distributor_id,
            'distribution_note': self.distribution_note,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'result': json.loads(self.result) if self.result else None,
            'error_message': self.error_message
        }


def allowed_file(filename):
    """检查文件类型是否允许上传"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


@file_api.route('/upload', methods=['POST'])
def upload_file():
    """上传文件"""
    # 检查是否有文件
    if 'file' not in request.files:
        return jsonify({
            'success': False,
            'message': '没有文件部分'
        }), 400
    
    file = request.files['file']
    
    # 检查文件名
    if file.filename == '':
        return jsonify({
            'success': False,
            'message': '没有选择文件'
        }), 400
    
    # 检查文件类型
    if not allowed_file(file.filename):
        return jsonify({
            'success': False,
            'message': '不支持的文件类型'
        }), 400
    
    # 安全处理文件名
    filename = secure_filename(file.filename)
    file_ext = filename.rsplit('.', 1)[1].lower()
    
    # 生成唯一文件名
    stored_filename = f"{uuid.uuid4().hex}.{file_ext}"
    file_path = os.path.join(UPLOAD_FOLDER, stored_filename)
    
    try:
        # 保存文件
        file.save(file_path)
        
        # 获取文件大小
        file_size = os.path.getsize(file_path)
        
        # 创建文件记录
        file_record = FileRecord(
            id=uuid.uuid4().hex,
            original_filename=filename,
            stored_filename=stored_filename,
            file_type=file_ext,
            file_size=file_size,
            uploader_id=request.form.get('uploader_id'),
            classroom_id=request.form.get('classroom_id'),
            description=request.form.get('description')
        )
        
        db.session.add(file_record)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '文件上传成功',
            'file': file_record.to_dict()
        })
    
    except Exception as e:
        # 删除已上传的文件
        if os.path.exists(file_path):
            os.remove(file_path)
        
        return jsonify({
            'success': False,
            'message': f'文件上传失败: {str(e)}'
        }), 500


@file_api.route('/download/<file_id>', methods=['GET'])
def download_file(file_id):
    """下载文件"""
    file_record = FileRecord.query.get(file_id)
    
    if not file_record:
        return jsonify({
            'success': False,
            'message': '文件不存在'
        }), 404
    
    try:
        return send_from_directory(
            UPLOAD_FOLDER,
            file_record.stored_filename,
            as_attachment=True,
            download_name=file_record.original_filename
        )
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'文件下载失败: {str(e)}'
        }), 500


@file_api.route('/list', methods=['GET'])
def list_files():
    """获取文件列表"""
    classroom_id = request.args.get('classroom_id')
    uploader_id = request.args.get('uploader_id')
    file_type = request.args.get('file_type')
    
    query = FileRecord.query
    
    if classroom_id:
        query = query.filter_by(classroom_id=classroom_id)
    
    if uploader_id:
        query = query.filter_by(uploader_id=uploader_id)
    
    if file_type:
        query = query.filter_by(file_type=file_type)
    
    files = query.order_by(FileRecord.upload_time.desc()).all()
    
    return jsonify({
        'success': True,
        'files': [file.to_dict() for file in files]
    })


@file_api.route('/<file_id>', methods=['GET'])
def get_file_info(file_id):
    """获取文件信息"""
    file_record = FileRecord.query.get(file_id)
    
    if not file_record:
        return jsonify({
            'success': False,
            'message': '文件不存在'
        }), 404
    
    return jsonify({
        'success': True,
        'file': file_record.to_dict()
    })


@file_api.route('/<file_id>', methods=['DELETE'])
def delete_file(file_id):
    """删除文件"""
    file_record = FileRecord.query.get(file_id)
    
    if not file_record:
        return jsonify({
            'success': False,
            'message': '文件不存在'
        }), 404
    
    try:
        # 删除物理文件
        file_path = os.path.join(UPLOAD_FOLDER, file_record.stored_filename)
        if os.path.exists(file_path):
            os.remove(file_path)
        
        # 删除数据库记录
        db.session.delete(file_record)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '文件删除成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'文件删除失败: {str(e)}'
        }), 500


@file_api.route('/distribute', methods=['POST'])
def distribute_file():
    """分发文件"""
    data = request.json
    
    if not data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据'
        }), 400
    
    required_fields = ['file_id', 'target_type']
    for field in required_fields:
        if field not in data:
            return jsonify({
                'success': False,
                'message': f'缺少必要字段: {field}'
            }), 400
    
    file_id = data['file_id']
    target_type = data['target_type']  # 'all', 'group', 'student'
    target_ids = data.get('target_ids', [])
    
    file_record = FileRecord.query.get(file_id)
    
    if not file_record:
        return jsonify({
            'success': False,
            'message': '文件不存在'
        }), 404
    
    # todo: 添加文件分发逻辑
    # 这里应该有实际的文件分发逻辑，例如通过WebSocket通知目标设备
    # 简化版本，只返回成功消息
    
    return jsonify({
        'success': True,
        'message': '文件分发成功',
        'distribution': {
            'file_id': file_id,
            'target_type': target_type,
            'target_ids': target_ids,
            'timestamp': datetime.utcnow().isoformat()
        }
    })


@file_api.route('/stats', methods=['GET'])
def get_file_stats():
    """获取文件统计信息"""
    classroom_id = request.args.get('classroom_id')
    
    query = FileRecord.query
    
    if classroom_id:
        query = query.filter_by(classroom_id=classroom_id)
    
    # 总文件数
    total_count = query.count()
    
    # 按类型统计
    type_stats = {}
    for file_record in query.all():
        file_type = file_record.file_type
        if file_type not in type_stats:
            type_stats[file_type] = 0
        type_stats[file_type] += 1
    
    # 总存储大小
    total_size = sum(file.file_size for file in query.all())
    
    return jsonify({
        'success': True,
        'stats': {
            'total_count': total_count,
            'total_size': total_size,
            'by_type': type_stats
        }
    })


# ==================== 文件版本管理 API ====================

@file_api.route('/<file_id>/versions', methods=['GET'])
def get_file_versions(file_id):
    """获取文件版本历史"""
    try:
        versions = version_service.get_file_versions(file_id)
        return jsonify({
            'success': True,
            'versions': versions
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取版本历史失败: {str(e)}'
        }), 500


@file_api.route('/<file_id>/versions/<int:version_number>/restore', methods=['POST'])
def restore_file_version(file_id, version_number):
    """恢复到指定版本"""
    try:
        success = version_service.restore_version(file_id, version_number)
        if success:
            return jsonify({
                'success': True,
                'message': f'已恢复到版本 {version_number}'
            })
        else:
            return jsonify({
                'success': False,
                'message': '版本恢复失败'
            }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'版本恢复失败: {str(e)}'
        }), 500


# ==================== 分块上传 API ====================

@file_api.route('/chunk/init', methods=['POST'])
def init_chunked_upload():
    """初始化分块上传"""
    data = request.json
    
    required_fields = ['filename', 'total_size', 'total_chunks']
    for field in required_fields:
        if field not in data:
            return jsonify({
                'success': False,
                'message': f'缺少必要字段: {field}'
            }), 400
    
    try:
        result = chunked_service.init_upload(
            filename=data['filename'],
            total_size=data['total_size'],
            total_chunks=data['total_chunks'],
            uploader_id=data.get('uploader_id'),
            classroom_id=data.get('classroom_id')
        )
        
        return jsonify({
            'success': True,
            'upload_session': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'初始化上传失败: {str(e)}'
        }), 500


@file_api.route('/chunk/<upload_id>', methods=['POST'])
def upload_chunk(upload_id):
    """上传文件块"""
    if 'chunk' not in request.files:
        return jsonify({
            'success': False,
            'message': '没有文件块'
        }), 400
    
    chunk_file = request.files['chunk']
    chunk_number = int(request.form.get('chunk_number', 0))
    
    try:
        result = chunked_service.upload_chunk(
            upload_id=upload_id,
            chunk_number=chunk_number,
            chunk_data=chunk_file.read()
        )
        
        return jsonify({
            'success': True,
            'chunk_info': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'上传文件块失败: {str(e)}'
        }), 500


@file_api.route('/chunk/<upload_id>/status', methods=['GET'])
def get_upload_status(upload_id):
    """获取上传状态"""
    try:
        status = chunked_service.get_upload_status(upload_id)
        if status:
            return jsonify({
                'success': True,
                'status': status
            })
        else:
            return jsonify({
                'success': False,
                'message': '上传会话不存在'
            }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取状态失败: {str(e)}'
        }), 500


# ==================== 文件预览 API ====================

@file_api.route('/<file_id>/preview', methods=['GET'])
def get_file_preview(file_id):
    """获取文件预览"""
    file_record = FileRecord.query.get(file_id)
    
    if not file_record:
        return jsonify({
            'success': False,
            'message': '文件不存在'
        }), 404
    
    try:
        preview_data = preview_service.generate_preview(file_record)
        return jsonify({
            'success': True,
            'preview': preview_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'生成预览失败: {str(e)}'
        }), 500


@file_api.route('/preview/<file_id>/<preview_type>', methods=['GET'])
def serve_preview_content(file_id, preview_type):
    """提供预览内容"""
    file_record = FileRecord.query.get(file_id)
    
    if not file_record:
        return jsonify({
            'success': False,
            'message': '文件不存在'
        }), 404
    
    # 这里应该根据preview_type返回相应的预览内容
    # 简化实现，返回原文件
    try:
        return send_from_directory(
            UPLOAD_FOLDER,
            file_record.stored_filename
        )
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取预览内容失败: {str(e)}'
        }), 500


# ==================== 文件权限管理 API ====================

@file_api.route('/<file_id>/permissions', methods=['GET'])
def get_file_permissions(file_id):
    """获取文件权限"""
    try:
        permissions = permission_service.get_file_permissions(file_id)
        return jsonify({
            'success': True,
            'permissions': permissions
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取权限失败: {str(e)}'
        }), 500


@file_api.route('/<file_id>/permissions', methods=['POST'])
def set_file_permissions(file_id):
    """设置文件权限"""
    data = request.json
    
    if not data or 'user_id' not in data or 'permissions' not in data:
        return jsonify({
            'success': False,
            'message': '无效的权限数据'
        }), 400
    
    try:
        success = permission_service.set_file_permissions(
            file_id=file_id,
            user_id=data['user_id'],
            permissions=data['permissions']
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': '权限设置成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '权限设置失败'
            }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'权限设置失败: {str(e)}'
        }), 500


@file_api.route('/<file_id>/check-permission', methods=['POST'])
def check_file_permission(file_id):
    """检查文件权限"""
    data = request.json
    
    if not data or 'user_id' not in data or 'action' not in data:
        return jsonify({
            'success': False,
            'message': '缺少必要参数'
        }), 400
    
    try:
        has_permission = permission_service.check_permission(
            file_id=file_id,
            user_id=data['user_id'],
            action=data['action']
        )
        
        return jsonify({
            'success': True,
            'has_permission': has_permission
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'权限检查失败: {str(e)}'
        }), 500


# ==================== 文件访问审计 API ====================

@file_api.route('/<file_id>/access-logs', methods=['GET'])
def get_file_access_logs(file_id):
    """获取文件访问日志"""
    try:
        limit = int(request.args.get('limit', 100))
        logs = audit_service.get_access_logs(file_id=file_id, limit=limit)
        
        return jsonify({
            'success': True,
            'logs': logs
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取访问日志失败: {str(e)}'
        }), 500


@file_api.route('/<file_id>/access-stats', methods=['GET'])
def get_file_access_stats(file_id):
    """获取文件访问统计"""
    try:
        stats = audit_service.get_access_statistics(file_id)
        return jsonify({
            'success': True,
            'statistics': stats
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取访问统计失败: {str(e)}'
        }), 500


# ==================== 文件标签管理 API ====================

@file_api.route('/<file_id>/tags', methods=['GET'])
def get_file_tags(file_id):
    """获取文件标签"""
    try:
        tags = tag_service.get_file_tags(file_id)
        return jsonify({
            'success': True,
            'tags': tags
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取标签失败: {str(e)}'
        }), 500


@file_api.route('/<file_id>/tags', methods=['POST'])
def add_file_tag(file_id):
    """添加文件标签"""
    data = request.json
    
    if not data or 'tag_name' not in data:
        return jsonify({
            'success': False,
            'message': '缺少标签名称'
        }), 400
    
    try:
        success = tag_service.add_tag_to_file(
            file_id=file_id,
            tag_name=data['tag_name'],
            category=data.get('category')
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': '标签添加成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '标签已存在'
            }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'添加标签失败: {str(e)}'
        }), 500


@file_api.route('/<file_id>/tags/<tag_name>', methods=['DELETE'])
def remove_file_tag(file_id, tag_name):
    """移除文件标签"""
    try:
        success = tag_service.remove_tag_from_file(file_id, tag_name)
        
        if success:
            return jsonify({
                'success': True,
                'message': '标签移除成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '标签不存在'
            }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'移除标签失败: {str(e)}'
        }), 500


@file_api.route('/search-by-tags', methods=['POST'])
def search_files_by_tags():
    """根据标签搜索文件"""
    data = request.json
    
    if not data or 'tags' not in data:
        return jsonify({
            'success': False,
            'message': '缺少搜索标签'
        }), 400
    
    try:
        files = tag_service.search_files_by_tags(
            tags=data['tags'],
            classroom_id=data.get('classroom_id')
        )
        
        return jsonify({
            'success': True,
            'files': files
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'搜索失败: {str(e)}'
        }), 500


@file_api.route('/<file_id>/auto-classify', methods=['POST'])
def auto_classify_file(file_id):
    """自动分类文件"""
    file_record = FileRecord.query.get(file_id)
    
    if not file_record:
        return jsonify({
            'success': False,
            'message': '文件不存在'
        }), 404
    
    try:
        suggested_tags = tag_service.auto_classify_file(file_record)
        return jsonify({
            'success': True,
            'suggested_tags': suggested_tags
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'自动分类失败: {str(e)}'
        }), 500


@file_api.route('/popular-tags', methods=['GET'])
def get_popular_tags():
    """获取热门标签"""
    try:
        classroom_id = request.args.get('classroom_id')
        limit = int(request.args.get('limit', 20))
        
        tags = tag_service.get_popular_tags(classroom_id=classroom_id, limit=limit)
        return jsonify({
            'success': True,
            'tags': tags
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取热门标签失败: {str(e)}'
        }), 500


# ==================== 批量操作 API ====================

@file_api.route('/batch/delete', methods=['POST'])
def batch_delete_files():
    """批量删除文件"""
    data = request.json
    
    if not data or 'file_ids' not in data:
        return jsonify({
            'success': False,
            'message': '缺少文件ID列表'
        }), 400
    
    try:
        batch_id = batch_service.create_batch_operation(
            operation_type='delete',
            file_ids=data['file_ids'],
            user_id=data.get('user_id', 'system')
        )
        
        # 异步执行批量操作
        result = batch_service.execute_batch_operation(batch_id)
        
        return jsonify({
            'success': True,
            'batch_id': batch_id,
            'result': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'批量删除失败: {str(e)}'
        }), 500


@file_api.route('/batch/tag', methods=['POST'])
def batch_tag_files():
    """批量添加标签"""
    data = request.json
    
    required_fields = ['file_ids', 'tags']
    for field in required_fields:
        if field not in data:
            return jsonify({
                'success': False,
                'message': f'缺少必要字段: {field}'
            }), 400
    
    try:
        batch_id = batch_service.create_batch_operation(
            operation_type='tag',
            file_ids=data['file_ids'],
            user_id=data.get('user_id', 'system'),
            parameters={'tags': data['tags']}
        )
        
        result = batch_service.execute_batch_operation(batch_id)
        
        return jsonify({
            'success': True,
            'batch_id': batch_id,
            'result': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'批量标签失败: {str(e)}'
        }), 500


@file_api.route('/batch/compress', methods=['POST'])
def batch_compress_files():
    """批量压缩文件"""
    data = request.json
    
    required_fields = ['file_ids', 'archive_name']
    for field in required_fields:
        if field not in data:
            return jsonify({
                'success': False,
                'message': f'缺少必要字段: {field}'
            }), 400
    
    try:
        batch_id = batch_service.create_batch_operation(
            operation_type='compress',
            file_ids=data['file_ids'],
            user_id=data.get('user_id', 'system'),
            parameters={'archive_name': data['archive_name']}
        )
        
        result = batch_service.execute_batch_operation(batch_id)
        
        return jsonify({
            'success': True,
            'batch_id': batch_id,
            'result': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'批量压缩失败: {str(e)}'
        }), 500


@file_api.route('/batch/<batch_id>/status', methods=['GET'])
def get_batch_status(batch_id):
    """获取批量操作状态"""
    try:
        status = batch_service.get_batch_status(batch_id)
        if status:
            return jsonify({
                'success': True,
                'status': status
            })
        else:
            return jsonify({
                'success': False,
                'message': '批量操作不存在'
            }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取状态失败: {str(e)}'
        }), 500


# ==================== 增强文件分发 API ====================

@file_api.route('/distribute/advanced', methods=['POST'])
def advanced_distribute_files():
    """高级文件分发"""
    data = request.json
    
    required_fields = ['file_ids', 'target_type', 'distributor_id']
    for field in required_fields:
        if field not in data:
            return jsonify({
                'success': False,
                'message': f'缺少必要字段: {field}'
            }), 400
    
    try:
        distribution_id = distribution_service.create_distribution(
            file_ids=data['file_ids'],
            target_type=data['target_type'],
            target_ids=data.get('target_ids', []),
            distributor_id=data['distributor_id'],
            distribution_note=data.get('distribution_note')
        )
        
        # 异步执行分发
        result = distribution_service.execute_distribution(distribution_id)
        
        return jsonify({
            'success': True,
            'distribution_id': distribution_id,
            'result': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'文件分发失败: {str(e)}'
        }), 500


@file_api.route('/distribute/<distribution_id>/status', methods=['GET'])
def get_distribution_status(distribution_id):
    """获取分发状态"""
    try:
        status = distribution_service.get_distribution_status(distribution_id)
        if status:
            return jsonify({
                'success': True,
                'status': status
            })
        else:
            return jsonify({
                'success': False,
                'message': '分发任务不存在'
            }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取分发状态失败: {str(e)}'
        }), 500


@file_api.route('/distribute/history', methods=['GET'])
def get_distribution_history():
    """获取分发历史"""
    try:
        distributor_id = request.args.get('distributor_id')
        limit = int(request.args.get('limit', 50))
        
        history = distribution_service.get_distribution_history(
            distributor_id=distributor_id,
            limit=limit
        )
        
        return jsonify({
            'success': True,
            'history': history
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取分发历史失败: {str(e)}'
        }), 500
# 初始化服务实例
version_service = FileVersionService()
chunked_service = ChunkedUploadService()
preview_service = FilePreviewService()
permission_service = FilePermissionService()
audit_service = FileAuditService()
tag_service = FileTagService()
batch_service = FileBatchService()
distribution_service = FileDistributionService()