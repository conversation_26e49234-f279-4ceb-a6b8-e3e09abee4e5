# -*- coding: utf-8 -*-
"""
课堂管理API接口
"""

from flask import Blueprint, request, jsonify
from services import ClassroomService
from models import Classroom

classroom_bp = Blueprint('classroom', __name__, url_prefix='/api/classroom')


@classroom_bp.route('/create', methods=['POST'])
def create_classroom():
    """创建课堂"""
    data = request.get_json()
    
    required_fields = ['teacher_id', 'teacher_name', 'name']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'缺少必需字段: {field}'}), 400
    
    try:
        classroom = ClassroomService.create_classroom(
            teacher_id=data['teacher_id'],
            teacher_name=data['teacher_name'],
            name=data['name'],
            description=data.get('description'),
            duration=data.get('duration')
        )
        
        return jsonify({
            'success': True,
            'message': '课堂创建成功',
            'data': classroom.to_dict()
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@classroom_bp.route('/start', methods=['POST'])
def start_classroom():
    """开始课堂"""
    data = request.get_json()
    
    if 'classroom_id' not in data or 'teacher_id' not in data:
        return jsonify({'error': '缺少必需字段'}), 400
    
    try:
        classroom, message = ClassroomService.start_classroom(
            data['classroom_id'], data['teacher_id']
        )
        
        if classroom:
            return jsonify({
                'success': True,
                'message': message,
                'data': classroom.to_dict()
            })
        else:
            return jsonify({'error': message}), 400
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@classroom_bp.route('/join', methods=['POST'])
def join_classroom():
    """学生加入课堂"""
    data = request.get_json()
    
    required_fields = ['access_code', 'student_id', 'student_name']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'缺少必需字段: {field}'}), 400
    
    try:
        student, message = ClassroomService.join_classroom(
            access_code=data['access_code'],
            student_id=data['student_id'],
            student_name=data['student_name'],
            device_info=data.get('device_info')
        )
        
        if student:
            return jsonify({
                'success': True,
                'message': message,
                'data': student.to_dict()
            })
        else:
            return jsonify({'error': message}), 400
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@classroom_bp.route('/status/<classroom_id>')
def get_classroom_status(classroom_id):
    """获取课堂状态"""
    try:
        status = ClassroomService.get_classroom_status(classroom_id)
        
        if status:
            return jsonify({
                'success': True,
                'data': status
            })
        else:
            return jsonify({'error': '课堂不存在'}), 404
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@classroom_bp.route('/groups/create', methods=['POST'])
def create_groups():
    """创建小组"""
    data = request.get_json()
    
    if 'classroom_id' not in data or 'group_count' not in data:
        return jsonify({'error': '缺少必需字段'}), 400
    
    try:
        groups, message = ClassroomService.create_groups(
            classroom_id=data['classroom_id'],
            group_count=data['group_count'],
            group_prefix=data.get('group_prefix', '小组')
        )
        
        if groups:
            return jsonify({
                'success': True,
                'message': message,
                'data': [group.to_dict() for group in groups]
            })
        else:
            return jsonify({'error': message}), 400
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@classroom_bp.route('/groups/assign', methods=['POST'])
def assign_student_to_group():
    """分配学生到小组"""
    data = request.get_json()
    
    if 'student_id' not in data or 'group_id' not in data:
        return jsonify({'error': '缺少必需字段'}), 400
    
    try:
        success, message = ClassroomService.assign_student_to_group(
            data['student_id'], data['group_id']
        )
        
        return jsonify({
            'success': success,
            'message': message
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@classroom_bp.route('/groups/auto-assign', methods=['POST'])
def auto_assign_groups():
    """自动分组"""
    data = request.get_json()
    
    if 'classroom_id' not in data:
        return jsonify({'error': '缺少必需字段'}), 400
    
    try:
        success, message = ClassroomService.auto_assign_groups(
            classroom_id=data['classroom_id'],
            strategy=data.get('strategy', 'random')
        )
        
        return jsonify({
            'success': success,
            'message': message
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@classroom_bp.route('/list/<teacher_id>')
def list_teacher_classrooms(teacher_id):
    """获取教师的课堂列表"""
    try:
        classrooms = Classroom.get_teacher_classrooms(teacher_id)
        
        return jsonify({
            'success': True,
            'data': [classroom.to_dict() for classroom in classrooms]
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500