# -*- coding: utf-8 -*-
"""
小组管理API
"""

from flask import Blueprint, request, jsonify
from models import Group, Student
from services.classroom_service import ClassroomService

# 创建蓝图
group_api = Blueprint('group_api', __name__, url_prefix='/api/groups')


@group_api.route('/', methods=['GET'])
def get_groups():
    """获取小组列表"""
    classroom_id = request.args.get('classroom_id')
    
    if not classroom_id:
        return jsonify({
            'success': False,
            'message': '缺少课堂ID参数'
        }), 400
    
    groups = Group.query.filter_by(classroom_id=classroom_id).all()
    
    return jsonify({
        'success': True,
        'groups': [group.to_dict() for group in groups]
    })


@group_api.route('/<group_id>', methods=['GET'])
def get_group_details(group_id):
    """获取小组详情"""
    group = Group.query.get(group_id)
    
    if not group:
        return jsonify({
            'success': False,
            'message': '小组不存在'
        }), 404
    
    # 获取小组成员
    students = Student.query.filter_by(group_id=group_id).all()
    
    return jsonify({
        'success': True,
        'group': group.to_dict(),
        'members': [student.to_dict() for student in students]
    })


@group_api.route('/', methods=['POST'])
def create_group():
    """创建小组"""
    data = request.json
    
    if not data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据'
        }), 400
    
    required_fields = ['classroom_id', 'name']
    for field in required_fields:
        if field not in data:
            return jsonify({
                'success': False,
                'message': f'缺少必要字段: {field}'
            }), 400
    
    try:
        group = Group.create(
            classroom_id=data['classroom_id'],
            name=data['name'],
            device_id=data.get('device_id')
        )
        
        return jsonify({
            'success': True,
            'message': '小组创建成功',
            'group': group.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'创建小组失败: {str(e)}'
        }), 500


@group_api.route('/<group_id>', methods=['PUT'])
def update_group(group_id):
    """更新小组信息"""
    group = Group.query.get(group_id)
    
    if not group:
        return jsonify({
            'success': False,
            'message': '小组不存在'
        }), 404
    
    data = request.json
    
    if not data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据'
        }), 400
    
    try:
        if 'name' in data:
            group.name = data['name']
        
        if 'device_id' in data:
            group.device_id = data['device_id']
        
        group.save()
        
        return jsonify({
            'success': True,
            'message': '小组信息更新成功',
            'group': group.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新小组信息失败: {str(e)}'
        }), 500


@group_api.route('/<group_id>/members', methods=['POST'])
def add_member(group_id):
    """添加小组成员"""
    group = Group.query.get(group_id)
    
    if not group:
        return jsonify({
            'success': False,
            'message': '小组不存在'
        }), 404
    
    data = request.json
    
    if not data or 'student_id' not in data:
        return jsonify({
            'success': False,
            'message': '缺少学生ID'
        }), 400
    
    try:
        success, message = ClassroomService.assign_student_to_group(
            data['student_id'], group_id
        )
        
        return jsonify({
            'success': success,
            'message': message
        }), 200 if success else 400
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'添加小组成员失败: {str(e)}'
        }), 500


@group_api.route('/<group_id>/members/<student_id>', methods=['DELETE'])
def remove_member(group_id, student_id):
    """移除小组成员"""
    student = Student.query.get(student_id)
    
    if not student:
        return jsonify({
            'success': False,
            'message': '学生不存在'
        }), 404
    
    if student.group_id != group_id:
        return jsonify({
            'success': False,
            'message': '该学生不在指定小组中'
        }), 400
    
    try:
        student.group_id = None
        student.save()
        
        return jsonify({
            'success': True,
            'message': '已从小组中移除学生'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'移除小组成员失败: {str(e)}'
        }), 500


@group_api.route('/<group_id>', methods=['DELETE'])
def delete_group(group_id):
    """删除小组"""
    group = Group.query.get(group_id)
    
    if not group:
        return jsonify({
            'success': False,
            'message': '小组不存在'
        }), 404
    
    try:
        # 将小组成员的group_id设为None
        students = Student.query.filter_by(group_id=group_id).all()
        for student in students:
            student.group_id = None
            student.save()
        
        # 删除小组
        group.delete()
        
        return jsonify({
            'success': True,
            'message': '小组删除成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'删除小组失败: {str(e)}'
        }), 500


@group_api.route('/random-assign', methods=['POST'])
def random_assign():
    """随机分组"""
    data = request.json
    
    if not data or 'classroom_id' not in data:
        return jsonify({
            'success': False,
            'message': '缺少课堂ID'
        }), 400
    
    try:
        success, message = ClassroomService.auto_assign_groups(
            classroom_id=data['classroom_id'],
            strategy='random'
        )
        
        return jsonify({
            'success': success,
            'message': message
        }), 200 if success else 400
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'随机分组失败: {str(e)}'
        }), 500