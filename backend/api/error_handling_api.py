#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 错误处理和异常恢复API
"""

from flask import Blueprint, request, jsonify
from flask_socketio import emit
import logging
from datetime import datetime

from services.error_handler import get_error_handler, ErrorType, ErrorSeverity
from services.network_monitor import get_network_monitor, NetworkEndpoint, ConnectionStatus
from services.backup_service import get_backup_service, BackupType
from services.system_monitor import get_system_monitor, MonitorRule, MonitorType, AlertLevel
from services.user_guidance import get_user_guidance_service, UserRole, GuidanceType

# 创建蓝图
error_handling_bp = Blueprint('error_handling', __name__, url_prefix='/api/error-handling')

logger = logging.getLogger('smart_classroom.error_handling_api')

# 获取服务实例
error_handler = get_error_handler()
network_monitor = get_network_monitor()
backup_service = get_backup_service()
system_monitor = get_system_monitor()
user_guidance = get_user_guidance_service()

@error_handling_bp.route('/errors/statistics', methods=['GET'])
def get_error_statistics():
    """获取错误统计"""
    try:
        stats = error_handler.get_error_statistics()
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取错误统计失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取错误统计失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/errors/active', methods=['GET'])
def get_active_errors():
    """获取活动错误"""
    try:
        active_errors = error_handler.get_active_errors()
        return jsonify({
            'success': True,
            'data': active_errors
        })
    except Exception as e:
        logger.error(f"获取活动错误失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取活动错误失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/errors/<error_id>/resolve', methods=['POST'])
def resolve_error(error_id):
    """解决错误"""
    try:
        data = request.get_json() or {}
        resolution_note = data.get('resolution_note', '手动解决')
        
        success = error_handler.resolve_error(error_id, resolution_note)
        
        if success:
            return jsonify({
                'success': True,
                'message': '错误已解决'
            })
        else:
            return jsonify({
                'success': False,
                'message': '错误不存在或已解决'
            }), 404
    except Exception as e:
        logger.error(f"解决错误失败: {e}")
        return jsonify({
            'success': False,
            'message': '解决错误失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/network/endpoints', methods=['GET'])
def get_network_endpoints():
    """获取网络端点状态"""
    try:
        endpoints = network_monitor.get_all_endpoints_status()
        return jsonify({
            'success': True,
            'data': endpoints
        })
    except Exception as e:
        logger.error(f"获取网络端点失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取网络端点失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/network/endpoints', methods=['POST'])
def add_network_endpoint():
    """添加网络监控端点"""
    try:
        data = request.get_json()
        
        endpoint = NetworkEndpoint(
            endpoint_id=data['endpoint_id'],
            host=data['host'],
            port=data['port'],
            protocol=data['protocol'],
            description=data['description'],
            check_interval=data.get('check_interval', 60),
            timeout=data.get('timeout', 5),
            auto_reconnect=data.get('auto_reconnect', True)
        )
        
        network_monitor.add_endpoint(endpoint)
        
        return jsonify({
            'success': True,
            'message': '网络端点已添加'
        })
    except Exception as e:
        logger.error(f"添加网络端点失败: {e}")
        return jsonify({
            'success': False,
            'message': '添加网络端点失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/network/endpoints/<endpoint_id>', methods=['DELETE'])
def remove_network_endpoint(endpoint_id):
    """移除网络监控端点"""
    try:
        network_monitor.remove_endpoint(endpoint_id)
        return jsonify({
            'success': True,
            'message': '网络端点已移除'
        })
    except Exception as e:
        logger.error(f"移除网络端点失败: {e}")
        return jsonify({
            'success': False,
            'message': '移除网络端点失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/network/endpoints/<endpoint_id>/reconnect', methods=['POST'])
def force_reconnect_endpoint(endpoint_id):
    """强制重连端点"""
    try:
        success = network_monitor.force_reconnect(endpoint_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': '重连请求已发送'
            })
        else:
            return jsonify({
                'success': False,
                'message': '端点不存在'
            }), 404
    except Exception as e:
        logger.error(f"强制重连失败: {e}")
        return jsonify({
            'success': False,
            'message': '强制重连失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/network/statistics', methods=['GET'])
def get_network_statistics():
    """获取网络统计"""
    try:
        stats = network_monitor.get_network_statistics()
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取网络统计失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取网络统计失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/network/ping', methods=['POST'])
def ping_host():
    """Ping主机"""
    try:
        data = request.get_json()
        host = data['host']
        count = data.get('count', 4)
        
        result = network_monitor.ping_host(host, count)
        
        return jsonify({
            'success': True,
            'data': result
        })
    except Exception as e:
        logger.error(f"Ping主机失败: {e}")
        return jsonify({
            'success': False,
            'message': 'Ping主机失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/backup/tasks', methods=['GET'])
def get_backup_tasks():
    """获取备份任务"""
    try:
        tasks = backup_service.get_all_tasks()
        return jsonify({
            'success': True,
            'data': tasks
        })
    except Exception as e:
        logger.error(f"获取备份任务失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取备份任务失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/backup/tasks', methods=['POST'])
def create_backup_task():
    """创建备份任务"""
    try:
        data = request.get_json()
        
        task = backup_service.create_backup_task(
            task_id=data['task_id'],
            backup_type=BackupType(data['backup_type']),
            source_paths=data['source_paths'],
            description=data['description'],
            compression=data.get('compression', True)
        )
        
        return jsonify({
            'success': True,
            'message': '备份任务已创建',
            'data': {
                'task_id': task.task_id,
                'status': task.status.value
            }
        })
    except Exception as e:
        logger.error(f"创建备份任务失败: {e}")
        return jsonify({
            'success': False,
            'message': '创建备份任务失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/backup/tasks/<task_id>/status', methods=['GET'])
def get_backup_task_status(task_id):
    """获取备份任务状态"""
    try:
        status = backup_service.get_task_status(task_id)
        
        if status:
            return jsonify({
                'success': True,
                'data': status
            })
        else:
            return jsonify({
                'success': False,
                'message': '任务不存在'
            }), 404
    except Exception as e:
        logger.error(f"获取备份任务状态失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取备份任务状态失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/backup/tasks/<task_id>/cancel', methods=['POST'])
def cancel_backup_task(task_id):
    """取消备份任务"""
    try:
        success = backup_service.cancel_task(task_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': '任务已取消'
            })
        else:
            return jsonify({
                'success': False,
                'message': '任务不存在或无法取消'
            }), 404
    except Exception as e:
        logger.error(f"取消备份任务失败: {e}")
        return jsonify({
            'success': False,
            'message': '取消备份任务失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/backup/database', methods=['POST'])
def backup_database():
    """备份数据库"""
    try:
        data = request.get_json()
        db_path = data['db_path']
        backup_name = data.get('backup_name')
        
        backup_path = backup_service.backup_database(db_path, backup_name)
        
        return jsonify({
            'success': True,
            'message': '数据库备份完成',
            'data': {
                'backup_path': backup_path
            }
        })
    except Exception as e:
        logger.error(f"数据库备份失败: {e}")
        return jsonify({
            'success': False,
            'message': '数据库备份失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/backup/list', methods=['GET'])
def get_backup_list():
    """获取备份列表"""
    try:
        backups = backup_service.get_backup_list()
        return jsonify({
            'success': True,
            'data': backups
        })
    except Exception as e:
        logger.error(f"获取备份列表失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取备份列表失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/backup/statistics', methods=['GET'])
def get_backup_statistics():
    """获取备份统计"""
    try:
        stats = backup_service.get_backup_statistics()
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取备份统计失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取备份统计失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/system/status', methods=['GET'])
def get_system_status():
    """获取系统状态"""
    try:
        status = system_monitor.get_system_status()
        return jsonify({
            'success': True,
            'data': status
        })
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取系统状态失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/system/metrics', methods=['GET'])
def get_system_metrics():
    """获取系统指标"""
    try:
        metrics = system_monitor.get_current_metrics()
        return jsonify({
            'success': True,
            'data': metrics
        })
    except Exception as e:
        logger.error(f"获取系统指标失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取系统指标失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/system/metrics/history', methods=['GET'])
def get_metrics_history():
    """获取指标历史"""
    try:
        hours = request.args.get('hours', 1, type=int)
        history = system_monitor.get_metrics_history(hours)
        return jsonify({
            'success': True,
            'data': history
        })
    except Exception as e:
        logger.error(f"获取指标历史失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取指标历史失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/system/alerts', methods=['GET'])
def get_system_alerts():
    """获取系统告警"""
    try:
        active_only = request.args.get('active_only', 'false').lower() == 'true'
        
        if active_only:
            alerts = system_monitor.get_active_alerts()
        else:
            hours = request.args.get('hours', 24, type=int)
            alerts = system_monitor.get_alert_history(hours)
        
        return jsonify({
            'success': True,
            'data': alerts
        })
    except Exception as e:
        logger.error(f"获取系统告警失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取系统告警失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/system/alerts/<alert_id>/acknowledge', methods=['POST'])
def acknowledge_alert(alert_id):
    """确认告警"""
    try:
        data = request.get_json() or {}
        user_id = data.get('user_id')
        
        success = system_monitor.acknowledge_alert(alert_id, user_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': '告警已确认'
            })
        else:
            return jsonify({
                'success': False,
                'message': '告警不存在'
            }), 404
    except Exception as e:
        logger.error(f"确认告警失败: {e}")
        return jsonify({
            'success': False,
            'message': '确认告警失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/system/monitor-rules', methods=['GET'])
def get_monitor_rules():
    """获取监控规则"""
    try:
        rules = system_monitor.get_monitor_rules()
        return jsonify({
            'success': True,
            'data': rules
        })
    except Exception as e:
        logger.error(f"获取监控规则失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取监控规则失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/system/monitor-rules/<rule_id>/enable', methods=['POST'])
def enable_monitor_rule(rule_id):
    """启用监控规则"""
    try:
        system_monitor.enable_rule(rule_id)
        return jsonify({
            'success': True,
            'message': '监控规则已启用'
        })
    except Exception as e:
        logger.error(f"启用监控规则失败: {e}")
        return jsonify({
            'success': False,
            'message': '启用监控规则失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/system/monitor-rules/<rule_id>/disable', methods=['POST'])
def disable_monitor_rule(rule_id):
    """禁用监控规则"""
    try:
        system_monitor.disable_rule(rule_id)
        return jsonify({
            'success': True,
            'message': '监控规则已禁用'
        })
    except Exception as e:
        logger.error(f"禁用监控规则失败: {e}")
        return jsonify({
            'success': False,
            'message': '禁用监控规则失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/guidance/error/<error_key>', methods=['GET'])
def get_error_guidance(error_key):
    """获取错误指导"""
    try:
        user_role = UserRole(request.args.get('user_role', 'teacher'))
        
        # 获取用户友好的错误信息
        error_info = user_guidance.get_user_friendly_error(error_key, user_role)
        
        # 获取相关指导
        guidance_list = user_guidance.get_error_guidance(error_key, user_role)
        
        return jsonify({
            'success': True,
            'data': {
                'error_info': error_info,
                'guidance': guidance_list
            }
        })
    except Exception as e:
        logger.error(f"获取错误指导失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取错误指导失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/guidance/search', methods=['GET'])
def search_guidance():
    """搜索指导"""
    try:
        keyword = request.args.get('keyword', '')
        user_role = UserRole(request.args.get('user_role', 'teacher'))
        
        results = user_guidance.search_guidance(keyword, user_role)
        
        return jsonify({
            'success': True,
            'data': results
        })
    except Exception as e:
        logger.error(f"搜索指导失败: {e}")
        return jsonify({
            'success': False,
            'message': '搜索指导失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/guidance/faq', methods=['GET'])
def get_faq():
    """获取FAQ"""
    try:
        category = request.args.get('category')
        keyword = request.args.get('keyword')
        
        faq_list = user_guidance.get_faq(category, keyword)
        
        return jsonify({
            'success': True,
            'data': faq_list
        })
    except Exception as e:
        logger.error(f"获取FAQ失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取FAQ失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/guidance/contextual-help', methods=['GET'])
def get_contextual_help():
    """获取上下文帮助"""
    try:
        context = request.args.get('context', '')
        user_role = UserRole(request.args.get('user_role', 'teacher'))
        
        help_content = user_guidance.get_contextual_help(context, user_role)
        
        return jsonify({
            'success': True,
            'data': help_content
        })
    except Exception as e:
        logger.error(f"获取上下文帮助失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取上下文帮助失败',
            'error': str(e)
        }), 500

@error_handling_bp.route('/test/run', methods=['POST'])
def run_error_handling_tests():
    """运行错误处理测试"""
    try:
        # 导入测试模块
        from tests.test_error_handling import run_error_handling_tests
        
        # 运行测试
        test_results = run_error_handling_tests()
        
        return jsonify({
            'success': True,
            'data': test_results
        })
    except Exception as e:
        logger.error(f"运行测试失败: {e}")
        return jsonify({
            'success': False,
            'message': '运行测试失败',
            'error': str(e)
        }), 500

# WebSocket事件处理
def register_error_handling_websocket_events(socketio):
    """注册错误处理WebSocket事件"""
    
    @socketio.on('subscribe_error_updates')
    def handle_subscribe_error_updates():
        """订阅错误更新"""
        # 客户端可以订阅错误更新
        emit('subscribed', {'message': '已订阅错误更新'})
    
    @socketio.on('subscribe_system_status')
    def handle_subscribe_system_status():
        """订阅系统状态更新"""
        emit('subscribed', {'message': '已订阅系统状态更新'})
    
    # 定期发送系统状态更新
    def send_system_status_update():
        """发送系统状态更新"""
        try:
            status = system_monitor.get_system_status()
            socketio.emit('system_status_update', status)
        except Exception as e:
            logger.error(f"发送系统状态更新失败: {e}")
    
    # 发送错误告警
    def send_error_alert(alert):
        """发送错误告警"""
        try:
            socketio.emit('error_alert', {
                'alert_id': alert.alert_id,
                'level': alert.alert_level.value,
                'message': alert.message,
                'timestamp': alert.timestamp.isoformat()
            })
        except Exception as e:
            logger.error(f"发送错误告警失败: {e}")
    
    # 注册告警回调
    system_monitor.register_alert_callback(send_error_alert)
    
    # 启动定期状态更新（每30秒）
    import threading
    def status_update_loop():
        while True:
            send_system_status_update()
            threading.Event().wait(30)
    
    status_thread = threading.Thread(target=status_update_loop, daemon=True)
    status_thread.start()

# 启动服务
def initialize_error_handling_services():
    """初始化错误处理服务"""
    try:
        # 启动网络监控
        if not network_monitor.running:
            network_monitor.start_monitoring()
        
        # 启动备份调度器
        if not backup_service.running:
            backup_service.start_scheduler()
        
        # 启动系统监控
        if not system_monitor.running:
            system_monitor.start_monitoring()
        
        logger.info("错误处理服务已初始化")
        
    except Exception as e:
        logger.error(f"初始化错误处理服务失败: {e}")
        raise