# -*- coding: utf-8 -*-
"""
设备管理API
"""

from flask import Blueprint, request, jsonify
from services.device_service import DeviceService
from services.device_discovery import get_discovery_service

# 创建蓝图
device_api = Blueprint('device_api', __name__)


@device_api.route('/', methods=['GET'])
def get_devices():
    """获取设备列表"""
    device_type = request.args.get('type')
    classroom_id = request.args.get('classroom_id')
    
    if classroom_id:
        devices = DeviceService.get_classroom_devices(classroom_id)
        return jsonify({
            'success': True,
            'devices': devices
        })
    
    if device_type:
        devices = DeviceService.get_devices_by_type(device_type)
        return jsonify({
            'success': True,
            'devices': [device.to_dict() for device in devices]
        })
    
    # 默认返回所有设备
    from models import Device
    devices = Device.query.all()
    return jsonify({
        'success': True,
        'devices': [device.to_dict() for device in devices]
    })


@device_api.route('/discover', methods=['POST'])
def discover_devices():
    """发现设备"""
    discovery_service = get_discovery_service()

    # 确保服务已启动
    if not discovery_service.running:
        discovery_service.start()

    # 发送广播
    discovery_service.broadcast_discovery()

    # 返回已发现的设备
    devices = discovery_service.get_discovered_devices()

    return jsonify({
        'success': True,
        'message': '已发送设备发现广播',
        'devices': devices
    })


@device_api.route('/discovered', methods=['GET'])
def get_discovered_devices():
    """获取已发现的设备列表"""
    discovery_service = get_discovery_service()
    devices = discovery_service.get_discovered_devices()

    return jsonify({
        'success': True,
        'devices': devices
    })


@device_api.route('/register', methods=['POST'])
def register_device():
    """注册设备"""
    data = request.json
    
    if not data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据'
        }), 400
    
    required_fields = ['device_id', 'device_type', 'device_name', 'ip_address']
    for field in required_fields:
        if field not in data:
            return jsonify({
                'success': False,
                'message': f'缺少必要字段: {field}'
            }), 400
    
    device = DeviceService.register_device(
        device_id=data['device_id'],
        device_type=data['device_type'],
        device_name=data['device_name'],
        ip_address=data['ip_address'],
        port=data.get('port', 8080),
        capabilities=data.get('capabilities', []),
        screen_resolution=data.get('screen_resolution'),
        os_info=data.get('os_info')
    )
    
    return jsonify({
        'success': True,
        'message': '设备注册成功',
        'device': device.to_dict()
    })


@device_api.route('/<device_id>/connect', methods=['POST'])
def connect_device(device_id):
    """连接设备到课堂"""
    data = request.json

    if not data or 'classroom_id' not in data:
        return jsonify({
            'success': False,
            'message': '缺少课堂ID'
        }), 400

    success, message = DeviceService.connect_device_to_classroom(
        device_id=device_id,
        classroom_id=data['classroom_id']
    )

    return jsonify({
        'success': success,
        'message': message
    }), 200 if success else 400


@device_api.route('/<device_id>/disconnect', methods=['POST'])
def disconnect_device(device_id):
    """断开设备连接"""
    success, message = DeviceService.disconnect_device(device_id)

    return jsonify({
        'success': success,
        'message': message
    }), 200 if success else 400


@device_api.route('/<device_id>/heartbeat', methods=['POST'])
def update_heartbeat(device_id):
    """更新设备心跳"""
    success = DeviceService.update_device_heartbeat(device_id)

    return jsonify({
        'success': success,
        'message': '心跳更新成功' if success else '设备不存在'
    }), 200 if success else 404


@device_api.route('/<device_id>', methods=['GET'])
def get_device_status(device_id):
    """获取设备状态"""
    status = DeviceService.get_device_status(device_id)

    if not status:
        return jsonify({
            'success': False,
            'message': '设备不存在'
        }), 404

    return jsonify({
        'success': True,
        'status': status
    })


@device_api.route('/statistics', methods=['GET'])
def get_device_statistics():
    """获取设备统计信息"""
    statistics = DeviceService.get_device_statistics()
    
    return jsonify({
        'success': True,
        'statistics': statistics
    })