# -*- coding: utf-8 -*-
"""
互动答题API
"""

import uuid
from datetime import datetime
from flask import Blueprint, request, jsonify, Response
from models import db, Question, Student, Classroom
from services.question_service import QuestionService

# 创建蓝图
question_api = Blueprint('question_api', __name__, url_prefix='/api/questions')


@question_api.route('/', methods=['POST'])
def create_question():
    """创建题目"""
    data = request.json
    
    if not data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据'
        }), 400
    
    required_fields = ['classroom_id', 'question_type', 'content']
    for field in required_fields:
        if field not in data:
            return jsonify({
                'success': False,
                'message': f'缺少必要字段: {field}'
            }), 400
    
    try:
        question = QuestionService.create_question(
            classroom_id=data['classroom_id'],
            question_type=data['question_type'],
            content=data['content'],
            options=data.get('options', []),
            correct_answer=data.get('correct_answer'),
            points=data.get('points', 1)
        )
        
        return jsonify({
            'success': True,
            'message': '题目创建成功',
            'question': question.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'创建题目失败: {str(e)}'
        }), 500


@question_api.route('/batch', methods=['POST'])
def create_batch_questions():
    """批量创建题目"""
    data = request.json
    
    if not data or 'questions' not in data or 'classroom_id' not in data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据'
        }), 400
    
    classroom_id = data['classroom_id']
    questions_data = data['questions']
    
    if not isinstance(questions_data, list) or len(questions_data) == 0:
        return jsonify({
            'success': False,
            'message': '题目列表为空或格式错误'
        }), 400
    
    try:
        created_questions = []
        
        for q_data in questions_data:
            q_data['classroom_id'] = classroom_id
            
            question = QuestionService.create_question(
                classroom_id=classroom_id,
                question_type=q_data.get('question_type'),
                content=q_data.get('content'),
                options=q_data.get('options', []),
                correct_answer=q_data.get('correct_answer'),
                points=q_data.get('points', 1)
            )
            
            created_questions.append(question.to_dict())
        
        return jsonify({
            'success': True,
            'message': f'成功创建{len(created_questions)}个题目',
            'questions': created_questions
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'批量创建题目失败: {str(e)}'
        }), 500


@question_api.route('/<question_id>', methods=['GET'])
def get_question(question_id):
    """获取题目详情"""
    question = Question.query.get(question_id)
    
    if not question:
        return jsonify({
            'success': False,
            'message': '题目不存在'
        }), 404
    
    return jsonify({
        'success': True,
        'question': question.to_dict()
    })


@question_api.route('/classroom/<classroom_id>', methods=['GET'])
def get_classroom_questions(classroom_id):
    """获取课堂题目列表"""
    questions = Question.query.filter_by(classroom_id=classroom_id).all()
    
    return jsonify({
        'success': True,
        'questions': [question.to_dict() for question in questions]
    })


@question_api.route('/<question_id>/publish', methods=['POST'])
def publish_question(question_id):
    """发布题目"""
    question = Question.query.get(question_id)
    
    if not question:
        return jsonify({
            'success': False,
            'message': '题目不存在'
        }), 404
    
    data = request.json or {}
    target_groups = data.get('target_groups', [])  # 目标小组ID列表，空列表表示全部
    
    try:
        success, message = QuestionService.publish_question(
            question_id=question_id,
            target_groups=target_groups
        )
        
        return jsonify({
            'success': success,
            'message': message
        }), 200 if success else 400
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'发布题目失败: {str(e)}'
        }), 500


@question_api.route('/<question_id>/answer', methods=['POST'])
def submit_answer(question_id):
    """提交答案"""
    question = Question.query.get(question_id)
    
    if not question:
        return jsonify({
            'success': False,
            'message': '题目不存在'
        }), 404
    
    data = request.json
    
    if not data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据'
        }), 400
    
    required_fields = ['student_id', 'answer']
    for field in required_fields:
        if field not in data:
            return jsonify({
                'success': False,
                'message': f'缺少必要字段: {field}'
            }), 400
    
    student_id = data['student_id']
    answer_content = data['answer']
    
    # 验证学生是否存在
    student = Student.query.get(student_id)
    if not student:
        return jsonify({
            'success': False,
            'message': '学生不存在'
        }), 404
    
    try:
        answer, is_correct = QuestionService.submit_answer(
            question_id=question_id,
            student_id=student_id,
            answer_content=answer_content
        )
        
        return jsonify({
            'success': True,
            'message': '答案提交成功',
            'answer': {
                'id': answer.id,
                'is_correct': is_correct
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'提交答案失败: {str(e)}'
        }), 500


@question_api.route('/<question_id>/results', methods=['GET'])
def get_question_results(question_id):
    """获取题目答题结果"""
    question = Question.query.get(question_id)
    
    if not question:
        return jsonify({
            'success': False,
            'message': '题目不存在'
        }), 404
    
    group_by = request.args.get('group_by', 'all')  # 'all', 'group'
    
    try:
        results = QuestionService.get_question_results(
            question_id=question_id,
            group_by=group_by
        )
        
        return jsonify({
            'success': True,
            'results': results
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取答题结果失败: {str(e)}'
        }), 500


@question_api.route('/<question_id>', methods=['DELETE'])
def delete_question(question_id):
    """删除题目"""
    question = Question.query.get(question_id)
    
    if not question:
        return jsonify({
            'success': False,
            'message': '题目不存在'
        }), 404
    
    try:
        # 删除相关答案
        QuestionService.delete_question_answers(question_id)
        
        # 删除题目
        db.session.delete(question)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '题目删除成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'删除题目失败: {str(e)}'
        }), 500


@question_api.route('/compare-groups', methods=['GET'])
def compare_group_results():
    """比较小组答题结果"""
    classroom_id = request.args.get('classroom_id')
    question_id = request.args.get('question_id')
    
    if not classroom_id:
        return jsonify({
            'success': False,
            'message': '缺少课堂ID'
        }), 400
    
    try:
        if question_id:
            # 比较单个题目的小组结果
            comparison = QuestionService.compare_group_results_for_question(question_id)
        else:
            # 比较整个课堂的小组结果
            comparison = QuestionService.compare_group_results_for_classroom(classroom_id)
        
        return jsonify({
            'success': True,
            'comparison': comparison
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'比较小组结果失败: {str(e)}'
        }), 500


@question_api.route('/<question_id>/export', methods=['GET'])
def export_question_results(question_id):
    """导出题目结果"""
    question = Question.query.get(question_id)
    
    if not question:
        return jsonify({
            'success': False,
            'message': '题目不存在'
        }), 404
    
    format_type = request.args.get('format', 'csv')
    
    try:
        result, message = QuestionService.export_question_results(question_id, format_type)
        
        if not result:
            return jsonify({
                'success': False,
                'message': message
            }), 400
        
        if format_type == 'csv':
            # 返回CSV文件下载
            filename = f"question_{question_id}_results_{datetime.now().strftime('%Y%m%d%H%M%S')}.csv"
            return Response(
                result,
                mimetype="text/csv",
                headers={"Content-disposition": f"attachment; filename={filename}"}
            )
        else:
            # 返回JSON数据
            return jsonify({
                'success': True,
                'data': result
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导出题目结果失败: {str(e)}'
        }), 500


@question_api.route('/', methods=['PUT'])
def update_question():
    """更新题目"""
    data = request.json
    
    if not data or 'question_id' not in data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据'
        }), 400
    
    question_id = data['question_id']
    question = Question.query.get(question_id)
    
    if not question:
        return jsonify({
            'success': False,
            'message': '题目不存在'
        }), 404
    
    # 只允许更新草稿状态的题目
    if question.status != 'draft':
        return jsonify({
            'success': False,
            'message': '只能更新草稿状态的题目'
        }), 400
    
    try:
        # 更新题目字段
        if 'title' in data:
            question.title = data['title']
        if 'content' in data:
            question.content = data['content']
        if 'question_type' in data:
            question.question_type = data['question_type']
        if 'options' in data:
            question.options = data['options']
        if 'correct_answer' in data:
            question.correct_answer = data['correct_answer']
        if 'time_limit' in data:
            question.time_limit = data['time_limit']
        if 'target_type' in data:
            question.target_type = data['target_type']
        if 'target_groups' in data:
            question.target_groups = data['target_groups']
        if 'target_students' in data:
            question.target_students = data['target_students']
        if 'allow_multiple_attempts' in data:
            question.allow_multiple_attempts = data['allow_multiple_attempts']
        if 'show_correct_answer' in data:
            question.show_correct_answer = data['show_correct_answer']
        if 'randomize_options' in data:
            question.randomize_options = data['randomize_options']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '题目更新成功',
            'question': question.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新题目失败: {str(e)}'
        }), 500


@question_api.route('/<question_id>/close', methods=['POST'])
def close_question(question_id):
    """关闭题目"""
    question = Question.query.get(question_id)
    
    if not question:
        return jsonify({
            'success': False,
            'message': '题目不存在'
        }), 404
    
    data = request.json or {}
    creator_id = data.get('creator_id')
    
    try:
        question, message = QuestionService.close_question(question_id, creator_id)
        
        if not question:
            return jsonify({
                'success': False,
                'message': message
            }), 400
        
        return jsonify({
            'success': True,
            'message': message,
            'question': question.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'关闭题目失败: {str(e)}'
        }), 500


@question_api.route('/statistics/<classroom_id>', methods=['GET'])
def get_question_statistics(classroom_id):
    """获取课堂题目统计"""
    try:
        statistics = QuestionService.get_question_statistics(classroom_id)
        
        if not statistics:
            return jsonify({
                'success': False,
                'message': '课堂不存在'
            }), 404
        
        return jsonify({
            'success': True,
            'statistics': statistics
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取题目统计失败: {str(e)}'
        }), 500