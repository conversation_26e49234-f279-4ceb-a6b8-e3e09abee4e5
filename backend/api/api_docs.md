# 智慧课堂系统 API 文档

## 目录

1. [认证 API](#认证-api)
2. [课堂管理 API](#课堂管理-api)
3. [设备管理 API](#设备管理-api)
4. [小组管理 API](#小组管理-api)
5. [文件管理 API](#文件管理-api)
6. [互动答题 API](#互动答题-api)

## 认证 API

### 用户登录

- **URL**: `/api/auth/login`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "user_id": "teacher1",
    "password": "password123"
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "登录成功",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "teacher1",
      "role": "teacher",
      "name": "张老师"
    }
  }
  ```

### 用户登出

- **URL**: `/api/auth/logout`
- **方法**: `POST`
- **请求头**: `Authorization: Bearer <token>`
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "登出成功"
  }
  ```

### 验证令牌

- **URL**: `/api/auth/verify`
- **方法**: `GET`
- **请求头**: `Authorization: Bearer <token>`
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "令牌有效",
    "user": {
      "id": "teacher1",
      "role": "teacher"
    }
  }
  ```

### 获取用户资料

- **URL**: `/api/auth/profile`
- **方法**: `GET`
- **请求头**: `Authorization: Bearer <token>`
- **成功响应**:
  ```json
  {
    "success": true,
    "user": {
      "id": "teacher1",
      "role": "teacher",
      "name": "张老师"
    }
  }
  ```

### 修改密码

- **URL**: `/api/auth/change-password`
- **方法**: `POST`
- **请求头**: `Authorization: Bearer <token>`
- **请求体**:
  ```json
  {
    "old_password": "password123",
    "new_password": "newpassword123"
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "密码修改成功"
  }
  ```

## 课堂管理 API

### 创建课堂

- **URL**: `/api/classroom/create`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "teacher_id": "teacher1",
    "teacher_name": "张老师",
    "name": "高等数学",
    "description": "微积分基础课程",
    "duration": 90
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "课堂创建成功",
    "data": {
      "id": "classroom_id",
      "name": "高等数学",
      "teacher_id": "teacher1",
      "teacher_name": "张老师",
      "status": "created",
      "access_code": "123456",
      "created_at": "2023-01-01T12:00:00Z"
    }
  }
  ```

### 开始课堂

- **URL**: `/api/classroom/start`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "classroom_id": "classroom_id",
    "teacher_id": "teacher1"
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "课堂已开始",
    "data": {
      "id": "classroom_id",
      "status": "active",
      "started_at": "2023-01-01T12:05:00Z"
    }
  }
  ```

### 学生加入课堂

- **URL**: `/api/classroom/join`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "access_code": "123456",
    "student_id": "student1",
    "student_name": "李同学",
    "device_info": {
      "device_id": "device123",
      "device_type": "tablet"
    }
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "成功加入课堂",
    "data": {
      "id": "student_id",
      "name": "李同学",
      "classroom_id": "classroom_id",
      "joined_at": "2023-01-01T12:10:00Z"
    }
  }
  ```

### 获取课堂状态

- **URL**: `/api/classroom/status/<classroom_id>`
- **方法**: `GET`
- **成功响应**:
  ```json
  {
    "success": true,
    "data": {
      "id": "classroom_id",
      "name": "高等数学",
      "status": "active",
      "student_count": 25,
      "started_at": "2023-01-01T12:05:00Z",
      "duration": 90
    }
  }
  ```

### 创建小组

- **URL**: `/api/classroom/groups/create`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "classroom_id": "classroom_id",
    "group_count": 5,
    "group_prefix": "小组"
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "成功创建5个小组",
    "data": [
      {
        "id": "group1",
        "name": "小组1",
        "classroom_id": "classroom_id"
      },
      {
        "id": "group2",
        "name": "小组2",
        "classroom_id": "classroom_id"
      }
    ]
  }
  ```

### 分配学生到小组

- **URL**: `/api/classroom/groups/assign`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "student_id": "student1",
    "group_id": "group1"
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "学生已分配到小组"
  }
  ```

### 自动分组

- **URL**: `/api/classroom/groups/auto-assign`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "classroom_id": "classroom_id",
    "strategy": "random"
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "自动分组完成"
  }
  ```

### 获取教师的课堂列表

- **URL**: `/api/classroom/list/<teacher_id>`
- **方法**: `GET`
- **成功响应**:
  ```json
  {
    "success": true,
    "data": [
      {
        "id": "classroom1",
        "name": "高等数学",
        "status": "active",
        "created_at": "2023-01-01T12:00:00Z"
      },
      {
        "id": "classroom2",
        "name": "大学物理",
        "status": "ended",
        "created_at": "2023-01-02T09:00:00Z"
      }
    ]
  }
  ```

## 设备管理 API

### 获取设备列表

- **URL**: `/api/devices`
- **方法**: `GET`
- **查询参数**:
  - `type`: 设备类型（可选）
  - `classroom_id`: 课堂ID（可选）
- **成功响应**:
  ```json
  {
    "success": true,
    "devices": [
      {
        "id": "device1",
        "device_type": "tablet",
        "device_name": "iPad Pro",
        "ip_address": "*************",
        "status": "online"
      }
    ]
  }
  ```

### 发现设备

- **URL**: `/api/devices/discover`
- **方法**: `POST`
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "已发送设备发现广播",
    "devices": [
      {
        "device_id": "device1",
        "device_type": "tablet",
        "ip_address": "*************"
      }
    ]
  }
  ```

### 获取已发现的设备列表

- **URL**: `/api/devices/discovered`
- **方法**: `GET`
- **成功响应**:
  ```json
  {
    "success": true,
    "devices": [
      {
        "device_id": "device1",
        "device_type": "tablet",
        "ip_address": "*************"
      }
    ]
  }
  ```

### 注册设备

- **URL**: `/api/devices/register`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "device_id": "device1",
    "device_type": "tablet",
    "device_name": "iPad Pro",
    "ip_address": "*************",
    "port": 8080,
    "capabilities": ["touch", "camera"],
    "screen_resolution": "2048x1536",
    "os_info": "iOS 15.0"
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "设备注册成功",
    "device": {
      "id": "device1",
      "device_type": "tablet",
      "device_name": "iPad Pro",
      "ip_address": "*************"
    }
  }
  ```

### 连接设备到课堂

- **URL**: `/api/devices/<device_id>/connect`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "classroom_id": "classroom1"
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "设备已连接到课堂"
  }
  ```

### 断开设备连接

- **URL**: `/api/devices/<device_id>/disconnect`
- **方法**: `POST`
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "设备已断开连接"
  }
  ```

### 更新设备心跳

- **URL**: `/api/devices/<device_id>/heartbeat`
- **方法**: `POST`
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "心跳更新成功"
  }
  ```

### 获取设备状态

- **URL**: `/api/devices/<device_id>`
- **方法**: `GET`
- **成功响应**:
  ```json
  {
    "success": true,
    "status": {
      "device_id": "device1",
      "online": true,
      "last_heartbeat": "2023-01-01T12:30:00Z",
      "connected_classroom": "classroom1"
    }
  }
  ```

### 获取设备统计信息

- **URL**: `/api/devices/statistics`
- **方法**: `GET`
- **成功响应**:
  ```json
  {
    "success": true,
    "statistics": {
      "total": 10,
      "online": 8,
      "by_type": {
        "tablet": 5,
        "laptop": 3,
        "desktop": 2
      }
    }
  }
  ```

## 小组管理 API

### 获取小组列表

- **URL**: `/api/groups/`
- **方法**: `GET`
- **查询参数**:
  - `classroom_id`: 课堂ID
- **成功响应**:
  ```json
  {
    "success": true,
    "groups": [
      {
        "id": "group1",
        "name": "小组1",
        "classroom_id": "classroom1",
        "device_id": "device1"
      }
    ]
  }
  ```

### 获取小组详情

- **URL**: `/api/groups/<group_id>`
- **方法**: `GET`
- **成功响应**:
  ```json
  {
    "success": true,
    "group": {
      "id": "group1",
      "name": "小组1",
      "classroom_id": "classroom1",
      "device_id": "device1"
    },
    "members": [
      {
        "id": "student1",
        "name": "李同学",
        "joined_at": "2023-01-01T12:10:00Z"
      }
    ]
  }
  ```

### 创建小组

- **URL**: `/api/groups/`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "classroom_id": "classroom1",
    "name": "小组1",
    "device_id": "device1"
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "小组创建成功",
    "group": {
      "id": "group1",
      "name": "小组1",
      "classroom_id": "classroom1",
      "device_id": "device1"
    }
  }
  ```

### 更新小组信息

- **URL**: `/api/groups/<group_id>`
- **方法**: `PUT`
- **请求体**:
  ```json
  {
    "name": "新小组名称",
    "device_id": "device2"
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "小组信息更新成功",
    "group": {
      "id": "group1",
      "name": "新小组名称",
      "classroom_id": "classroom1",
      "device_id": "device2"
    }
  }
  ```

### 添加小组成员

- **URL**: `/api/groups/<group_id>/members`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "student_id": "student1"
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "学生已添加到小组"
  }
  ```

### 移除小组成员

- **URL**: `/api/groups/<group_id>/members/<student_id>`
- **方法**: `DELETE`
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "已从小组中移除学生"
  }
  ```

### 删除小组

- **URL**: `/api/groups/<group_id>`
- **方法**: `DELETE`
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "小组删除成功"
  }
  ```

### 随机分组

- **URL**: `/api/groups/random-assign`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "classroom_id": "classroom1"
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "随机分组完成"
  }
  ```

## 文件管理 API

### 上传文件

- **URL**: `/api/files/upload`
- **方法**: `POST`
- **请求体**: `multipart/form-data`
  - `file`: 文件数据
  - `uploader_id`: 上传者ID
  - `classroom_id`: 课堂ID
  - `description`: 文件描述（可选）
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "文件上传成功",
    "file": {
      "id": "file1",
      "original_filename": "lecture.pdf",
      "file_type": "pdf",
      "file_size": 1024000,
      "upload_time": "2023-01-01T13:00:00Z",
      "uploader_id": "teacher1",
      "classroom_id": "classroom1",
      "download_url": "/api/files/download/file1"
    }
  }
  ```

### 下载文件

- **URL**: `/api/files/download/<file_id>`
- **方法**: `GET`
- **响应**: 文件内容

### 获取文件列表

- **URL**: `/api/files/list`
- **方法**: `GET`
- **查询参数**:
  - `classroom_id`: 课堂ID（可选）
  - `uploader_id`: 上传者ID（可选）
  - `file_type`: 文件类型（可选）
- **成功响应**:
  ```json
  {
    "success": true,
    "files": [
      {
        "id": "file1",
        "original_filename": "lecture.pdf",
        "file_type": "pdf",
        "file_size": 1024000,
        "upload_time": "2023-01-01T13:00:00Z",
        "uploader_id": "teacher1",
        "classroom_id": "classroom1",
        "download_url": "/api/files/download/file1"
      }
    ]
  }
  ```

### 获取文件信息

- **URL**: `/api/files/<file_id>`
- **方法**: `GET`
- **成功响应**:
  ```json
  {
    "success": true,
    "file": {
      "id": "file1",
      "original_filename": "lecture.pdf",
      "file_type": "pdf",
      "file_size": 1024000,
      "upload_time": "2023-01-01T13:00:00Z",
      "uploader_id": "teacher1",
      "classroom_id": "classroom1",
      "download_url": "/api/files/download/file1"
    }
  }
  ```

### 删除文件

- **URL**: `/api/files/<file_id>`
- **方法**: `DELETE`
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "文件删除成功"
  }
  ```

### 分发文件

- **URL**: `/api/files/distribute`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "file_id": "file1",
    "target_type": "group",
    "target_ids": ["group1", "group2"]
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "文件分发成功",
    "distribution": {
      "file_id": "file1",
      "target_type": "group",
      "target_ids": ["group1", "group2"],
      "timestamp": "2023-01-01T13:05:00Z"
    }
  }
  ```

### 获取文件统计信息

- **URL**: `/api/files/stats`
- **方法**: `GET`
- **查询参数**:
  - `classroom_id`: 课堂ID（可选）
- **成功响应**:
  ```json
  {
    "success": true,
    "stats": {
      "total_count": 10,
      "total_size": 5242880,
      "by_type": {
        "pdf": 5,
        "pptx": 3,
        "docx": 2
      }
    }
  }
  ```

## 互动答题 API

### 创建题目

- **URL**: `/api/questions/`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "classroom_id": "classroom1",
    "question_type": "single_choice",
    "content": "1+1=?",
    "options": ["1", "2", "3", "4"],
    "correct_answer": "2",
    "points": 1
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "题目创建成功",
    "question": {
      "id": "question1",
      "classroom_id": "classroom1",
      "question_type": "single_choice",
      "content": "1+1=?",
      "options": ["1", "2", "3", "4"],
      "points": 1
    }
  }
  ```

### 批量创建题目

- **URL**: `/api/questions/batch`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "classroom_id": "classroom1",
    "questions": [
      {
        "question_type": "single_choice",
        "content": "1+1=?",
        "options": ["1", "2", "3", "4"],
        "correct_answer": "2",
        "points": 1
      },
      {
        "question_type": "true_false",
        "content": "地球是圆的",
        "correct_answer": "true",
        "points": 1
      }
    ]
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "成功创建2个题目",
    "questions": [
      {
        "id": "question1",
        "question_type": "single_choice",
        "content": "1+1=?"
      },
      {
        "id": "question2",
        "question_type": "true_false",
        "content": "地球是圆的"
      }
    ]
  }
  ```

### 获取题目详情

- **URL**: `/api/questions/<question_id>`
- **方法**: `GET`
- **成功响应**:
  ```json
  {
    "success": true,
    "question": {
      "id": "question1",
      "classroom_id": "classroom1",
      "question_type": "single_choice",
      "content": "1+1=?",
      "options": ["1", "2", "3", "4"],
      "points": 1
    }
  }
  ```

### 获取课堂题目列表

- **URL**: `/api/questions/classroom/<classroom_id>`
- **方法**: `GET`
- **成功响应**:
  ```json
  {
    "success": true,
    "questions": [
      {
        "id": "question1",
        "question_type": "single_choice",
        "content": "1+1=?"
      },
      {
        "id": "question2",
        "question_type": "true_false",
        "content": "地球是圆的"
      }
    ]
  }
  ```

### 发布题目

- **URL**: `/api/questions/<question_id>/publish`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "target_groups": ["group1", "group2"]
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "题目已发布"
  }
  ```

### 提交答案

- **URL**: `/api/questions/<question_id>/answer`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "student_id": "student1",
    "answer": "2"
  }
  ```
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "答案提交成功",
    "answer": {
      "id": "answer1",
      "is_correct": true
    }
  }
  ```

### 获取题目答题结果

- **URL**: `/api/questions/<question_id>/results`
- **方法**: `GET`
- **查询参数**:
  - `group_by`: 分组方式（可选，默认为"all"）
- **成功响应**:
  ```json
  {
    "success": true,
    "results": {
      "total_answers": 25,
      "correct_answers": 20,
      "accuracy_rate": 0.8,
      "options_distribution": {
        "1": 2,
        "2": 20,
        "3": 1,
        "4": 2
      }
    }
  }
  ```

### 删除题目

- **URL**: `/api/questions/<question_id>`
- **方法**: `DELETE`
- **成功响应**:
  ```json
  {
    "success": true,
    "message": "题目删除成功"
  }
  ```

### 比较小组答题结果

- **URL**: `/api/questions/compare-groups`
- **方法**: `GET`
- **查询参数**:
  - `classroom_id`: 课堂ID
  - `question_id`: 题目ID（可选）
- **成功响应**:
  ```json
  {
    "success": true,
    "comparison": {
      "group1": {
        "total_questions": 5,
        "correct_answers": 4,
        "accuracy_rate": 0.8
      },
      "group2": {
        "total_questions": 5,
        "correct_answers": 3,
        "accuracy_rate": 0.6
      }
    }
  }
  ```