# -*- coding: utf-8 -*-
"""
安全管理API
"""

import datetime
from flask import Blueprint, request, jsonify, g
from services.security_service import get_security_service
from models.security import User, Permission, UserRole, DeviceAccess, AuditLog
from .auth_api import token_required, permission_required, role_required

# 创建蓝图
security_api = Blueprint('security_api', __name__, url_prefix='/api/security')

# 获取安全服务
security_service = get_security_service()


@security_api.route('/users', methods=['GET'])
@token_required
@permission_required(Permission.USER_MANAGEMENT)
def get_users():
    """获取用户列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    role_filter = request.args.get('role')
    search = request.args.get('search')
    
    query = security_service.session.query(User)
    
    if role_filter:
        query = query.filter(User.role == role_filter)
    
    if search:
        query = query.filter(
            User.username.contains(search) | 
            User.name.contains(search) |
            User.email.contains(search)
        )
    
    # 分页
    total = query.count()
    users = query.offset((page - 1) * per_page).limit(per_page).all()
    
    users_data = []
    for user in users:
        users_data.append({
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'name': user.name,
            'role': user.role,
            'department': user.department,
            'is_active': user.is_active,
            'is_verified': user.is_verified,
            'last_login': user.last_login.isoformat() if user.last_login else None,
            'created_at': user.created_at.isoformat(),
            'is_locked': user.is_locked()
        })
    
    return jsonify({
        'success': True,
        'users': users_data,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': total,
            'pages': (total + per_page - 1) // per_page
        }
    })


@security_api.route('/users', methods=['POST'])
@token_required
@permission_required(Permission.USER_MANAGEMENT)
def create_user():
    """创建用户"""
    data = request.json
    
    if not data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据',
            'error_code': 'INVALID_REQUEST'
        }), 400
    
    required_fields = ['username', 'password', 'name', 'role']
    for field in required_fields:
        if not data.get(field):
            return jsonify({
                'success': False,
                'message': f'缺少必填字段: {field}',
                'error_code': 'MISSING_FIELD'
            }), 400
    
    # 检查用户名是否已存在
    existing_user = security_service.session.query(User).filter_by(
        username=data['username']
    ).first()
    
    if existing_user:
        return jsonify({
            'success': False,
            'message': '用户名已存在',
            'error_code': 'USERNAME_EXISTS'
        }), 409
    
    # 验证密码策略
    password_validation = security_service.validate_password_policy(data['password'])
    if not password_validation['valid']:
        return jsonify({
            'success': False,
            'message': '密码不符合安全策略',
            'errors': password_validation['errors'],
            'error_code': 'INVALID_PASSWORD_POLICY'
        }), 400
    
    # 创建用户
    try:
        user = User(
            username=data['username'],
            email=data.get('email'),
            name=data['name'],
            role=data['role'],
            phone=data.get('phone'),
            department=data.get('department'),
            is_active=data.get('is_active', True),
            is_verified=data.get('is_verified', False)
        )
        
        user.set_password(data['password'])
        
        security_service.session.add(user)
        security_service.session.commit()
        
        security_service._log_audit(
            'user_created', 
            user_id=g.current_user.id,
            resource_type='user',
            resource_id=str(user.id),
            details={'created_user': user.username}
        )
        
        return jsonify({
            'success': True,
            'message': '用户创建成功',
            'user_id': user.id
        }), 201
        
    except Exception as e:
        security_service.session.rollback()
        return jsonify({
            'success': False,
            'message': '用户创建失败',
            'error_code': 'USER_CREATION_FAILED'
        }), 500


@security_api.route('/users/<int:user_id>', methods=['PUT'])
@token_required
@permission_required(Permission.USER_MANAGEMENT)
def update_user(user_id):
    """更新用户信息"""
    user = security_service.session.query(User).get(user_id)
    
    if not user:
        return jsonify({
            'success': False,
            'message': '用户不存在',
            'error_code': 'USER_NOT_FOUND'
        }), 404
    
    data = request.json
    if not data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据',
            'error_code': 'INVALID_REQUEST'
        }), 400
    
    try:
        # 更新用户信息
        if 'email' in data:
            user.email = data['email']
        if 'name' in data:
            user.name = data['name']
        if 'phone' in data:
            user.phone = data['phone']
        if 'department' in data:
            user.department = data['department']
        if 'is_active' in data:
            user.is_active = data['is_active']
        if 'is_verified' in data:
            user.is_verified = data['is_verified']
        if 'role' in data and g.current_user.role == UserRole.ADMIN.value:
            user.role = data['role']
        
        security_service.session.commit()
        
        security_service._log_audit(
            'user_updated',
            user_id=g.current_user.id,
            resource_type='user',
            resource_id=str(user_id),
            details={'updated_fields': list(data.keys())}
        )
        
        return jsonify({
            'success': True,
            'message': '用户信息更新成功'
        })
        
    except Exception as e:
        security_service.session.rollback()
        return jsonify({
            'success': False,
            'message': '用户信息更新失败',
            'error_code': 'USER_UPDATE_FAILED'
        }), 500


@security_api.route('/users/<int:user_id>/lock', methods=['POST'])
@token_required
@permission_required(Permission.USER_MANAGEMENT)
def lock_user(user_id):
    """锁定用户账户"""
    user = security_service.session.query(User).get(user_id)
    
    if not user:
        return jsonify({
            'success': False,
            'message': '用户不存在',
            'error_code': 'USER_NOT_FOUND'
        }), 404
    
    data = request.json or {}
    duration_minutes = data.get('duration_minutes', 30)
    
    try:
        user.lock_account(duration_minutes)
        security_service.session.commit()
        
        security_service._log_audit(
            'user_locked',
            user_id=g.current_user.id,
            resource_type='user',
            resource_id=str(user_id),
            details={'duration_minutes': duration_minutes}
        )
        
        return jsonify({
            'success': True,
            'message': f'用户账户已锁定{duration_minutes}分钟'
        })
        
    except Exception as e:
        security_service.session.rollback()
        return jsonify({
            'success': False,
            'message': '用户锁定失败',
            'error_code': 'USER_LOCK_FAILED'
        }), 500


@security_api.route('/users/<int:user_id>/unlock', methods=['POST'])
@token_required
@permission_required(Permission.USER_MANAGEMENT)
def unlock_user(user_id):
    """解锁用户账户"""
    user = security_service.session.query(User).get(user_id)
    
    if not user:
        return jsonify({
            'success': False,
            'message': '用户不存在',
            'error_code': 'USER_NOT_FOUND'
        }), 404
    
    try:
        user.unlock_account()
        security_service.session.commit()
        
        security_service._log_audit(
            'user_unlocked',
            user_id=g.current_user.id,
            resource_type='user',
            resource_id=str(user_id)
        )
        
        return jsonify({
            'success': True,
            'message': '用户账户已解锁'
        })
        
    except Exception as e:
        security_service.session.rollback()
        return jsonify({
            'success': False,
            'message': '用户解锁失败',
            'error_code': 'USER_UNLOCK_FAILED'
        }), 500


@security_api.route('/users/<int:user_id>/permissions', methods=['GET'])
@token_required
@permission_required(Permission.USER_MANAGEMENT)
def get_user_permissions(user_id):
    """获取用户权限"""
    user = security_service.session.query(User).get(user_id)
    
    if not user:
        return jsonify({
            'success': False,
            'message': '用户不存在',
            'error_code': 'USER_NOT_FOUND'
        }), 404
    
    permissions = security_service._get_user_permissions(user)
    
    return jsonify({
        'success': True,
        'user_id': user_id,
        'username': user.username,
        'role': user.role,
        'permissions': permissions
    })


@security_api.route('/users/<int:user_id>/permissions', methods=['POST'])
@token_required
@permission_required(Permission.USER_MANAGEMENT)
def grant_user_permission(user_id):
    """授予用户权限"""
    user = security_service.session.query(User).get(user_id)
    
    if not user:
        return jsonify({
            'success': False,
            'message': '用户不存在',
            'error_code': 'USER_NOT_FOUND'
        }), 404
    
    data = request.json
    if not data or 'permission' not in data:
        return jsonify({
            'success': False,
            'message': '缺少权限信息',
            'error_code': 'MISSING_PERMISSION'
        }), 400
    
    try:
        permission = Permission(data['permission'])
    except ValueError:
        return jsonify({
            'success': False,
            'message': '无效的权限',
            'error_code': 'INVALID_PERMISSION'
        }), 400
    
    expires_at = None
    if data.get('expires_at'):
        try:
            expires_at = datetime.datetime.fromisoformat(data['expires_at'])
        except ValueError:
            return jsonify({
                'success': False,
                'message': '无效的过期时间格式',
                'error_code': 'INVALID_EXPIRES_AT'
            }), 400
    
    success = security_service.grant_permission(
        user_id, permission, g.current_user.id, expires_at
    )
    
    if success:
        return jsonify({
            'success': True,
            'message': '权限授予成功'
        })
    else:
        return jsonify({
            'success': False,
            'message': '权限授予失败',
            'error_code': 'PERMISSION_GRANT_FAILED'
        }), 500


@security_api.route('/users/<int:user_id>/permissions/<permission>', methods=['DELETE'])
@token_required
@permission_required(Permission.USER_MANAGEMENT)
def revoke_user_permission(user_id, permission):
    """撤销用户权限"""
    user = security_service.session.query(User).get(user_id)
    
    if not user:
        return jsonify({
            'success': False,
            'message': '用户不存在',
            'error_code': 'USER_NOT_FOUND'
        }), 404
    
    try:
        permission_enum = Permission(permission)
    except ValueError:
        return jsonify({
            'success': False,
            'message': '无效的权限',
            'error_code': 'INVALID_PERMISSION'
        }), 400
    
    success = security_service.revoke_permission(
        user_id, permission_enum, g.current_user.id
    )
    
    if success:
        return jsonify({
            'success': True,
            'message': '权限撤销成功'
        })
    else:
        return jsonify({
            'success': False,
            'message': '权限撤销失败',
            'error_code': 'PERMISSION_REVOKE_FAILED'
        }), 500


@security_api.route('/device-access', methods=['GET'])
@token_required
@permission_required(Permission.DEVICE_MANAGEMENT)
def get_device_access_rules():
    """获取设备访问规则"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    device_id = request.args.get('device_id')
    
    query = security_service.session.query(DeviceAccess)
    
    if device_id:
        query = query.filter(DeviceAccess.device_id == device_id)
    
    total = query.count()
    rules = query.offset((page - 1) * per_page).limit(per_page).all()
    
    rules_data = []
    for rule in rules:
        rules_data.append({
            'id': rule.id,
            'device_id': rule.device_id,
            'user_id': rule.user_id,
            'username': rule.user.username if rule.user else None,
            'access_type': rule.access_type,
            'allowed_operations': rule.allowed_operations,
            'time_restrictions': rule.time_restrictions,
            'ip_restrictions': rule.ip_restrictions,
            'created_at': rule.created_at.isoformat()
        })
    
    return jsonify({
        'success': True,
        'rules': rules_data,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': total,
            'pages': (total + per_page - 1) // per_page
        }
    })


@security_api.route('/device-access', methods=['POST'])
@token_required
@permission_required(Permission.DEVICE_MANAGEMENT)
def create_device_access_rule():
    """创建设备访问规则"""
    data = request.json
    
    if not data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据',
            'error_code': 'INVALID_REQUEST'
        }), 400
    
    required_fields = ['device_id', 'access_type']
    for field in required_fields:
        if not data.get(field):
            return jsonify({
                'success': False,
                'message': f'缺少必填字段: {field}',
                'error_code': 'MISSING_FIELD'
            }), 400
    
    try:
        device_access = DeviceAccess(
            device_id=data['device_id'],
            user_id=data.get('user_id'),
            access_type=data['access_type'],
            allowed_operations=data.get('allowed_operations'),
            time_restrictions=data.get('time_restrictions'),
            ip_restrictions=data.get('ip_restrictions')
        )
        
        security_service.session.add(device_access)
        security_service.session.commit()
        
        security_service._log_audit(
            'device_access_rule_created',
            user_id=g.current_user.id,
            resource_type='device_access',
            resource_id=str(device_access.id),
            details={'device_id': data['device_id'], 'access_type': data['access_type']}
        )
        
        return jsonify({
            'success': True,
            'message': '设备访问规则创建成功',
            'rule_id': device_access.id
        }), 201
        
    except Exception as e:
        security_service.session.rollback()
        return jsonify({
            'success': False,
            'message': '设备访问规则创建失败',
            'error_code': 'DEVICE_ACCESS_RULE_CREATION_FAILED'
        }), 500


@security_api.route('/audit-logs', methods=['GET'])
@token_required
@permission_required(Permission.SYSTEM_ADMIN)
def get_audit_logs():
    """获取审计日志"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    user_id = request.args.get('user_id', type=int)
    action = request.args.get('action')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # 解析日期
    start_datetime = None
    end_datetime = None
    
    if start_date:
        try:
            start_datetime = datetime.datetime.fromisoformat(start_date)
        except ValueError:
            return jsonify({
                'success': False,
                'message': '无效的开始日期格式',
                'error_code': 'INVALID_START_DATE'
            }), 400
    
    if end_date:
        try:
            end_datetime = datetime.datetime.fromisoformat(end_date)
        except ValueError:
            return jsonify({
                'success': False,
                'message': '无效的结束日期格式',
                'error_code': 'INVALID_END_DATE'
            }), 400
    
    # 获取审计日志
    logs = security_service.get_audit_logs(
        user_id=user_id,
        action=action,
        start_date=start_datetime,
        end_date=end_datetime,
        limit=per_page * page
    )
    
    # 分页处理
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    page_logs = logs[start_idx:end_idx]
    
    logs_data = []
    for log in page_logs:
        logs_data.append({
            'id': log.id,
            'user_id': log.user_id,
            'username': log.user.username if log.user else None,
            'action': log.action,
            'resource_type': log.resource_type,
            'resource_id': log.resource_id,
            'ip_address': log.ip_address,
            'user_agent': log.user_agent,
            'request_method': log.request_method,
            'request_url': log.request_url,
            'details': log.details,
            'result': log.result,
            'error_message': log.error_message,
            'created_at': log.created_at.isoformat()
        })
    
    return jsonify({
        'success': True,
        'logs': logs_data,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': len(logs),
            'pages': (len(logs) + per_page - 1) // per_page
        }
    })


@security_api.route('/security-config', methods=['GET'])
@token_required
@permission_required(Permission.SYSTEM_ADMIN)
def get_security_config():
    """获取安全配置"""
    return jsonify({
        'success': True,
        'config': security_service.configs
    })


@security_api.route('/security-config', methods=['PUT'])
@token_required
@permission_required(Permission.SYSTEM_ADMIN)
def update_security_config():
    """更新安全配置"""
    data = request.json
    
    if not data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据',
            'error_code': 'INVALID_REQUEST'
        }), 400
    
    try:
        # 更新配置
        for key, value in data.items():
            if key in security_service.configs:
                security_service.configs[key] = value
                
                # 更新数据库中的配置
                from models.security import SecurityConfig
                config = security_service.session.query(SecurityConfig).filter_by(key=key).first()
                if config:
                    config.value = value
                    config.last_modified_by = g.current_user.id
                else:
                    config = SecurityConfig(
                        key=key,
                        value=value,
                        category='security',
                        last_modified_by=g.current_user.id
                    )
                    security_service.session.add(config)
        
        security_service.session.commit()
        
        security_service._log_audit(
            'security_config_updated',
            user_id=g.current_user.id,
            details={'updated_keys': list(data.keys())}
        )
        
        return jsonify({
            'success': True,
            'message': '安全配置更新成功'
        })
        
    except Exception as e:
        security_service.session.rollback()
        return jsonify({
            'success': False,
            'message': '安全配置更新失败',
            'error_code': 'CONFIG_UPDATE_FAILED'
        }), 500


@security_api.route('/security-scan', methods=['POST'])
@token_required
@permission_required(Permission.SYSTEM_ADMIN)
def security_scan():
    """安全扫描"""
    scan_type = request.json.get('scan_type', 'basic') if request.json else 'basic'
    
    # 执行安全扫描
    scan_results = {
        'scan_type': scan_type,
        'scan_time': datetime.datetime.now().isoformat(),
        'results': []
    }
    
    # 基础安全检查
    if scan_type in ['basic', 'full']:
        # 检查弱密码用户
        weak_password_users = []
        users = security_service.session.query(User).all()
        for user in users:
            # 这里可以实现密码强度检查逻辑
            # 简化实现
            if user.must_change_password:
                weak_password_users.append(user.username)
        
        if weak_password_users:
            scan_results['results'].append({
                'type': 'weak_passwords',
                'severity': 'medium',
                'message': f'发现{len(weak_password_users)}个用户需要更改密码',
                'details': weak_password_users
            })
        
        # 检查锁定的用户
        locked_users = []
        for user in users:
            if user.is_locked():
                locked_users.append(user.username)
        
        if locked_users:
            scan_results['results'].append({
                'type': 'locked_accounts',
                'severity': 'info',
                'message': f'发现{len(locked_users)}个被锁定的账户',
                'details': locked_users
            })
        
        # 检查过期会话
        from ..models.security import UserSession
        expired_sessions = security_service.session.query(UserSession).filter(
            UserSession.expires_at < datetime.datetime.utcnow(),
            UserSession.is_active == True
        ).count()
        
        if expired_sessions > 0:
            scan_results['results'].append({
                'type': 'expired_sessions',
                'severity': 'low',
                'message': f'发现{expired_sessions}个过期但仍活跃的会话',
                'details': {'count': expired_sessions}
            })
    
    # 完整安全检查
    if scan_type == 'full':
        # 检查权限异常
        # 检查设备访问异常
        # 检查审计日志异常
        pass
    
    # 记录扫描日志
    security_service._log_audit(
        'security_scan_performed',
        user_id=g.current_user.id,
        details={'scan_type': scan_type, 'issues_found': len(scan_results['results'])}
    )
    
    return jsonify({
        'success': True,
        'scan_results': scan_results
    })