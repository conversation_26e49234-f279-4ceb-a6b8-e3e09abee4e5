#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 多屏显示控制API
"""

from flask import Blueprint, request, jsonify
from flask_socketio import emit
import logging

from services.display_service import get_display_service, DisplayMode, LayoutType

# 配置日志
logger = logging.getLogger('display_api')

# 创建蓝图
display_bp = Blueprint('display', __name__, url_prefix='/api/display')

# 获取显示服务实例
display_service = get_display_service()

@display_bp.route('/configs', methods=['POST'])
def create_display_config():
    """创建显示配置"""
    try:
        data = request.get_json()
        
        # 验证必需字段
        required_fields = ['classroom_id', 'mode', 'layout', 'target_devices', 'content_sources']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必需字段: {field}'}), 400
        
        # 验证显示模式和布局类型
        try:
            DisplayMode(data['mode'])
            LayoutType(data['layout'])
        except ValueError:
            return jsonify({'error': '无效的显示模式或布局类型'}), 400
        
        # 创建显示配置
        config = display_service.create_display_config(
            classroom_id=data['classroom_id'],
            mode=data['mode'],
            layout=data['layout'],
            target_devices=data['target_devices'],
            content_sources=data['content_sources'],
            sync_enabled=data.get('sync_enabled', True)
        )
        
        return jsonify({
            'success': True,
            'config': config.to_dict()
        }), 201
        
    except Exception as e:
        logger.error(f"创建显示配置时出错: {str(e)}")
        return jsonify({'error': '创建显示配置失败'}), 500

@display_bp.route('/configs/<config_id>', methods=['PUT'])
def update_display_config(config_id):
    """更新显示配置"""
    try:
        data = request.get_json()
        
        # 验证显示模式和布局类型（如果提供）
        if 'mode' in data:
            try:
                DisplayMode(data['mode'])
            except ValueError:
                return jsonify({'error': '无效的显示模式'}), 400
        
        if 'layout' in data:
            try:
                LayoutType(data['layout'])
            except ValueError:
                return jsonify({'error': '无效的布局类型'}), 400
        
        # 更新配置
        config = display_service.update_display_config(config_id, **data)
        
        if not config:
            return jsonify({'error': '找不到指定的显示配置'}), 404
        
        return jsonify({
            'success': True,
            'config': config.to_dict()
        })
        
    except Exception as e:
        logger.error(f"更新显示配置时出错: {str(e)}")
        return jsonify({'error': '更新显示配置失败'}), 500

@display_bp.route('/configs/<config_id>', methods=['DELETE'])
def delete_display_config(config_id):
    """删除显示配置"""
    try:
        success = display_service.delete_display_config(config_id)
        
        if not success:
            return jsonify({'error': '找不到指定的显示配置'}), 404
        
        return jsonify({'success': True})
        
    except Exception as e:
        logger.error(f"删除显示配置时出错: {str(e)}")
        return jsonify({'error': '删除显示配置失败'}), 500

@display_bp.route('/configs/<config_id>', methods=['GET'])
def get_display_config(config_id):
    """获取显示配置"""
    try:
        config = display_service.get_display_config(config_id)
        
        if not config:
            return jsonify({'error': '找不到指定的显示配置'}), 404
        
        return jsonify({
            'success': True,
            'config': config.to_dict()
        })
        
    except Exception as e:
        logger.error(f"获取显示配置时出错: {str(e)}")
        return jsonify({'error': '获取显示配置失败'}), 500

@display_bp.route('/configs', methods=['GET'])
def list_display_configs():
    """列出显示配置"""
    try:
        classroom_id = request.args.get('classroom_id')
        configs = display_service.list_display_configs(classroom_id)
        
        return jsonify({
            'success': True,
            'configs': [config.to_dict() for config in configs]
        })
        
    except Exception as e:
        logger.error(f"列出显示配置时出错: {str(e)}")
        return jsonify({'error': '获取显示配置列表失败'}), 500

@display_bp.route('/configs/<config_id>/apply', methods=['POST'])
def apply_display_config(config_id):
    """应用显示配置"""
    try:
        success = display_service.apply_display_config(config_id)
        
        if not success:
            return jsonify({'error': '应用显示配置失败'}), 400
        
        # 通过WebSocket通知相关设备
        config = display_service.get_display_config(config_id)
        if config:
            emit('display_config_applied', {
                'config_id': config_id,
                'config': config.to_dict()
            }, room=f"classroom_{config.classroom_id}")
        
        return jsonify({'success': True})
        
    except Exception as e:
        logger.error(f"应用显示配置时出错: {str(e)}")
        return jsonify({'error': '应用显示配置失败'}), 500

@display_bp.route('/displays/active', methods=['GET'])
def get_active_displays():
    """获取活动显示状态"""
    try:
        classroom_id = request.args.get('classroom_id')
        displays = display_service.get_active_displays(classroom_id)
        
        return jsonify({
            'success': True,
            'displays': displays
        })
        
    except Exception as e:
        logger.error(f"获取活动显示状态时出错: {str(e)}")
        return jsonify({'error': '获取活动显示状态失败'}), 500

@display_bp.route('/displays/<config_id>/stop', methods=['POST'])
def stop_display(config_id):
    """停止显示"""
    try:
        success = display_service.stop_display(config_id)
        
        if not success:
            return jsonify({'error': '找不到指定的显示配置'}), 404
        
        # 通过WebSocket通知相关设备
        emit('display_stopped', {'config_id': config_id}, broadcast=True)
        
        return jsonify({'success': True})
        
    except Exception as e:
        logger.error(f"停止显示时出错: {str(e)}")
        return jsonify({'error': '停止显示失败'}), 500

@display_bp.route('/timers', methods=['POST'])
def create_group_timer():
    """创建分组计时器"""
    try:
        data = request.get_json()
        
        # 验证必需字段
        required_fields = ['classroom_id', 'group_ids', 'title', 'duration']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必需字段: {field}'}), 400
        
        # 验证持续时间
        if not isinstance(data['duration'], int) or data['duration'] <= 0:
            return jsonify({'error': '持续时间必须是正整数'}), 400
        
        # 创建计时器
        timer = display_service.create_group_timer(
            classroom_id=data['classroom_id'],
            group_ids=data['group_ids'],
            title=data['title'],
            duration=data['duration']
        )
        
        return jsonify({
            'success': True,
            'timer': timer.to_dict()
        }), 201
        
    except Exception as e:
        logger.error(f"创建分组计时器时出错: {str(e)}")
        return jsonify({'error': '创建分组计时器失败'}), 500

@display_bp.route('/timers/<timer_id>/start', methods=['POST'])
def start_group_timer(timer_id):
    """启动分组计时器"""
    try:
        success = display_service.start_group_timer(timer_id)
        
        if not success:
            return jsonify({'error': '找不到指定的计时器'}), 404
        
        # 获取计时器信息并通过WebSocket广播
        timer = display_service.get_group_timer(timer_id)
        if timer:
            emit('timer_started', timer.to_dict(), room=f"classroom_{timer.classroom_id}")
        
        return jsonify({'success': True})
        
    except Exception as e:
        logger.error(f"启动分组计时器时出错: {str(e)}")
        return jsonify({'error': '启动分组计时器失败'}), 500

@display_bp.route('/timers/<timer_id>/pause', methods=['POST'])
def pause_group_timer(timer_id):
    """暂停分组计时器"""
    try:
        success = display_service.pause_group_timer(timer_id)
        
        if not success:
            return jsonify({'error': '找不到指定的计时器'}), 404
        
        # 通过WebSocket广播暂停事件
        timer = display_service.get_group_timer(timer_id)
        if timer:
            emit('timer_paused', timer.to_dict(), room=f"classroom_{timer.classroom_id}")
        
        return jsonify({'success': True})
        
    except Exception as e:
        logger.error(f"暂停分组计时器时出错: {str(e)}")
        return jsonify({'error': '暂停分组计时器失败'}), 500

@display_bp.route('/timers/<timer_id>/resume', methods=['POST'])
def resume_group_timer(timer_id):
    """恢复分组计时器"""
    try:
        success = display_service.resume_group_timer(timer_id)
        
        if not success:
            return jsonify({'error': '找不到指定的计时器'}), 404
        
        # 通过WebSocket广播恢复事件
        timer = display_service.get_group_timer(timer_id)
        if timer:
            emit('timer_resumed', timer.to_dict(), room=f"classroom_{timer.classroom_id}")
        
        return jsonify({'success': True})
        
    except Exception as e:
        logger.error(f"恢复分组计时器时出错: {str(e)}")
        return jsonify({'error': '恢复分组计时器失败'}), 500

@display_bp.route('/timers/<timer_id>/stop', methods=['POST'])
def stop_group_timer(timer_id):
    """停止分组计时器"""
    try:
        success = display_service.stop_group_timer(timer_id)
        
        if not success:
            return jsonify({'error': '找不到指定的计时器'}), 404
        
        # 通过WebSocket广播停止事件
        timer = display_service.get_group_timer(timer_id)
        if timer:
            emit('timer_stopped', timer.to_dict(), room=f"classroom_{timer.classroom_id}")
        
        return jsonify({'success': True})
        
    except Exception as e:
        logger.error(f"停止分组计时器时出错: {str(e)}")
        return jsonify({'error': '停止分组计时器失败'}), 500

@display_bp.route('/timers/<timer_id>', methods=['GET'])
def get_group_timer(timer_id):
    """获取分组计时器"""
    try:
        timer = display_service.get_group_timer(timer_id)
        
        if not timer:
            return jsonify({'error': '找不到指定的计时器'}), 404
        
        return jsonify({
            'success': True,
            'timer': timer.to_dict()
        })
        
    except Exception as e:
        logger.error(f"获取分组计时器时出错: {str(e)}")
        return jsonify({'error': '获取分组计时器失败'}), 500

@display_bp.route('/timers', methods=['GET'])
def list_group_timers():
    """列出分组计时器"""
    try:
        classroom_id = request.args.get('classroom_id')
        timers = display_service.list_group_timers(classroom_id)
        
        return jsonify({
            'success': True,
            'timers': [timer.to_dict() for timer in timers]
        })
        
    except Exception as e:
        logger.error(f"列出分组计时器时出错: {str(e)}")
        return jsonify({'error': '获取分组计时器列表失败'}), 500

@display_bp.route('/topics', methods=['POST'])
def create_discussion_topic():
    """创建讨论主题"""
    try:
        data = request.get_json()
        
        # 验证必需字段
        required_fields = ['classroom_id', 'title', 'content']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必需字段: {field}'}), 400
        
        # 创建讨论主题
        topic = display_service.create_discussion_topic(
            classroom_id=data['classroom_id'],
            title=data['title'],
            content=data['content'],
            image_data=data.get('image_data'),
            target_groups=data.get('target_groups', [])
        )
        
        return jsonify({
            'success': True,
            'topic': topic.to_dict()
        }), 201
        
    except Exception as e:
        logger.error(f"创建讨论主题时出错: {str(e)}")
        return jsonify({'error': '创建讨论主题失败'}), 500

@display_bp.route('/topics/<topic_id>/distribute', methods=['POST'])
def distribute_discussion_topic(topic_id):
    """分发讨论主题"""
    try:
        success = display_service.distribute_discussion_topic(topic_id)
        
        if not success:
            return jsonify({'error': '找不到指定的讨论主题'}), 404
        
        # 获取主题信息并通过WebSocket分发
        topic = display_service.get_discussion_topic(topic_id)
        if topic:
            # 向目标小组分发主题
            for group_id in topic.target_groups:
                emit('discussion_topic_received', topic.to_dict(), room=f"group_{group_id}")
            
            # 如果没有指定目标小组，则向整个课堂分发
            if not topic.target_groups:
                emit('discussion_topic_received', topic.to_dict(), room=f"classroom_{topic.classroom_id}")
        
        return jsonify({'success': True})
        
    except Exception as e:
        logger.error(f"分发讨论主题时出错: {str(e)}")
        return jsonify({'error': '分发讨论主题失败'}), 500

@display_bp.route('/topics/<topic_id>', methods=['GET'])
def get_discussion_topic(topic_id):
    """获取讨论主题"""
    try:
        topic = display_service.get_discussion_topic(topic_id)
        
        if not topic:
            return jsonify({'error': '找不到指定的讨论主题'}), 404
        
        return jsonify({
            'success': True,
            'topic': topic.to_dict()
        })
        
    except Exception as e:
        logger.error(f"获取讨论主题时出错: {str(e)}")
        return jsonify({'error': '获取讨论主题失败'}), 500

@display_bp.route('/topics', methods=['GET'])
def list_discussion_topics():
    """列出讨论主题"""
    try:
        classroom_id = request.args.get('classroom_id')
        topics = display_service.list_discussion_topics(classroom_id)
        
        return jsonify({
            'success': True,
            'topics': [topic.to_dict() for topic in topics]
        })
        
    except Exception as e:
        logger.error(f"列出讨论主题时出错: {str(e)}")
        return jsonify({'error': '获取讨论主题列表失败'}), 500

@display_bp.route('/modes', methods=['GET'])
def get_display_modes():
    """获取支持的显示模式"""
    try:
        modes = [mode.value for mode in DisplayMode]
        layouts = [layout.value for layout in LayoutType]
        
        return jsonify({
            'success': True,
            'modes': modes,
            'layouts': layouts
        })
        
    except Exception as e:
        logger.error(f"获取显示模式时出错: {str(e)}")
        return jsonify({'error': '获取显示模式失败'}), 500

# WebSocket事件处理
def register_display_websocket_events(socketio):
    """注册显示相关的WebSocket事件"""
    
    @socketio.on('join_classroom')
    def handle_join_classroom(data):
        """加入课堂房间"""
        classroom_id = data.get('classroom_id')
        if classroom_id:
            from flask_socketio import join_room
            join_room(f"classroom_{classroom_id}")
            emit('joined_classroom', {'classroom_id': classroom_id})
    
    @socketio.on('join_group')
    def handle_join_group(data):
        """加入小组房间"""
        group_id = data.get('group_id')
        if group_id:
            from flask_socketio import join_room
            join_room(f"group_{group_id}")
            emit('joined_group', {'group_id': group_id})
    
    @socketio.on('request_timer_status')
    def handle_request_timer_status(data):
        """请求计时器状态"""
        timer_id = data.get('timer_id')
        if timer_id:
            timer = display_service.get_group_timer(timer_id)
            if timer:
                emit('timer_status', timer.to_dict())
    
    @socketio.on('request_display_status')
    def handle_request_display_status(data):
        """请求显示状态"""
        classroom_id = data.get('classroom_id')
        displays = display_service.get_active_displays(classroom_id)
        emit('display_status', {'displays': displays})