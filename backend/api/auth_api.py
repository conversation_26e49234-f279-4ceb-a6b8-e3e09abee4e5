# -*- coding: utf-8 -*-
"""
增强的认证和授权API
"""

import datetime
from functools import wraps
from flask import Blueprint, request, jsonify, g
from services.security_service import get_security_service
from models.security import User, Permission, UserRole

# 创建蓝图
auth_api = Blueprint('auth_api', __name__, url_prefix='/api/auth')

# 获取安全服务
security_service = get_security_service()


def token_required(f):
    """验证令牌的装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        
        # 从请求头获取令牌
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
        
        if not token:
            return jsonify({
                'success': False,
                'message': '缺少认证令牌',
                'error_code': 'MISSING_TOKEN'
            }), 401
        
        # 验证会话
        user = security_service.validate_session(token)
        if not user:
            return jsonify({
                'success': False,
                'message': '无效或已过期的令牌',
                'error_code': 'INVALID_TOKEN'
            }), 401
        
        # 设置当前用户信息
        g.current_user = user
        g.session_token = token
        
        return f(*args, **kwargs)
    
    return decorated


def permission_required(permission: Permission):
    """验证权限的装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated(*args, **kwargs):
            if not hasattr(g, 'current_user') or not g.current_user:
                return jsonify({
                    'success': False,
                    'message': '未认证的用户',
                    'error_code': 'UNAUTHENTICATED'
                }), 401
            
            if not security_service.check_permission(g.current_user, permission):
                return jsonify({
                    'success': False,
                    'message': '权限不足',
                    'error_code': 'INSUFFICIENT_PERMISSION'
                }), 403
            
            return f(*args, **kwargs)
        
        return decorated
    
    return decorator


def role_required(roles):
    """验证用户角色的装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated(*args, **kwargs):
            if not hasattr(g, 'current_user') or not g.current_user:
                return jsonify({
                    'success': False,
                    'message': '未认证的用户',
                    'error_code': 'UNAUTHENTICATED'
                }), 401
            
            if g.current_user.role not in roles:
                return jsonify({
                    'success': False,
                    'message': '角色权限不足',
                    'error_code': 'INSUFFICIENT_ROLE'
                }), 403
            
            return f(*args, **kwargs)
        
        return decorated
    
    return decorator


@auth_api.route('/login', methods=['POST'])
def login():
    """用户登录 - 支持多种登录方式"""
    data = request.json
    
    if not data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据',
            'error_code': 'INVALID_REQUEST'
        }), 400
    
    login_type = data.get('login_type', 'password')  # password, qr_code, course_schedule
    
    if login_type == 'password':
        return _login_with_password(data)
    elif login_type == 'qr_code':
        return _login_with_qr_code(data)
    elif login_type == 'course_schedule':
        return _login_with_course_schedule(data)
    else:
        return jsonify({
            'success': False,
            'message': '不支持的登录方式',
            'error_code': 'UNSUPPORTED_LOGIN_TYPE'
        }), 400


def _login_with_password(data):
    """密码登录"""
    username = data.get('username')
    password = data.get('password')
    device_info = data.get('device_info', {})
    
    if not username or not password:
        return jsonify({
            'success': False,
            'message': '缺少用户名或密码',
            'error_code': 'MISSING_CREDENTIALS'
        }), 400
    
    # 验证密码策略
    password_validation = security_service.validate_password_policy(password)
    if not password_validation['valid'] and data.get('validate_password', True):
        return jsonify({
            'success': False,
            'message': '密码不符合安全策略',
            'errors': password_validation['errors'],
            'error_code': 'INVALID_PASSWORD_POLICY'
        }), 400
    
    # 认证用户
    result = security_service.authenticate_user(username, password, device_info)
    
    if result['success']:
        return jsonify(result), 200
    else:
        return jsonify(result), 401


def _login_with_qr_code(data):
    """二维码登录"""
    qr_token = data.get('qr_token')
    device_info = data.get('device_info', {})
    
    if not qr_token:
        return jsonify({
            'success': False,
            'message': '缺少二维码令牌',
            'error_code': 'MISSING_QR_TOKEN'
        }), 400
    
    # 这里应该验证二维码令牌并获取用户信息
    # 简化实现，实际应用中需要完整的二维码认证流程
    return jsonify({
        'success': False,
        'message': '二维码登录功能正在开发中',
        'error_code': 'FEATURE_NOT_IMPLEMENTED'
    }), 501


def _login_with_course_schedule(data):
    """课表关联登录"""
    course_id = data.get('course_id')
    teacher_id = data.get('teacher_id')
    device_info = data.get('device_info', {})
    
    if not course_id or not teacher_id:
        return jsonify({
            'success': False,
            'message': '缺少课程ID或教师ID',
            'error_code': 'MISSING_COURSE_INFO'
        }), 400
    
    # 这里应该验证课程信息并自动登录
    # 简化实现，实际应用中需要与课程管理系统集成
    return jsonify({
        'success': False,
        'message': '课表关联登录功能正在开发中',
        'error_code': 'FEATURE_NOT_IMPLEMENTED'
    }), 501


@auth_api.route('/student-checkin', methods=['POST'])
def student_checkin():
    """学生考勤 - 支持二维码和数字码"""
    data = request.json
    
    if not data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据',
            'error_code': 'INVALID_REQUEST'
        }), 400
    
    checkin_type = data.get('checkin_type', 'qr_code')  # qr_code, digital_code
    
    if checkin_type == 'qr_code':
        return _checkin_with_qr_code(data)
    elif checkin_type == 'digital_code':
        return _checkin_with_digital_code(data)
    else:
        return jsonify({
            'success': False,
            'message': '不支持的考勤方式',
            'error_code': 'UNSUPPORTED_CHECKIN_TYPE'
        }), 400


def _checkin_with_qr_code(data):
    """二维码考勤"""
    qr_code = data.get('qr_code')
    student_info = data.get('student_info', {})
    
    if not qr_code:
        return jsonify({
            'success': False,
            'message': '缺少二维码',
            'error_code': 'MISSING_QR_CODE'
        }), 400
    
    # 验证二维码并记录考勤
    # 简化实现
    return jsonify({
        'success': True,
        'message': '二维码考勤成功',
        'checkin_time': datetime.datetime.now().isoformat()
    })


def _checkin_with_digital_code(data):
    """数字码考勤"""
    digital_code = data.get('digital_code')
    code_length = len(str(digital_code)) if digital_code else 0
    student_info = data.get('student_info', {})
    
    if not digital_code or code_length not in [4, 6, 9]:
        return jsonify({
            'success': False,
            'message': '数字码必须是4位、6位或9位',
            'error_code': 'INVALID_DIGITAL_CODE'
        }), 400
    
    # 验证数字码并记录考勤
    # 简化实现
    return jsonify({
        'success': True,
        'message': f'{code_length}位数字码考勤成功',
        'checkin_time': datetime.datetime.now().isoformat()
    })


@auth_api.route('/logout', methods=['POST'])
@token_required
def logout():
    """用户登出"""
    success = security_service.logout_user(g.session_token)
    
    if success:
        return jsonify({
            'success': True,
            'message': '登出成功'
        })
    else:
        return jsonify({
            'success': False,
            'message': '登出失败',
            'error_code': 'LOGOUT_FAILED'
        }), 500


@auth_api.route('/verify', methods=['GET'])
@token_required
def verify_token():
    """验证令牌"""
    return jsonify({
        'success': True,
        'message': '令牌有效',
        'user': {
            'id': g.current_user.id,
            'username': g.current_user.username,
            'name': g.current_user.name,
            'role': g.current_user.role,
            'permissions': security_service._get_user_permissions(g.current_user)
        }
    })


@auth_api.route('/profile', methods=['GET'])
@token_required
def get_profile():
    """获取用户资料"""
    user = g.current_user
    
    return jsonify({
        'success': True,
        'user': {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'name': user.name,
            'role': user.role,
            'phone': user.phone,
            'department': user.department,
            'last_login': user.last_login.isoformat() if user.last_login else None,
            'is_verified': user.is_verified,
            'permissions': security_service._get_user_permissions(user)
        }
    })


@auth_api.route('/change-password', methods=['POST'])
@token_required
def change_password():
    """修改密码"""
    data = request.json
    
    if not data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据',
            'error_code': 'INVALID_REQUEST'
        }), 400
    
    old_password = data.get('old_password')
    new_password = data.get('new_password')
    
    if not old_password or not new_password:
        return jsonify({
            'success': False,
            'message': '缺少旧密码或新密码',
            'error_code': 'MISSING_PASSWORDS'
        }), 400
    
    user = g.current_user
    
    # 验证旧密码
    if not user.check_password(old_password):
        return jsonify({
            'success': False,
            'message': '旧密码错误',
            'error_code': 'INVALID_OLD_PASSWORD'
        }), 401
    
    # 验证新密码策略
    password_validation = security_service.validate_password_policy(new_password)
    if not password_validation['valid']:
        return jsonify({
            'success': False,
            'message': '新密码不符合安全策略',
            'errors': password_validation['errors'],
            'error_code': 'INVALID_PASSWORD_POLICY'
        }), 400
    
    # 更新密码
    user.set_password(new_password)
    user.must_change_password = False
    
    try:
        security_service.session.commit()
        return jsonify({
            'success': True,
            'message': '密码修改成功'
        })
    except Exception as e:
        security_service.session.rollback()
        return jsonify({
            'success': False,
            'message': '密码修改失败',
            'error_code': 'PASSWORD_CHANGE_FAILED'
        }), 500


@auth_api.route('/permissions', methods=['GET'])
@token_required
def get_user_permissions():
    """获取用户权限列表"""
    permissions = security_service._get_user_permissions(g.current_user)
    
    return jsonify({
        'success': True,
        'permissions': permissions,
        'role': g.current_user.role
    })


@auth_api.route('/sessions', methods=['GET'])
@token_required
def get_user_sessions():
    """获取用户活跃会话"""
    user_sessions = security_service.session.query(security_service.session.query(User).get(g.current_user.id).sessions).filter_by(is_active=True).all()
    
    sessions_data = []
    for session in user_sessions:
        sessions_data.append({
            'id': session.id,
            'device_info': session.device_info,
            'ip_address': session.ip_address,
            'last_activity': session.last_activity.isoformat(),
            'expires_at': session.expires_at.isoformat(),
            'is_current': session.session_token == g.session_token
        })
    
    return jsonify({
        'success': True,
        'sessions': sessions_data
    })


@auth_api.route('/sessions/<int:session_id>', methods=['DELETE'])
@token_required
def terminate_session(session_id):
    """终止指定会话"""
    user_session = security_service.session.query(security_service.session.query(User).get(g.current_user.id).sessions).filter_by(
        id=session_id, user_id=g.current_user.id, is_active=True
    ).first()
    
    if not user_session:
        return jsonify({
            'success': False,
            'message': '会话不存在',
            'error_code': 'SESSION_NOT_FOUND'
        }), 404
    
    user_session.is_active = False
    
    try:
        security_service.session.commit()
        return jsonify({
            'success': True,
            'message': '会话已终止'
        })
    except Exception as e:
        security_service.session.rollback()
        return jsonify({
            'success': False,
            'message': '会话终止失败',
            'error_code': 'SESSION_TERMINATION_FAILED'
        }), 500