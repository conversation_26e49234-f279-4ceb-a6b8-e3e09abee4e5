# -*- coding: utf-8 -*-
"""
课堂报告和数据分析API
"""

from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, send_file
from sqlalchemy.exc import SQLAlchemyError

from models.base import db
from models.classroom import Classroom
from models.student import Student
from models.report import ClassroomReport, LearningAnalytics, DataExport
from services.report_service import ReportService, AnalyticsService, ExportService

# 创建蓝图
report_bp = Blueprint('report', __name__, url_prefix='/api/report')


@report_bp.route('/generate', methods=['POST'])
def generate_report():
    """生成课堂报告"""
    try:
        data = request.get_json()
        
        # 验证必需参数
        classroom_id = data.get('classroom_id')
        teacher_id = data.get('teacher_id')
        report_type = data.get('report_type', 'comprehensive')
        
        if not classroom_id or not teacher_id:
            return jsonify({
                'success': False,
                'message': '缺少必需参数'
            }), 400
        
        # 解析时间范围
        start_time = None
        end_time = None
        if data.get('start_time'):
            start_time = datetime.fromisoformat(data['start_time'])
        if data.get('end_time'):
            end_time = datetime.fromisoformat(data['end_time'])
        
        # 验证课堂存在
        classroom = Classroom.get_by_id(classroom_id)
        if not classroom:
            return jsonify({
                'success': False,
                'message': '课堂不存在'
            }), 404
        
        # 生成报告
        if report_type == 'comprehensive':
            report = ReportService.generate_comprehensive_report(
                classroom_id=classroom_id,
                teacher_id=teacher_id,
                start_time=start_time,
                end_time=end_time
            )
        else:
            return jsonify({
                'success': False,
                'message': f'不支持的报告类型: {report_type}'
            }), 400
        
        return jsonify({
            'success': True,
            'message': '报告生成成功',
            'data': {
                'report_id': report.report_id,
                'status': report.status,
                'title': report.title
            }
        })
        
    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'生成报告失败: {str(e)}'
        }), 500


@report_bp.route('/<report_id>', methods=['GET'])
def get_report(report_id):
    """获取报告详情"""
    try:
        report = ClassroomReport.get_by_report_id(report_id)
        if not report:
            return jsonify({
                'success': False,
                'message': '报告不存在'
            }), 404
        
        return jsonify({
            'success': True,
            'data': {
                'report_id': report.report_id,
                'title': report.title,
                'report_type': report.report_type,
                'status': report.status,
                'generated_at': report.generated_at.isoformat(),
                'start_time': report.start_time.isoformat() if report.start_time else None,
                'end_time': report.end_time.isoformat() if report.end_time else None,
                'summary': report.summary,
                'data': report.data,
                'charts': report.charts,
                'notes': report.notes,
                'tags': report.tags
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取报告失败: {str(e)}'
        }), 500


@report_bp.route('/classroom/<int:classroom_id>', methods=['GET'])
def get_classroom_reports(classroom_id):
    """获取课堂报告列表"""
    try:
        # 验证课堂存在
        classroom = Classroom.get_by_id(classroom_id)
        if not classroom:
            return jsonify({
                'success': False,
                'message': '课堂不存在'
            }), 404
        
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        
        # 查询报告
        reports = ClassroomReport.query.filter_by(classroom_id=classroom_id)\
                                     .order_by(ClassroomReport.created_at.desc())\
                                     .paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'success': True,
            'data': {
                'reports': [
                    {
                        'report_id': report.report_id,
                        'title': report.title,
                        'report_type': report.report_type,
                        'status': report.status,
                        'generated_at': report.generated_at.isoformat(),
                        'summary': report.summary[:200] + '...' if report.summary and len(report.summary) > 200 else report.summary
                    }
                    for report in reports.items
                ],
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': reports.total,
                    'pages': reports.pages,
                    'has_next': reports.has_next,
                    'has_prev': reports.has_prev
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取报告列表失败: {str(e)}'
        }), 500


@report_bp.route('/<report_id>/notes', methods=['PUT'])
def update_report_notes(report_id):
    """更新报告备注"""
    try:
        data = request.get_json()
        notes = data.get('notes', '')
        
        report = ClassroomReport.get_by_report_id(report_id)
        if not report:
            return jsonify({
                'success': False,
                'message': '报告不存在'
            }), 404
        
        report.notes = notes
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '备注更新成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新备注失败: {str(e)}'
        }), 500


@report_bp.route('/analytics/student/<int:student_id>', methods=['POST'])
def analyze_student(student_id):
    """分析学生学习情况"""
    try:
        data = request.get_json()
        classroom_id = data.get('classroom_id')
        analysis_type = data.get('analysis_type', 'participation')
        
        if not classroom_id:
            return jsonify({
                'success': False,
                'message': '缺少课堂ID'
            }), 400
        
        # 验证学生和课堂存在
        student = Student.get_by_id(student_id)
        if not student:
            return jsonify({
                'success': False,
                'message': '学生不存在'
            }), 404
        
        classroom = Classroom.get_by_id(classroom_id)
        if not classroom:
            return jsonify({
                'success': False,
                'message': '课堂不存在'
            }), 404
        
        # 执行分析
        if analysis_type == 'participation':
            analytics = AnalyticsService.analyze_student_participation(student_id, classroom_id)
        else:
            return jsonify({
                'success': False,
                'message': f'不支持的分析类型: {analysis_type}'
            }), 400
        
        return jsonify({
            'success': True,
            'message': '学生分析完成',
            'data': {
                'analytics_id': analytics.analytics_id,
                'analysis_type': analytics.analysis_type,
                'status': analytics.status,
                'confidence_score': analytics.confidence_score,
                'metrics': analytics.metrics,
                'insights': analytics.insights,
                'recommendations': analytics.recommendations
            }
        })
        
    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'学生分析失败: {str(e)}'
        }), 500


@report_bp.route('/analytics/student/<int:student_id>/history', methods=['GET'])
def get_student_analytics_history(student_id):
    """获取学生分析历史"""
    try:
        # 验证学生存在
        student = Student.get_by_id(student_id)
        if not student:
            return jsonify({
                'success': False,
                'message': '学生不存在'
            }), 404
        
        # 获取查询参数
        analysis_type = request.args.get('analysis_type')
        limit = min(request.args.get('limit', 10, type=int), 50)
        
        # 查询分析历史
        analytics_list = LearningAnalytics.get_student_analytics(student_id, analysis_type)[:limit]
        
        return jsonify({
            'success': True,
            'data': [
                {
                    'analytics_id': analytics.analytics_id,
                    'analysis_type': analytics.analysis_type,
                    'status': analytics.status,
                    'confidence_score': analytics.confidence_score,
                    'created_at': analytics.created_at.isoformat(),
                    'insights_count': len(analytics.insights) if analytics.insights else 0,
                    'recommendations_count': len(analytics.recommendations) if analytics.recommendations else 0
                }
                for analytics in analytics_list
            ]
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取分析历史失败: {str(e)}'
        }), 500


@report_bp.route('/export', methods=['POST'])
def export_data():
    """导出数据"""
    try:
        data = request.get_json()
        
        # 验证必需参数
        classroom_id = data.get('classroom_id')
        export_format = data.get('format', 'csv')
        data_type = data.get('data_type', 'attendance')
        user_id = data.get('user_id')
        
        if not classroom_id or not user_id:
            return jsonify({
                'success': False,
                'message': '缺少必需参数'
            }), 400
        
        # 验证课堂存在
        classroom = Classroom.get_by_id(classroom_id)
        if not classroom:
            return jsonify({
                'success': False,
                'message': '课堂不存在'
            }), 404
        
        # 执行导出
        if data_type == 'attendance':
            export_task = ExportService.export_attendance_data(classroom_id, export_format, user_id)
        else:
            return jsonify({
                'success': False,
                'message': f'不支持的数据类型: {data_type}'
            }), 400
        
        return jsonify({
            'success': True,
            'message': '导出任务创建成功',
            'data': {
                'export_id': export_task.export_id,
                'filename': export_task.filename,
                'status': export_task.status,
                'progress': export_task.progress
            }
        })
        
    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导出失败: {str(e)}'
        }), 500


@report_bp.route('/export/<export_id>/status', methods=['GET'])
def get_export_status(export_id):
    """获取导出状态"""
    try:
        export_task = DataExport.get_by_export_id(export_id)
        if not export_task:
            return jsonify({
                'success': False,
                'message': '导出任务不存在'
            }), 404
        
        return jsonify({
            'success': True,
            'data': {
                'export_id': export_task.export_id,
                'filename': export_task.filename,
                'status': export_task.status,
                'progress': export_task.progress,
                'file_size': export_task.file_size,
                'requested_at': export_task.requested_at.isoformat(),
                'completed_at': export_task.completed_at.isoformat() if export_task.completed_at else None
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取导出状态失败: {str(e)}'
        }), 500


@report_bp.route('/export/<export_id>/download', methods=['GET'])
def download_export(export_id):
    """下载导出文件"""
    try:
        export_task = DataExport.get_by_export_id(export_id)
        if not export_task:
            return jsonify({
                'success': False,
                'message': '导出任务不存在'
            }), 404
        
        if export_task.status != 'completed':
            return jsonify({
                'success': False,
                'message': '导出任务未完成'
            }), 400
        
        if not export_task.file_path or not os.path.exists(export_task.file_path):
            return jsonify({
                'success': False,
                'message': '导出文件不存在'
            }), 404
        
        return send_file(
            export_task.file_path,
            as_attachment=True,
            download_name=export_task.filename
        )
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'下载文件失败: {str(e)}'
        }), 500


@report_bp.route('/export/user/<user_id>', methods=['GET'])
def get_user_exports(user_id):
    """获取用户的导出任务列表"""
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        
        # 查询导出任务
        exports = DataExport.query.filter_by(requested_by=user_id)\
                                 .order_by(DataExport.created_at.desc())\
                                 .paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'success': True,
            'data': {
                'exports': [
                    {
                        'export_id': export.export_id,
                        'filename': export.filename,
                        'export_type': export.export_type,
                        'data_type': export.data_type,
                        'status': export.status,
                        'progress': export.progress,
                        'file_size': export.file_size,
                        'requested_at': export.requested_at.isoformat(),
                        'completed_at': export.completed_at.isoformat() if export.completed_at else None
                    }
                    for export in exports.items
                ],
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': exports.total,
                    'pages': exports.pages,
                    'has_next': exports.has_next,
                    'has_prev': exports.has_prev
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取导出列表失败: {str(e)}'
        }), 500


@report_bp.route('/statistics/classroom/<int:classroom_id>', methods=['GET'])
def get_classroom_statistics(classroom_id):
    """获取课堂统计数据"""
    try:
        # 验证课堂存在
        classroom = Classroom.get_by_id(classroom_id)
        if not classroom:
            return jsonify({
                'success': False,
                'message': '课堂不存在'
            }), 404
        
        # 获取时间范围参数
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        
        if start_time:
            start_time = datetime.fromisoformat(start_time)
        else:
            start_time = classroom.start_time or classroom.created_at
        
        if end_time:
            end_time = datetime.fromisoformat(end_time)
        else:
            end_time = classroom.end_time or datetime.utcnow()
        
        # 收集统计数据
        basic_data = ReportService._collect_basic_data(classroom_id, start_time, end_time)
        attendance_data = ReportService._collect_attendance_data(classroom_id, start_time, end_time)
        interaction_data = ReportService._collect_interaction_data(classroom_id, start_time, end_time)
        quiz_data = ReportService._collect_quiz_data(classroom_id, start_time, end_time)
        device_data = ReportService._collect_device_data(classroom_id, start_time, end_time)
        
        return jsonify({
            'success': True,
            'data': {
                'basic': basic_data,
                'attendance': attendance_data,
                'interaction': interaction_data,
                'quiz': quiz_data,
                'device': device_data,
                'time_range': {
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat()
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取统计数据失败: {str(e)}'
        }), 500


# 错误处理
@report_bp.errorhandler(SQLAlchemyError)
def handle_db_error(error):
    """处理数据库错误"""
    db.session.rollback()
    return jsonify({
        'success': False,
        'message': '数据库操作失败'
    }), 500


@report_bp.errorhandler(404)
def handle_not_found(error):
    """处理404错误"""
    return jsonify({
        'success': False,
        'message': '资源不存在'
    }), 404


@report_bp.errorhandler(500)
def handle_internal_error(error):
    """处理500错误"""
    return jsonify({
        'success': False,
        'message': '服务器内部错误'
    }), 500