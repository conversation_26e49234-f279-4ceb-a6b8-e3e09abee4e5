#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 系统管理API
"""

from flask import Blueprint, jsonify, request
from flask_socketio import emit
import logging
from datetime import datetime

from services.system_integration import get_integration_manager
from services.performance_monitor import get_performance_monitor
from services.config_manager import get_config_manager
from services.logging_service import get_logging_service
from services.stress_test import get_stress_test_service, LoadTestConfig

# 创建蓝图
system_bp = Blueprint('system', __name__, url_prefix='/api/system')
logger = logging.getLogger(__name__)

@system_bp.route('/status', methods=['GET'])
def get_system_status():
    """获取系统状态"""
    try:
        integration_manager = get_integration_manager()
        performance_monitor = get_performance_monitor()
        
        system_status = integration_manager.get_system_status()
        performance_summary = performance_monitor.get_performance_summary()
        
        return jsonify({
            'success': True,
            'data': {
                'system': system_status,
                'performance': performance_summary,
                'timestamp': datetime.now().isoformat()
            }
        })
    
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/services', methods=['GET'])
def get_services_status():
    """获取服务状态"""
    try:
        integration_manager = get_integration_manager()
        system_status = integration_manager.get_system_status()
        
        return jsonify({
            'success': True,
            'data': system_status['services']
        })
    
    except Exception as e:
        logger.error(f"获取服务状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/services/<service_name>/restart', methods=['POST'])
def restart_service(service_name):
    """重启服务"""
    try:
        integration_manager = get_integration_manager()
        
        # 这里应该实现服务重启逻辑
        # integration_manager.restart_service(service_name)
        
        return jsonify({
            'success': True,
            'message': f'服务 {service_name} 重启请求已提交'
        })
    
    except Exception as e:
        logger.error(f"重启服务失败 {service_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/performance/metrics', methods=['GET'])
def get_performance_metrics():
    """获取性能指标"""
    try:
        performance_monitor = get_performance_monitor()
        current_metrics = performance_monitor.get_current_metrics()
        
        metrics_data = {}
        for name, metric in current_metrics.items():
            metrics_data[name] = {
                'value': metric.value,
                'unit': metric.unit,
                'status': metric.status,
                'timestamp': metric.timestamp.isoformat()
            }
        
        return jsonify({
            'success': True,
            'data': metrics_data
        })
    
    except Exception as e:
        logger.error(f"获取性能指标失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/performance/history/<metric_name>', methods=['GET'])
def get_metric_history(metric_name):
    """获取指标历史"""
    try:
        duration = request.args.get('duration', 60, type=int)
        performance_monitor = get_performance_monitor()
        
        history = performance_monitor.get_metric_history(metric_name, duration)
        
        history_data = []
        for metric in history:
            history_data.append({
                'timestamp': metric.timestamp.isoformat(),
                'value': metric.value,
                'status': metric.status
            })
        
        return jsonify({
            'success': True,
            'data': {
                'metric_name': metric_name,
                'duration_minutes': duration,
                'history': history_data
            }
        })
    
    except Exception as e:
        logger.error(f"获取指标历史失败 {metric_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/performance/statistics/<metric_name>', methods=['GET'])
def get_metric_statistics(metric_name):
    """获取指标统计"""
    try:
        duration = request.args.get('duration', 60, type=int)
        performance_monitor = get_performance_monitor()
        
        statistics = performance_monitor.get_statistics(metric_name, duration)
        
        return jsonify({
            'success': True,
            'data': {
                'metric_name': metric_name,
                'duration_minutes': duration,
                'statistics': statistics
            }
        })
    
    except Exception as e:
        logger.error(f"获取指标统计失败 {metric_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/performance/report', methods=['GET'])
def get_performance_report():
    """获取性能报告"""
    try:
        integration_manager = get_integration_manager()
        performance_report = integration_manager.get_performance_report()
        
        return jsonify({
            'success': True,
            'data': performance_report
        })
    
    except Exception as e:
        logger.error(f"获取性能报告失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/config', methods=['GET'])
def get_config():
    """获取配置"""
    try:
        config_name = request.args.get('config', 'development')
        key_path = request.args.get('key')
        
        config_manager = get_config_manager()
        config_data = config_manager.get_config(config_name, key_path)
        
        return jsonify({
            'success': True,
            'data': {
                'config_name': config_name,
                'key_path': key_path,
                'value': config_data
            }
        })
    
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/config', methods=['POST'])
def update_config():
    """更新配置"""
    try:
        data = request.get_json()
        config_name = data.get('config_name')
        key_path = data.get('key_path')
        value = data.get('value')
        
        if not all([config_name, key_path, value is not None]):
            return jsonify({
                'success': False,
                'error': '缺少必要参数'
            }), 400
        
        config_manager = get_config_manager()
        config_manager.set_config(config_name, key_path, value)
        
        return jsonify({
            'success': True,
            'message': '配置已更新'
        })
    
    except Exception as e:
        logger.error(f"更新配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/config/validate', methods=['POST'])
def validate_config():
    """验证配置"""
    try:
        data = request.get_json()
        config_name = data.get('config_name')
        
        if not config_name:
            return jsonify({
                'success': False,
                'error': '缺少配置名称'
            }), 400
        
        config_manager = get_config_manager()
        validation_result = config_manager.validate_config(config_name)
        
        return jsonify({
            'success': True,
            'data': validation_result
        })
    
    except Exception as e:
        logger.error(f"验证配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/logs', methods=['GET'])
def get_log_files():
    """获取日志文件列表"""
    try:
        logging_service = get_logging_service()
        log_files = logging_service.get_log_files()
        
        return jsonify({
            'success': True,
            'data': log_files
        })
    
    except Exception as e:
        logger.error(f"获取日志文件失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/logs/<filename>', methods=['GET'])
def get_log_content(filename):
    """获取日志文件内容"""
    try:
        lines = request.args.get('lines', 100, type=int)
        logging_service = get_logging_service()
        
        log_content = logging_service.read_log_file(filename, lines)
        
        return jsonify({
            'success': True,
            'data': {
                'filename': filename,
                'lines': lines,
                'content': log_content
            }
        })
    
    except Exception as e:
        logger.error(f"获取日志内容失败 {filename}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/logs/search', methods=['POST'])
def search_logs():
    """搜索日志"""
    try:
        data = request.get_json()
        keyword = data.get('keyword')
        filename = data.get('filename')
        max_results = data.get('max_results', 100)
        
        if not keyword:
            return jsonify({
                'success': False,
                'error': '缺少搜索关键词'
            }), 400
        
        logging_service = get_logging_service()
        search_results = logging_service.search_logs(keyword, filename, max_results)
        
        return jsonify({
            'success': True,
            'data': {
                'keyword': keyword,
                'filename': filename,
                'results': search_results
            }
        })
    
    except Exception as e:
        logger.error(f"搜索日志失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/logs/statistics', methods=['GET'])
def get_log_statistics():
    """获取日志统计"""
    try:
        logging_service = get_logging_service()
        statistics = logging_service.get_log_statistics()
        
        return jsonify({
            'success': True,
            'data': statistics
        })
    
    except Exception as e:
        logger.error(f"获取日志统计失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/stress-test/start', methods=['POST'])
def start_stress_test():
    """启动压力测试"""
    try:
        data = request.get_json()
        test_type = data.get('test_type', 'api')
        
        stress_test_service = get_stress_test_service()
        
        if test_type == 'api':
            config = LoadTestConfig(
                concurrent_users=data.get('concurrent_users', 10),
                test_duration=data.get('test_duration', 60),
                ramp_up_time=data.get('ramp_up_time', 10)
            )
            result = stress_test_service.run_api_load_test(config)
        elif test_type == 'websocket':
            result = stress_test_service.run_websocket_load_test(
                concurrent_connections=data.get('concurrent_connections', 50),
                test_duration=data.get('test_duration', 60)
            )
        elif test_type == 'video':
            result = stress_test_service.run_video_stream_test(
                stream_count=data.get('stream_count', 8),
                test_duration=data.get('test_duration', 60)
            )
        elif test_type == 'comprehensive':
            result = stress_test_service.run_comprehensive_test()
        else:
            return jsonify({
                'success': False,
                'error': '不支持的测试类型'
            }), 400
        
        return jsonify({
            'success': True,
            'data': {
                'test_type': test_type,
                'result': result.__dict__ if hasattr(result, '__dict__') else result
            }
        })
    
    except Exception as e:
        logger.error(f"启动压力测试失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/stress-test/status', methods=['GET'])
def get_stress_test_status():
    """获取压力测试状态"""
    try:
        stress_test_service = get_stress_test_service()
        status = stress_test_service.get_test_status()
        
        return jsonify({
            'success': True,
            'data': status
        })
    
    except Exception as e:
        logger.error(f"获取压力测试状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/stress-test/stop', methods=['POST'])
def stop_stress_test():
    """停止压力测试"""
    try:
        stress_test_service = get_stress_test_service()
        stress_test_service.stop_test()
        
        return jsonify({
            'success': True,
            'message': '压力测试已停止'
        })
    
    except Exception as e:
        logger.error(f"停止压力测试失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/stress-test/history', methods=['GET'])
def get_stress_test_history():
    """获取压力测试历史"""
    try:
        stress_test_service = get_stress_test_service()
        history = stress_test_service.get_test_history()
        
        return jsonify({
            'success': True,
            'data': history
        })
    
    except Exception as e:
        logger.error(f"获取压力测试历史失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@system_bp.route('/health', methods=['GET'])
def system_health_check():
    """系统健康检查"""
    try:
        integration_manager = get_integration_manager()
        performance_monitor = get_performance_monitor()
        
        system_status = integration_manager.get_system_status()
        current_metrics = performance_monitor.get_current_metrics()
        
        # 检查关键指标
        health_status = "healthy"
        issues = []
        
        # 检查服务状态
        for service_name, service_info in system_status['services'].items():
            if service_info['status'] != 'running':
                health_status = "degraded"
                issues.append(f"服务 {service_name} 状态异常: {service_info['status']}")
        
        # 检查性能指标
        for metric_name, metric in current_metrics.items():
            if metric.status == 'critical':
                health_status = "unhealthy"
                issues.append(f"性能指标 {metric_name} 严重异常: {metric.value}{metric.unit}")
            elif metric.status == 'warning' and health_status == "healthy":
                health_status = "degraded"
                issues.append(f"性能指标 {metric_name} 警告: {metric.value}{metric.unit}")
        
        return jsonify({
            'success': True,
            'data': {
                'status': health_status,
                'timestamp': datetime.now().isoformat(),
                'uptime': system_status.get('uptime', 0),
                'issues': issues,
                'services_count': len(system_status['services']),
                'metrics_count': len(current_metrics)
            }
        })
    
    except Exception as e:
        logger.error(f"系统健康检查失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'status': 'unhealthy'
        }), 500

def register_system_websocket_events(socketio):
    """注册系统管理WebSocket事件"""
    
    @socketio.on('system_status_subscribe')
    def handle_system_status_subscribe():
        """订阅系统状态更新"""
        try:
            integration_manager = get_integration_manager()
            system_status = integration_manager.get_system_status()
            
            emit('system_status_update', {
                'success': True,
                'data': system_status
            })
            
        except Exception as e:
            logger.error(f"系统状态订阅失败: {e}")
            emit('system_status_update', {
                'success': False,
                'error': str(e)
            })
    
    @socketio.on('performance_metrics_subscribe')
    def handle_performance_metrics_subscribe():
        """订阅性能指标更新"""
        try:
            performance_monitor = get_performance_monitor()
            current_metrics = performance_monitor.get_current_metrics()
            
            metrics_data = {}
            for name, metric in current_metrics.items():
                metrics_data[name] = {
                    'value': metric.value,
                    'unit': metric.unit,
                    'status': metric.status,
                    'timestamp': metric.timestamp.isoformat()
                }
            
            emit('performance_metrics_update', {
                'success': True,
                'data': metrics_data
            })
            
        except Exception as e:
            logger.error(f"性能指标订阅失败: {e}")
            emit('performance_metrics_update', {
                'success': False,
                'error': str(e)
            })