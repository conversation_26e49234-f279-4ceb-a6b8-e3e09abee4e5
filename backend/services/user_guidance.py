#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 用户友好的错误提示和操作指导服务
"""

import logging
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from .error_handler import ErrorType, ErrorSeverity
from .logging_service import get_logging_service

class UserRole(Enum):
    """用户角色"""
    TEACHER = "teacher"
    STUDENT = "student"
    ADMIN = "admin"
    GUEST = "guest"

class GuidanceType(Enum):
    """指导类型"""
    ERROR_RESOLUTION = "error_resolution"
    OPERATION_GUIDE = "operation_guide"
    TROUBLESHOOTING = "troubleshooting"
    BEST_PRACTICE = "best_practice"

@dataclass
class UserGuidance:
    """用户指导"""
    guidance_id: str
    title: str
    description: str
    guidance_type: GuidanceType
    target_roles: List[UserRole]
    steps: List[str]
    tips: List[str]
    warnings: List[str]
    related_errors: List[str]
    priority: int = 1  # 1-5, 5最高
    tags: List[str] = None

class UserGuidanceService:
    """用户指导服务"""
    
    def __init__(self):
        self.logger = logging.getLogger('smart_classroom.user_guidance')
        self.logging_service = get_logging_service()
        
        # 错误消息映射
        self.error_messages: Dict[str, Dict[str, str]] = {}
        
        # 用户指导库
        self.guidance_library: Dict[str, UserGuidance] = {}
        
        # 常见问题解答
        self.faq: Dict[str, Dict[str, Any]] = {}
        
        # 初始化默认内容
        self._initialize_error_messages()
        self._initialize_guidance_library()
        self._initialize_faq()
        
        self.logger.info("用户指导服务已初始化")
    
    def _initialize_error_messages(self):
        """初始化错误消息"""
        self.error_messages = {
            # 网络错误
            "network_connection_failed": {
                "title": "网络连接失败",
                "message": "无法连接到服务器，请检查网络连接",
                "user_message": "网络似乎出现了问题，请稍后重试或联系管理员",
                "technical_details": "网络连接超时或服务器不可达"
            },
            "device_offline": {
                "title": "设备离线",
                "message": "目标设备已离线或无法访问",
                "user_message": "设备暂时无法连接，请检查设备是否正常运行",
                "technical_details": "设备心跳超时或网络连接中断"
            },
            "websocket_disconnected": {
                "title": "实时连接中断",
                "message": "与服务器的实时连接已中断",
                "user_message": "实时功能暂时不可用，系统正在尝试重新连接",
                "technical_details": "WebSocket连接异常断开"
            },
            
            # 设备错误
            "device_not_found": {
                "title": "设备未找到",
                "message": "指定的设备不存在或未注册",
                "user_message": "找不到该设备，请确认设备已正确连接",
                "technical_details": "设备ID不存在于系统中"
            },
            "device_busy": {
                "title": "设备忙碌",
                "message": "设备正在被其他用户使用",
                "user_message": "设备正在使用中，请稍后再试",
                "technical_details": "设备状态为busy或locked"
            },
            "device_permission_denied": {
                "title": "设备权限不足",
                "message": "您没有操作此设备的权限",
                "user_message": "您没有权限操作此设备，请联系管理员",
                "technical_details": "用户权限验证失败"
            },
            
            # 视频错误
            "video_stream_failed": {
                "title": "视频流启动失败",
                "message": "无法启动视频流",
                "user_message": "视频功能暂时不可用，请稍后重试",
                "technical_details": "FFmpeg进程启动失败或MediaMTX服务异常"
            },
            "video_quality_poor": {
                "title": "视频质量较差",
                "message": "视频传输质量不佳",
                "user_message": "视频可能有些卡顿，请检查网络连接",
                "technical_details": "网络带宽不足或编码参数需要调整"
            },
            "camera_not_available": {
                "title": "摄像头不可用",
                "message": "无法访问摄像头设备",
                "user_message": "摄像头暂时不可用，请检查设备连接",
                "technical_details": "摄像头设备被占用或驱动异常"
            },
            
            # 数据库错误
            "database_connection_failed": {
                "title": "数据库连接失败",
                "message": "无法连接到数据库",
                "user_message": "系统暂时不可用，请稍后重试",
                "technical_details": "数据库连接超时或服务异常"
            },
            "data_save_failed": {
                "title": "数据保存失败",
                "message": "无法保存数据到数据库",
                "user_message": "保存失败，请重试或联系管理员",
                "technical_details": "数据库写入操作失败"
            },
            
            # 文件错误
            "file_upload_failed": {
                "title": "文件上传失败",
                "message": "文件上传过程中出现错误",
                "user_message": "文件上传失败，请检查文件大小和格式",
                "technical_details": "文件上传中断或服务器存储空间不足"
            },
            "file_not_found": {
                "title": "文件未找到",
                "message": "请求的文件不存在",
                "user_message": "文件不存在或已被删除",
                "technical_details": "文件路径无效或文件已被移除"
            },
            "file_permission_denied": {
                "title": "文件权限不足",
                "message": "没有访问文件的权限",
                "user_message": "您没有权限访问此文件",
                "technical_details": "文件访问权限验证失败"
            },
            
            # 系统错误
            "system_overload": {
                "title": "系统负载过高",
                "message": "系统当前负载较高",
                "user_message": "系统繁忙，请稍后重试",
                "technical_details": "CPU或内存使用率过高"
            },
            "service_unavailable": {
                "title": "服务不可用",
                "message": "请求的服务暂时不可用",
                "user_message": "服务暂时不可用，请稍后重试",
                "technical_details": "相关服务进程异常或正在维护"
            }
        }
    
    def _initialize_guidance_library(self):
        """初始化指导库"""
        guidance_data = [
            {
                "guidance_id": "network_troubleshooting",
                "title": "网络连接问题排查",
                "description": "当遇到网络连接问题时的排查步骤",
                "guidance_type": GuidanceType.TROUBLESHOOTING,
                "target_roles": [UserRole.TEACHER, UserRole.ADMIN],
                "steps": [
                    "检查网络连接是否正常",
                    "确认设备IP地址配置正确",
                    "检查防火墙设置是否阻止连接",
                    "重启网络设备和应用程序",
                    "联系网络管理员检查网络状态"
                ],
                "tips": [
                    "可以使用ping命令测试网络连通性",
                    "检查是否有其他设备能正常连接",
                    "确认网络端口没有被占用"
                ],
                "warnings": [
                    "不要随意修改网络配置",
                    "重启设备前请保存重要数据"
                ],
                "related_errors": ["network_connection_failed", "device_offline"],
                "priority": 5
            },
            {
                "guidance_id": "device_connection_guide",
                "title": "设备连接指南",
                "description": "如何正确连接和配置设备",
                "guidance_type": GuidanceType.OPERATION_GUIDE,
                "target_roles": [UserRole.TEACHER, UserRole.ADMIN],
                "steps": [
                    "确保设备已正确连接到网络",
                    "启动设备上的客户端应用程序",
                    "等待设备自动发现和注册",
                    "在教师端确认设备连接状态",
                    "测试设备基本功能是否正常"
                ],
                "tips": [
                    "设备首次连接可能需要几分钟时间",
                    "确保所有设备在同一网络段内",
                    "定期检查设备连接状态"
                ],
                "warnings": [
                    "不要在课堂进行中重启设备",
                    "确保设备电源稳定"
                ],
                "related_errors": ["device_not_found", "device_offline"],
                "priority": 4
            },
            {
                "guidance_id": "video_streaming_setup",
                "title": "视频流设置指南",
                "description": "如何配置和优化视频流功能",
                "guidance_type": GuidanceType.OPERATION_GUIDE,
                "target_roles": [UserRole.TEACHER, UserRole.ADMIN],
                "steps": [
                    "检查摄像头和屏幕捕获设备",
                    "配置视频分辨率和帧率",
                    "测试视频流质量",
                    "调整网络带宽设置",
                    "启动视频广播功能"
                ],
                "tips": [
                    "较低的分辨率可以提高传输稳定性",
                    "确保网络带宽足够支持多路视频流",
                    "定期检查视频延迟情况"
                ],
                "warnings": [
                    "高分辨率视频会消耗更多带宽",
                    "避免同时启动过多视频流"
                ],
                "related_errors": ["video_stream_failed", "video_quality_poor"],
                "priority": 4
            },
            {
                "guidance_id": "file_management_best_practices",
                "title": "文件管理最佳实践",
                "description": "如何有效管理和分发课堂文件",
                "guidance_type": GuidanceType.BEST_PRACTICE,
                "target_roles": [UserRole.TEACHER],
                "steps": [
                    "按课程和日期组织文件结构",
                    "使用描述性的文件名",
                    "定期清理不需要的文件",
                    "备份重要的课堂资料",
                    "设置合适的文件访问权限"
                ],
                "tips": [
                    "支持的文件格式包括PDF、PPT、图片等",
                    "大文件建议压缩后上传",
                    "可以批量分发文件到多个小组"
                ],
                "warnings": [
                    "注意文件大小限制",
                    "不要上传包含敏感信息的文件"
                ],
                "related_errors": ["file_upload_failed", "file_not_found"],
                "priority": 3
            },
            {
                "guidance_id": "system_performance_optimization",
                "title": "系统性能优化",
                "description": "如何优化系统性能和稳定性",
                "guidance_type": GuidanceType.BEST_PRACTICE,
                "target_roles": [UserRole.ADMIN],
                "steps": [
                    "监控系统资源使用情况",
                    "定期清理临时文件和日志",
                    "优化数据库性能",
                    "调整服务配置参数",
                    "制定系统维护计划"
                ],
                "tips": [
                    "使用系统监控面板查看实时状态",
                    "设置资源使用告警阈值",
                    "定期备份重要数据"
                ],
                "warnings": [
                    "不要在课堂时间进行系统维护",
                    "修改配置前请备份原始设置"
                ],
                "related_errors": ["system_overload", "service_unavailable"],
                "priority": 5
            },
            {
                "guidance_id": "emergency_procedures",
                "title": "紧急情况处理程序",
                "description": "当系统出现严重问题时的应急处理",
                "guidance_type": GuidanceType.ERROR_RESOLUTION,
                "target_roles": [UserRole.TEACHER, UserRole.ADMIN],
                "steps": [
                    "保持冷静，不要慌张",
                    "记录错误信息和发生时间",
                    "尝试重启相关服务或设备",
                    "切换到备用方案继续教学",
                    "及时联系技术支持人员"
                ],
                "tips": [
                    "准备备用的教学方案",
                    "熟悉系统的基本操作",
                    "保存技术支持联系方式"
                ],
                "warnings": [
                    "不要随意修改系统设置",
                    "避免强制关闭正在运行的程序"
                ],
                "related_errors": ["system_overload", "database_connection_failed"],
                "priority": 5
            }
        ]
        
        for data in guidance_data:
            guidance = UserGuidance(
                guidance_id=data["guidance_id"],
                title=data["title"],
                description=data["description"],
                guidance_type=data["guidance_type"],
                target_roles=data["target_roles"],
                steps=data["steps"],
                tips=data["tips"],
                warnings=data["warnings"],
                related_errors=data["related_errors"],
                priority=data["priority"],
                tags=data.get("tags", [])
            )
            self.guidance_library[guidance.guidance_id] = guidance
    
    def _initialize_faq(self):
        """初始化常见问题解答"""
        self.faq = {
            "device_connection": {
                "question": "设备无法连接怎么办？",
                "answer": "首先检查网络连接，确保设备和服务器在同一网络中。然后重启设备应用程序，等待自动重连。如果问题持续，请联系管理员。",
                "category": "设备管理",
                "keywords": ["连接", "设备", "网络"],
                "related_guidance": ["network_troubleshooting", "device_connection_guide"]
            },
            "video_lag": {
                "question": "视频播放卡顿怎么解决？",
                "answer": "视频卡顿通常是网络带宽不足造成的。可以尝试降低视频质量，关闭不必要的应用程序，或者检查网络连接状态。",
                "category": "视频功能",
                "keywords": ["视频", "卡顿", "延迟"],
                "related_guidance": ["video_streaming_setup"]
            },
            "file_upload_slow": {
                "question": "文件上传很慢怎么办？",
                "answer": "大文件上传速度取决于网络带宽。建议压缩文件后上传，或者选择网络较好的时间段进行上传。",
                "category": "文件管理",
                "keywords": ["文件", "上传", "速度"],
                "related_guidance": ["file_management_best_practices"]
            },
            "system_slow": {
                "question": "系统响应很慢怎么办？",
                "answer": "系统响应慢可能是由于负载过高。请关闭不必要的功能，等待系统负载降低。如果问题持续，请联系管理员。",
                "category": "系统性能",
                "keywords": ["系统", "慢", "性能"],
                "related_guidance": ["system_performance_optimization"]
            },
            "data_lost": {
                "question": "数据丢失了怎么恢复？",
                "answer": "系统会自动备份重要数据。请联系管理员查看备份记录，可能可以恢复丢失的数据。",
                "category": "数据管理",
                "keywords": ["数据", "丢失", "恢复"],
                "related_guidance": ["emergency_procedures"]
            }
        }
    
    def get_user_friendly_error(self, error_key: str, user_role: UserRole = UserRole.TEACHER) -> Dict[str, str]:
        """获取用户友好的错误信息"""
        if error_key not in self.error_messages:
            return {
                "title": "未知错误",
                "message": "系统遇到了未知问题",
                "user_message": "系统出现问题，请稍后重试或联系管理员",
                "technical_details": f"错误代码: {error_key}"
            }
        
        error_info = self.error_messages[error_key].copy()
        
        # 根据用户角色调整消息内容
        if user_role == UserRole.STUDENT:
            # 学生用户看到更简化的消息
            error_info["message"] = error_info["user_message"]
        elif user_role == UserRole.ADMIN:
            # 管理员用户可以看到技术细节
            error_info["message"] += f" ({error_info['technical_details']})"
        
        return error_info
    
    def get_error_guidance(self, error_key: str, user_role: UserRole = UserRole.TEACHER) -> List[Dict]:
        """获取错误相关的指导"""
        related_guidance = []
        
        for guidance in self.guidance_library.values():
            if (error_key in guidance.related_errors and 
                user_role in guidance.target_roles):
                
                related_guidance.append({
                    "guidance_id": guidance.guidance_id,
                    "title": guidance.title,
                    "description": guidance.description,
                    "guidance_type": guidance.guidance_type.value,
                    "priority": guidance.priority,
                    "steps": guidance.steps,
                    "tips": guidance.tips,
                    "warnings": guidance.warnings
                })
        
        # 按优先级排序
        related_guidance.sort(key=lambda x: x["priority"], reverse=True)
        return related_guidance
    
    def get_operation_guide(self, guidance_id: str, user_role: UserRole = UserRole.TEACHER) -> Optional[Dict]:
        """获取操作指导"""
        if guidance_id not in self.guidance_library:
            return None
        
        guidance = self.guidance_library[guidance_id]
        
        # 检查用户角色权限
        if user_role not in guidance.target_roles:
            return None
        
        return {
            "guidance_id": guidance.guidance_id,
            "title": guidance.title,
            "description": guidance.description,
            "guidance_type": guidance.guidance_type.value,
            "steps": guidance.steps,
            "tips": guidance.tips,
            "warnings": guidance.warnings,
            "priority": guidance.priority
        }
    
    def search_guidance(self, keyword: str, user_role: UserRole = UserRole.TEACHER) -> List[Dict]:
        """搜索指导内容"""
        results = []
        keyword_lower = keyword.lower()
        
        for guidance in self.guidance_library.values():
            if user_role not in guidance.target_roles:
                continue
            
            # 搜索标题、描述和步骤
            if (keyword_lower in guidance.title.lower() or
                keyword_lower in guidance.description.lower() or
                any(keyword_lower in step.lower() for step in guidance.steps)):
                
                results.append({
                    "guidance_id": guidance.guidance_id,
                    "title": guidance.title,
                    "description": guidance.description,
                    "guidance_type": guidance.guidance_type.value,
                    "priority": guidance.priority
                })
        
        # 按优先级排序
        results.sort(key=lambda x: x["priority"], reverse=True)
        return results
    
    def get_faq(self, category: str = None, keyword: str = None) -> List[Dict]:
        """获取常见问题解答"""
        results = []
        
        for faq_id, faq_data in self.faq.items():
            # 分类过滤
            if category and faq_data["category"] != category:
                continue
            
            # 关键词过滤
            if keyword:
                keyword_lower = keyword.lower()
                if not (keyword_lower in faq_data["question"].lower() or
                       keyword_lower in faq_data["answer"].lower() or
                       any(keyword_lower in kw.lower() for kw in faq_data["keywords"])):
                    continue
            
            results.append({
                "faq_id": faq_id,
                "question": faq_data["question"],
                "answer": faq_data["answer"],
                "category": faq_data["category"],
                "keywords": faq_data["keywords"],
                "related_guidance": faq_data["related_guidance"]
            })
        
        return results
    
    def get_contextual_help(self, context: str, user_role: UserRole = UserRole.TEACHER) -> Dict:
        """获取上下文相关的帮助"""
        help_content = {
            "context": context,
            "guidance": [],
            "faq": [],
            "tips": []
        }
        
        # 根据上下文提供相关指导
        context_mapping = {
            "device_management": ["device_connection_guide", "network_troubleshooting"],
            "video_streaming": ["video_streaming_setup"],
            "file_management": ["file_management_best_practices"],
            "system_admin": ["system_performance_optimization", "emergency_procedures"]
        }
        
        if context in context_mapping:
            for guidance_id in context_mapping[context]:
                guidance = self.get_operation_guide(guidance_id, user_role)
                if guidance:
                    help_content["guidance"].append(guidance)
        
        # 获取相关FAQ
        context_keywords = {
            "device_management": "设备",
            "video_streaming": "视频",
            "file_management": "文件",
            "system_admin": "系统"
        }
        
        if context in context_keywords:
            help_content["faq"] = self.get_faq(keyword=context_keywords[context])
        
        # 添加通用提示
        general_tips = {
            "device_management": [
                "定期检查设备连接状态",
                "确保所有设备在同一网络中",
                "遇到问题时先尝试重启设备"
            ],
            "video_streaming": [
                "网络带宽影响视频质量",
                "可以调整视频分辨率优化性能",
                "避免同时开启过多视频流"
            ],
            "file_management": [
                "使用描述性文件名便于管理",
                "定期清理不需要的文件",
                "大文件建议压缩后上传"
            ],
            "system_admin": [
                "定期监控系统性能",
                "及时备份重要数据",
                "制定应急处理预案"
            ]
        }
        
        if context in general_tips:
            help_content["tips"] = general_tips[context]
        
        return help_content
    
    def log_user_help_request(self, user_id: str, help_type: str, context: str):
        """记录用户帮助请求"""
        self.logging_service.log_user_action(
            user_id=user_id,
            action="help_request",
            resource=context,
            help_type=help_type
        )
    
    def get_help_statistics(self) -> Dict:
        """获取帮助使用统计"""
        # 这里可以从日志中统计帮助使用情况
        # 暂时返回模拟数据
        return {
            "total_help_requests": 0,
            "popular_topics": [],
            "common_errors": [],
            "user_feedback": []
        }

# 全局用户指导服务实例
_user_guidance_service = None

def get_user_guidance_service() -> UserGuidanceService:
    """获取用户指导服务实例"""
    global _user_guidance_service
    if _user_guidance_service is None:
        _user_guidance_service = UserGuidanceService()
    return _user_guidance_service