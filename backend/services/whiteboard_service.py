#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
白板服务
"""

import uuid
import json
import logging
from datetime import datetime
from typing import List, Dict, Optional, Tuple

from backend.models import db
from backend.models.whiteboard import Whiteboard, WhiteboardVersion
from backend.models.group import Group
from backend.models.classroom import Classroom

logger = logging.getLogger(__name__)

class WhiteboardService:
    """白板服务类"""
    
    @staticmethod
    def create_whiteboard(name: str, created_by: str, group_id: str = None, classroom_id: str = None) -> Tuple[bool, str, Optional[Dict]]:
        """创建白板"""
        try:
            # 验证参数
            if not name or not created_by:
                return False, "白板名称和创建者不能为空", None
            
            # 验证小组或课堂存在
            if group_id:
                group = Group.query.filter_by(group_id=group_id).first()
                if not group:
                    return False, "指定的小组不存在", None
                    
            if classroom_id:
                classroom = Classroom.query.filter_by(classroom_id=classroom_id).first()
                if not classroom:
                    return False, "指定的课堂不存在", None
            
            # 创建白板
            whiteboard_id = str(uuid.uuid4())
            whiteboard = Whiteboard(
                id=whiteboard_id,
                name=name,
                group_id=group_id,
                classroom_id=classroom_id,
                created_by=created_by
            )
            
            db.session.add(whiteboard)
            db.session.commit()
            
            # 创建初始版本
            initial_version = WhiteboardVersion(
                id=str(uuid.uuid4()),
                whiteboard_id=whiteboard_id,
                version_number=1,
                content={"elements": [], "appState": {"viewBackgroundColor": "#ffffff"}},
                created_by=created_by,
                comment="初始版本"
            )
            
            db.session.add(initial_version)
            db.session.commit()
            
            logger.info(f"创建白板成功: {whiteboard_id}")
            
            return True, "创建成功", {
                "whiteboard_id": whiteboard_id,
                "name": name,
                "group_id": group_id,
                "classroom_id": classroom_id,
                "created_by": created_by,
                "created_at": whiteboard.created_at.isoformat(),
                "version": 1
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"创建白板失败: {str(e)}")
            return False, f"创建失败: {str(e)}", None
    
    @staticmethod
    def get_whiteboard(whiteboard_id: str) -> Tuple[bool, str, Optional[Dict]]:
        """获取白板信息"""
        try:
            whiteboard = Whiteboard.query.filter_by(id=whiteboard_id, is_active=True).first()
            if not whiteboard:
                return False, "白板不存在", None
            
            # 获取最新版本
            latest_version = WhiteboardVersion.query.filter_by(whiteboard_id=whiteboard_id)\
                                                  .order_by(WhiteboardVersion.version_number.desc())\
                                                  .first()
            
            return True, "获取成功", {
                "whiteboard_id": whiteboard.id,
                "name": whiteboard.name,
                "group_id": whiteboard.group_id,
                "classroom_id": whiteboard.classroom_id,
                "created_by": whiteboard.created_by,
                "created_at": whiteboard.created_at.isoformat(),
                "updated_at": whiteboard.updated_at.isoformat(),
                "current_version": latest_version.version_number if latest_version else 1,
                "content": latest_version.content if latest_version else {"elements": [], "appState": {"viewBackgroundColor": "#ffffff"}}
            }
            
        except Exception as e:
            logger.error(f"获取白板失败: {str(e)}")
            return False, f"获取失败: {str(e)}", None
    
    @staticmethod
    def update_whiteboard_content(whiteboard_id: str, content: Dict, updated_by: str, comment: str = None) -> Tuple[bool, str, Optional[Dict]]:
        """更新白板内容"""
        try:
            whiteboard = Whiteboard.query.filter_by(id=whiteboard_id, is_active=True).first()
            if not whiteboard:
                return False, "白板不存在", None
            
            # 获取当前最新版本号
            latest_version = WhiteboardVersion.query.filter_by(whiteboard_id=whiteboard_id)\
                                                  .order_by(WhiteboardVersion.version_number.desc())\
                                                  .first()
            
            new_version_number = (latest_version.version_number + 1) if latest_version else 1
            
            # 创建新版本
            new_version = WhiteboardVersion(
                id=str(uuid.uuid4()),
                whiteboard_id=whiteboard_id,
                version_number=new_version_number,
                content=content,
                created_by=updated_by,
                comment=comment or f"版本 {new_version_number}"
            )
            
            db.session.add(new_version)
            
            # 更新白板的更新时间
            whiteboard.updated_at = datetime.now()
            db.session.commit()
            
            logger.info(f"更新白板内容成功: {whiteboard_id}, 版本: {new_version_number}")
            
            return True, "更新成功", {
                "whiteboard_id": whiteboard_id,
                "version": new_version_number,
                "updated_by": updated_by,
                "updated_at": new_version.created_at.isoformat()
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新白板内容失败: {str(e)}")
            return False, f"更新失败: {str(e)}", None
    
    @staticmethod
    def get_whiteboard_versions(whiteboard_id: str, limit: int = 10) -> Tuple[bool, str, Optional[List[Dict]]]:
        """获取白板版本历史"""
        try:
            whiteboard = Whiteboard.query.filter_by(id=whiteboard_id, is_active=True).first()
            if not whiteboard:
                return False, "白板不存在", None
            
            versions = WhiteboardVersion.query.filter_by(whiteboard_id=whiteboard_id)\
                                            .order_by(WhiteboardVersion.version_number.desc())\
                                            .limit(limit).all()
            
            version_list = []
            for version in versions:
                version_list.append({
                    "version_id": version.id,
                    "version_number": version.version_number,
                    "created_by": version.created_by,
                    "created_at": version.created_at.isoformat(),
                    "comment": version.comment,
                    "thumbnail": version.thumbnail
                })
            
            return True, "获取成功", version_list
            
        except Exception as e:
            logger.error(f"获取白板版本历史失败: {str(e)}")
            return False, f"获取失败: {str(e)}", None
    
    @staticmethod
    def get_version_content(version_id: str) -> Tuple[bool, str, Optional[Dict]]:
        """获取指定版本的内容"""
        try:
            version = WhiteboardVersion.query.filter_by(id=version_id).first()
            if not version:
                return False, "版本不存在", None
            
            return True, "获取成功", {
                "version_id": version.id,
                "whiteboard_id": version.whiteboard_id,
                "version_number": version.version_number,
                "content": version.content,
                "created_by": version.created_by,
                "created_at": version.created_at.isoformat(),
                "comment": version.comment
            }
            
        except Exception as e:
            logger.error(f"获取版本内容失败: {str(e)}")
            return False, f"获取失败: {str(e)}", None
    
    @staticmethod
    def list_whiteboards(group_id: str = None, classroom_id: str = None, created_by: str = None) -> Tuple[bool, str, Optional[List[Dict]]]:
        """列出白板"""
        try:
            query = Whiteboard.query.filter_by(is_active=True)
            
            if group_id:
                query = query.filter_by(group_id=group_id)
            if classroom_id:
                query = query.filter_by(classroom_id=classroom_id)
            if created_by:
                query = query.filter_by(created_by=created_by)
            
            whiteboards = query.order_by(Whiteboard.updated_at.desc()).all()
            
            whiteboard_list = []
            for whiteboard in whiteboards:
                # 获取最新版本信息
                latest_version = WhiteboardVersion.query.filter_by(whiteboard_id=whiteboard.id)\
                                                       .order_by(WhiteboardVersion.version_number.desc())\
                                                       .first()
                
                whiteboard_list.append({
                    "whiteboard_id": whiteboard.id,
                    "name": whiteboard.name,
                    "group_id": whiteboard.group_id,
                    "classroom_id": whiteboard.classroom_id,
                    "created_by": whiteboard.created_by,
                    "created_at": whiteboard.created_at.isoformat(),
                    "updated_at": whiteboard.updated_at.isoformat(),
                    "current_version": latest_version.version_number if latest_version else 1,
                    "thumbnail": latest_version.thumbnail if latest_version else None
                })
            
            return True, "获取成功", whiteboard_list
            
        except Exception as e:
            logger.error(f"列出白板失败: {str(e)}")
            return False, f"获取失败: {str(e)}", None
    
    @staticmethod
    def delete_whiteboard(whiteboard_id: str, deleted_by: str) -> Tuple[bool, str]:
        """删除白板（软删除）"""
        try:
            whiteboard = Whiteboard.query.filter_by(id=whiteboard_id, is_active=True).first()
            if not whiteboard:
                return False, "白板不存在"
            
            # 软删除
            whiteboard.is_active = False
            whiteboard.updated_at = datetime.now()
            db.session.commit()
            
            logger.info(f"删除白板成功: {whiteboard_id}, 删除者: {deleted_by}")
            return True, "删除成功"
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"删除白板失败: {str(e)}")
            return False, f"删除失败: {str(e)}"
    
    @staticmethod
    def save_thumbnail(version_id: str, thumbnail_data: str) -> Tuple[bool, str]:
        """保存版本缩略图"""
        try:
            version = WhiteboardVersion.query.filter_by(id=version_id).first()
            if not version:
                return False, "版本不存在"
            
            version.thumbnail = thumbnail_data
            db.session.commit()
            
            return True, "保存成功"
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"保存缩略图失败: {str(e)}")
            return False, f"保存失败: {str(e)}"