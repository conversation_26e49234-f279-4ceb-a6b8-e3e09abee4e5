#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 日志服务
"""

import os
import logging
import logging.config
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional
import threading
import queue
import json

class LoggingService:
    """日志服务"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        self.initialized = False
        self.log_queue = queue.Queue()
        self.log_thread: Optional[threading.Thread] = None
        self.running = False
        
        # 初始化日志配置
        self._setup_logging()
        
        # 启动日志处理线程
        self._start_log_thread()
    
    def _setup_logging(self):
        """设置日志配置"""
        log_config = {
            'version': 1,
            'disable_existing_loggers': False,
            'formatters': {
                'standard': {
                    'format': '[%(levelname)s] %(asctime)s - %(name)s - %(message)s',
                    'datefmt': '%Y-%m-%d %H:%M:%S'
                },
                'detailed': {
                    'format': '[%(levelname)s] %(asctime)s - %(name)s:%(lineno)d - %(funcName)s() - %(message)s',
                    'datefmt': '%Y-%m-%d %H:%M:%S'
                },
                'json': {
                    'format': '%(message)s'
                }
            },
            'handlers': {
                'console': {
                    'class': 'logging.StreamHandler',
                    'level': 'INFO',
                    'formatter': 'standard',
                    'stream': 'ext://sys.stdout'
                },
                'file_info': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'level': 'INFO',
                    'formatter': 'detailed',
                    'filename': str(self.log_dir / 'smart_classroom.log'),
                    'maxBytes': 10485760,  # 10MB
                    'backupCount': 5,
                    'encoding': 'utf-8'
                },
                'file_error': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'level': 'ERROR',
                    'formatter': 'detailed',
                    'filename': str(self.log_dir / 'smart_classroom_error.log'),
                    'maxBytes': 10485760,  # 10MB
                    'backupCount': 5,
                    'encoding': 'utf-8'
                },
                'file_debug': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'level': 'DEBUG',
                    'formatter': 'detailed',
                    'filename': str(self.log_dir / 'smart_classroom_debug.log'),
                    'maxBytes': 10485760,  # 10MB
                    'backupCount': 3,
                    'encoding': 'utf-8'
                },
                'performance_file': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'level': 'INFO',
                    'formatter': 'json',
                    'filename': str(self.log_dir / 'performance.log'),
                    'maxBytes': 10485760,  # 10MB
                    'backupCount': 10,
                    'encoding': 'utf-8'
                },
                'audit_file': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'level': 'INFO',
                    'formatter': 'json',
                    'filename': str(self.log_dir / 'audit.log'),
                    'maxBytes': 10485760,  # 10MB
                    'backupCount': 20,
                    'encoding': 'utf-8'
                }
            },
            'loggers': {
                'smart_classroom': {
                    'level': 'DEBUG',
                    'handlers': ['console', 'file_info', 'file_error', 'file_debug'],
                    'propagate': False
                },
                'smart_classroom.performance': {
                    'level': 'INFO',
                    'handlers': ['performance_file'],
                    'propagate': False
                },
                'smart_classroom.audit': {
                    'level': 'INFO',
                    'handlers': ['audit_file'],
                    'propagate': False
                },
                'smart_classroom.device_discovery': {
                    'level': 'INFO',
                    'handlers': ['console', 'file_info'],
                    'propagate': False
                },
                'smart_classroom.video_service': {
                    'level': 'INFO',
                    'handlers': ['console', 'file_info'],
                    'propagate': False
                },
                'smart_classroom.websocket': {
                    'level': 'INFO',
                    'handlers': ['console', 'file_info'],
                    'propagate': False
                }
            },
            'root': {
                'level': 'INFO',
                'handlers': ['console']
            }
        }
        
        logging.config.dictConfig(log_config)
        self.initialized = True
        
        # 记录启动日志
        logger = logging.getLogger('smart_classroom')
        logger.info("日志服务已初始化")
    
    def _start_log_thread(self):
        """启动日志处理线程"""
        self.running = True
        self.log_thread = threading.Thread(
            target=self._log_processing_loop,
            daemon=True
        )
        self.log_thread.start()
    
    def _log_processing_loop(self):
        """日志处理循环"""
        while self.running:
            try:
                # 从队列获取日志记录
                log_record = self.log_queue.get(timeout=1)
                if log_record is None:
                    break
                
                # 处理日志记录
                self._process_log_record(log_record)
                
            except queue.Empty:
                continue
            except Exception as e:
                # 避免日志处理异常影响主程序
                print(f"日志处理异常: {e}")
    
    def _process_log_record(self, log_record: Dict[str, Any]):
        """处理日志记录"""
        # 这里可以添加额外的日志处理逻辑
        # 例如：发送到远程日志服务、数据库存储等
        pass
    
    def log_performance(self, metric_name: str, value: float, unit: str, **kwargs):
        """记录性能指标"""
        logger = logging.getLogger('smart_classroom.performance')
        
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'type': 'performance',
            'metric_name': metric_name,
            'value': value,
            'unit': unit,
            **kwargs
        }
        
        logger.info(json.dumps(log_data, ensure_ascii=False))
    
    def log_audit(self, action: str, user_id: str = None, resource: str = None, 
                  result: str = 'success', details: Dict[str, Any] = None):
        """记录审计日志"""
        logger = logging.getLogger('smart_classroom.audit')
        
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'type': 'audit',
            'action': action,
            'user_id': user_id,
            'resource': resource,
            'result': result,
            'details': details or {}
        }
        
        logger.info(json.dumps(log_data, ensure_ascii=False))
    
    def log_system_event(self, event_type: str, message: str, level: str = 'info', **kwargs):
        """记录系统事件"""
        logger = logging.getLogger('smart_classroom')
        
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'type': 'system_event',
            'event_type': event_type,
            'message': message,
            **kwargs
        }
        
        log_method = getattr(logger, level.lower(), logger.info)
        log_method(f"[{event_type}] {message}")
        
        # 添加到处理队列
        self.log_queue.put(log_data)
    
    def log_user_action(self, user_id: str, action: str, resource: str = None, 
                       result: str = 'success', **kwargs):
        """记录用户操作"""
        self.log_audit(
            action=action,
            user_id=user_id,
            resource=resource,
            result=result,
            details=kwargs
        )
    
    def log_error(self, error: Exception, context: Dict[str, Any] = None):
        """记录错误"""
        logger = logging.getLogger('smart_classroom')
        
        error_data = {
            'timestamp': datetime.now().isoformat(),
            'type': 'error',
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context or {}
        }
        
        logger.error(f"错误: {error}", exc_info=True)
        
        # 添加到处理队列
        self.log_queue.put(error_data)
    
    def get_log_files(self) -> Dict[str, Dict[str, Any]]:
        """获取日志文件信息"""
        log_files = {}
        
        for log_file in self.log_dir.glob('*.log'):
            try:
                stat = log_file.stat()
                log_files[log_file.name] = {
                    'path': str(log_file),
                    'size': stat.st_size,
                    'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    'size_mb': round(stat.st_size / 1024 / 1024, 2)
                }
            except Exception as e:
                log_files[log_file.name] = {
                    'error': str(e)
                }
        
        return log_files
    
    def read_log_file(self, filename: str, lines: int = 100) -> list:
        """读取日志文件"""
        log_file = self.log_dir / filename
        
        if not log_file.exists():
            return []
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                return all_lines[-lines:] if lines > 0 else all_lines
        except Exception as e:
            logger = logging.getLogger('smart_classroom')
            logger.error(f"读取日志文件失败 {filename}: {e}")
            return []
    
    def search_logs(self, keyword: str, filename: str = None, max_results: int = 100) -> list:
        """搜索日志"""
        results = []
        
        if filename:
            files_to_search = [self.log_dir / filename]
        else:
            files_to_search = list(self.log_dir.glob('*.log'))
        
        for log_file in files_to_search:
            if not log_file.exists():
                continue
            
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    for line_num, line in enumerate(f, 1):
                        if keyword.lower() in line.lower():
                            results.append({
                                'file': log_file.name,
                                'line_number': line_num,
                                'content': line.strip()
                            })
                            
                            if len(results) >= max_results:
                                return results
            except Exception as e:
                logger = logging.getLogger('smart_classroom')
                logger.error(f"搜索日志文件失败 {log_file}: {e}")
        
        return results
    
    def cleanup_old_logs(self, days: int = 30):
        """清理旧日志文件"""
        cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
        cleaned_files = []
        
        for log_file in self.log_dir.glob('*.log.*'):  # 轮转的日志文件
            try:
                if log_file.stat().st_mtime < cutoff_time:
                    log_file.unlink()
                    cleaned_files.append(str(log_file))
            except Exception as e:
                logger = logging.getLogger('smart_classroom')
                logger.error(f"清理日志文件失败 {log_file}: {e}")
        
        if cleaned_files:
            logger = logging.getLogger('smart_classroom')
            logger.info(f"已清理 {len(cleaned_files)} 个旧日志文件")
        
        return cleaned_files
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """获取日志统计"""
        stats = {
            'total_files': 0,
            'total_size': 0,
            'files': {},
            'log_levels': {
                'DEBUG': 0,
                'INFO': 0,
                'WARNING': 0,
                'ERROR': 0,
                'CRITICAL': 0
            }
        }
        
        for log_file in self.log_dir.glob('*.log'):
            try:
                stat = log_file.stat()
                stats['total_files'] += 1
                stats['total_size'] += stat.st_size
                
                stats['files'][log_file.name] = {
                    'size': stat.st_size,
                    'lines': 0
                }
                
                # 统计日志级别
                with open(log_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        stats['files'][log_file.name]['lines'] += 1
                        for level in stats['log_levels']:
                            if f'[{level}]' in line:
                                stats['log_levels'][level] += 1
                                break
                                
            except Exception as e:
                logger = logging.getLogger('smart_classroom')
                logger.error(f"统计日志文件失败 {log_file}: {e}")
        
        stats['total_size_mb'] = round(stats['total_size'] / 1024 / 1024, 2)
        return stats
    
    def export_logs(self, start_date: str = None, end_date: str = None, 
                   level: str = None, output_file: str = None) -> str:
        """导出日志"""
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = str(self.log_dir / f'export_{timestamp}.log')
        
        exported_lines = []
        
        for log_file in self.log_dir.glob('*.log'):
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        # 这里可以添加日期和级别过滤逻辑
                        if level and f'[{level.upper()}]' not in line:
                            continue
                        
                        exported_lines.append(line)
            except Exception as e:
                logger = logging.getLogger('smart_classroom')
                logger.error(f"导出日志文件失败 {log_file}: {e}")
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.writelines(exported_lines)
            
            logger = logging.getLogger('smart_classroom')
            logger.info(f"日志已导出到: {output_file}")
            return output_file
            
        except Exception as e:
            logger = logging.getLogger('smart_classroom')
            logger.error(f"导出日志失败: {e}")
            raise
    
    def stop(self):
        """停止日志服务"""
        self.running = False
        
        # 发送停止信号
        self.log_queue.put(None)
        
        # 等待线程结束
        if self.log_thread and self.log_thread.is_alive():
            self.log_thread.join(timeout=5)
        
        logger = logging.getLogger('smart_classroom')
        logger.info("日志服务已停止")

# 全局实例
_logging_service = None

def get_logging_service() -> LoggingService:
    """获取日志服务实例"""
    global _logging_service
    if _logging_service is None:
        _logging_service = LoggingService()
    return _logging_service

def setup_logging():
    """设置日志"""
    return get_logging_service()