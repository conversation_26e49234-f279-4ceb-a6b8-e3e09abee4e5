#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 性能监控服务
"""

import time
import threading
import psutil
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import deque
import statistics

@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    value: float
    unit: str
    timestamp: datetime
    threshold: Optional[float] = None
    status: str = "normal"  # normal, warning, critical

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_history_size: int = 1000):
        self.logger = logging.getLogger(__name__)
        self.max_history_size = max_history_size
        self.metrics_history: Dict[str, deque] = {}
        self.thresholds: Dict[str, Dict[str, float]] = {}
        self.callbacks: Dict[str, List[Callable]] = {}
        self.monitoring_thread: Optional[threading.Thread] = None
        self.running = False
        self.monitor_interval = 5  # 秒
        
        # 初始化默认阈值
        self._initialize_thresholds()
        
        # 初始化指标历史
        self._initialize_metrics()
    
    def _initialize_thresholds(self):
        """初始化性能阈值"""
        self.thresholds = {
            'cpu_usage': {'warning': 70.0, 'critical': 85.0},
            'memory_usage': {'warning': 75.0, 'critical': 90.0},
            'disk_usage': {'warning': 80.0, 'critical': 95.0},
            'network_latency': {'warning': 100.0, 'critical': 500.0},  # ms
            'response_time': {'warning': 1.0, 'critical': 3.0},  # seconds
            'active_connections': {'warning': 60, 'critical': 75},
            'video_stream_delay': {'warning': 0.8, 'critical': 1.2},  # seconds
            'broadcast_delay': {'warning': 1.2, 'critical': 1.8},  # seconds
            'frame_rate': {'warning': 25.0, 'critical': 20.0},  # fps (lower is worse)
            'packet_loss': {'warning': 1.0, 'critical': 5.0},  # percentage
        }
    
    def _initialize_metrics(self):
        """初始化指标历史记录"""
        metric_names = [
            'cpu_usage', 'memory_usage', 'disk_usage', 'network_latency',
            'response_time', 'active_connections', 'video_stream_delay',
            'broadcast_delay', 'frame_rate', 'packet_loss'
        ]
        
        for name in metric_names:
            self.metrics_history[name] = deque(maxlen=self.max_history_size)
    
    def start_monitoring(self):
        """开始监控"""
        if self.running:
            return
        
        self.running = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitoring_thread.start()
        self.logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=10)
        self.logger.info("性能监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.running:
            try:
                # 收集系统指标
                self._collect_system_metrics()
                
                # 收集应用指标
                self._collect_application_metrics()
                
                # 检查阈值
                self._check_thresholds()
                
            except Exception as e:
                self.logger.error(f"性能监控异常: {e}")
            
            time.sleep(self.monitor_interval)
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        now = datetime.now()
        
        # CPU使用率
        cpu_usage = psutil.cpu_percent(interval=1)
        self._add_metric('cpu_usage', cpu_usage, '%', now)
        
        # 内存使用率
        memory = psutil.virtual_memory()
        self._add_metric('memory_usage', memory.percent, '%', now)
        
        # 磁盘使用率
        disk = psutil.disk_usage('/')
        self._add_metric('disk_usage', disk.percent, '%', now)
        
        # 网络延迟（模拟）
        network_latency = self._measure_network_latency()
        self._add_metric('network_latency', network_latency, 'ms', now)
        
        # 活跃连接数
        connections = len(psutil.net_connections())
        self._add_metric('active_connections', connections, 'count', now)
    
    def _collect_application_metrics(self):
        """收集应用指标"""
        now = datetime.now()
        
        # 响应时间（通过健康检查测量）
        response_time = self._measure_response_time()
        self._add_metric('response_time', response_time, 's', now)
        
        # 视频流延迟（模拟）
        video_delay = self._measure_video_stream_delay()
        self._add_metric('video_stream_delay', video_delay, 's', now)
        
        # 广播延迟（模拟）
        broadcast_delay = self._measure_broadcast_delay()
        self._add_metric('broadcast_delay', broadcast_delay, 's', now)
        
        # 帧率（模拟）
        frame_rate = self._measure_frame_rate()
        self._add_metric('frame_rate', frame_rate, 'fps', now)
        
        # 丢包率（模拟）
        packet_loss = self._measure_packet_loss()
        self._add_metric('packet_loss', packet_loss, '%', now)
    
    def _add_metric(self, name: str, value: float, unit: str, timestamp: datetime):
        """添加指标"""
        if name not in self.metrics_history:
            self.metrics_history[name] = deque(maxlen=self.max_history_size)
        
        # 确定状态
        status = self._get_metric_status(name, value)
        
        metric = PerformanceMetric(
            name=name,
            value=value,
            unit=unit,
            timestamp=timestamp,
            threshold=self.thresholds.get(name, {}).get('critical'),
            status=status
        )
        
        self.metrics_history[name].append(metric)
    
    def _get_metric_status(self, name: str, value: float) -> str:
        """获取指标状态"""
        if name not in self.thresholds:
            return "normal"
        
        thresholds = self.thresholds[name]
        
        # 对于帧率，值越低越差
        if name == 'frame_rate':
            if value < thresholds.get('critical', 0):
                return "critical"
            elif value < thresholds.get('warning', 0):
                return "warning"
        else:
            # 对于其他指标，值越高越差
            if value > thresholds.get('critical', float('inf')):
                return "critical"
            elif value > thresholds.get('warning', float('inf')):
                return "warning"
        
        return "normal"
    
    def _measure_network_latency(self) -> float:
        """测量网络延迟"""
        try:
            import subprocess
            result = subprocess.run(
                ['ping', '-c', '1', '127.0.0.1'],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0:
                # 解析ping结果获取延迟
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'time=' in line:
                        time_str = line.split('time=')[1].split(' ')[0]
                        return float(time_str)
        except Exception:
            pass
        return 1.0  # 默认值
    
    def _measure_response_time(self) -> float:
        """测量响应时间"""
        try:
            import requests
            start_time = time.time()
            response = requests.get('http://localhost:5000/api/health', timeout=5)
            end_time = time.time()
            if response.status_code == 200:
                return end_time - start_time
        except Exception:
            pass
        return 0.1  # 默认值
    
    def _measure_video_stream_delay(self) -> float:
        """测量视频流延迟"""
        # 这里应该实现实际的视频流延迟测量
        # 目前返回模拟值
        import random
        return random.uniform(0.5, 1.0)
    
    def _measure_broadcast_delay(self) -> float:
        """测量广播延迟"""
        # 这里应该实现实际的广播延迟测量
        # 目前返回模拟值
        import random
        return random.uniform(0.8, 1.5)
    
    def _measure_frame_rate(self) -> float:
        """测量帧率"""
        # 这里应该实现实际的帧率测量
        # 目前返回模拟值
        import random
        return random.uniform(25, 30)
    
    def _measure_packet_loss(self) -> float:
        """测量丢包率"""
        # 这里应该实现实际的丢包率测量
        # 目前返回模拟值
        import random
        return random.uniform(0, 2)
    
    def _check_thresholds(self):
        """检查阈值"""
        for name, history in self.metrics_history.items():
            if not history:
                continue
            
            latest_metric = history[-1]
            if latest_metric.status in ['warning', 'critical']:
                self._trigger_alert(latest_metric)
    
    def _trigger_alert(self, metric: PerformanceMetric):
        """触发告警"""
        message = f"性能告警: {metric.name} = {metric.value}{metric.unit} ({metric.status})"
        
        if metric.status == 'critical':
            self.logger.error(message)
        else:
            self.logger.warning(message)
        
        # 调用回调函数
        if metric.name in self.callbacks:
            for callback in self.callbacks[metric.name]:
                try:
                    callback(metric)
                except Exception as e:
                    self.logger.error(f"告警回调异常: {e}")
    
    def add_callback(self, metric_name: str, callback: Callable):
        """添加告警回调"""
        if metric_name not in self.callbacks:
            self.callbacks[metric_name] = []
        self.callbacks[metric_name].append(callback)
    
    def set_threshold(self, metric_name: str, warning: float, critical: float):
        """设置阈值"""
        self.thresholds[metric_name] = {
            'warning': warning,
            'critical': critical
        }
    
    def get_current_metrics(self) -> Dict[str, PerformanceMetric]:
        """获取当前指标"""
        current_metrics = {}
        for name, history in self.metrics_history.items():
            if history:
                current_metrics[name] = history[-1]
        return current_metrics
    
    def get_metric_history(self, metric_name: str, duration_minutes: int = 60) -> List[PerformanceMetric]:
        """获取指标历史"""
        if metric_name not in self.metrics_history:
            return []
        
        cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
        history = self.metrics_history[metric_name]
        
        return [metric for metric in history if metric.timestamp >= cutoff_time]
    
    def get_statistics(self, metric_name: str, duration_minutes: int = 60) -> Dict[str, float]:
        """获取指标统计"""
        history = self.get_metric_history(metric_name, duration_minutes)
        if not history:
            return {}
        
        values = [metric.value for metric in history]
        
        return {
            'count': len(values),
            'min': min(values),
            'max': max(values),
            'mean': statistics.mean(values),
            'median': statistics.median(values),
            'std_dev': statistics.stdev(values) if len(values) > 1 else 0
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        summary = {
            'timestamp': datetime.now().isoformat(),
            'monitoring_status': 'running' if self.running else 'stopped',
            'metrics': {},
            'alerts': {
                'warning': 0,
                'critical': 0
            }
        }
        
        current_metrics = self.get_current_metrics()
        for name, metric in current_metrics.items():
            summary['metrics'][name] = {
                'value': metric.value,
                'unit': metric.unit,
                'status': metric.status,
                'timestamp': metric.timestamp.isoformat()
            }
            
            if metric.status == 'warning':
                summary['alerts']['warning'] += 1
            elif metric.status == 'critical':
                summary['alerts']['critical'] += 1
        
        return summary
    
    def export_metrics(self, filename: str, duration_minutes: int = 60):
        """导出指标数据"""
        import csv
        
        cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['timestamp', 'metric_name', 'value', 'unit', 'status']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for name, history in self.metrics_history.items():
                for metric in history:
                    if metric.timestamp >= cutoff_time:
                        writer.writerow({
                            'timestamp': metric.timestamp.isoformat(),
                            'metric_name': metric.name,
                            'value': metric.value,
                            'unit': metric.unit,
                            'status': metric.status
                        })
        
        self.logger.info(f"指标数据已导出到: {filename}")

# 全局实例
_performance_monitor = None

def get_performance_monitor() -> PerformanceMonitor:
    """获取性能监控器实例"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor