#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 系统状态监控和告警服务
"""

import psutil
import threading
import time
import logging
import json
import socket
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, field
from enum import Enum
import queue

from .error_handler import get_error_handler, ErrorType, ErrorSeverity
from .logging_service import get_logging_service

class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class MonitorType(Enum):
    """监控类型"""
    CPU = "cpu"
    MEMORY = "memory"
    DISK = "disk"
    NETWORK = "network"
    PROCESS = "process"
    SERVICE = "service"
    DATABASE = "database"
    CUSTOM = "custom"

@dataclass
class MonitorRule:
    """监控规则"""
    rule_id: str
    monitor_type: MonitorType
    metric_name: str
    threshold_value: float
    comparison: str  # '>', '<', '>=', '<=', '==', '!='
    alert_level: AlertLevel
    description: str
    enabled: bool = True
    check_interval: int = 60  # 秒
    consecutive_failures: int = 3
    current_failures: int = 0
    last_check: Optional[datetime] = None
    last_alert: Optional[datetime] = None
    alert_cooldown: int = 300  # 秒，告警冷却时间

@dataclass
class SystemAlert:
    """系统告警"""
    alert_id: str
    rule_id: str
    alert_level: AlertLevel
    message: str
    current_value: Any
    threshold_value: Any
    timestamp: datetime = field(default_factory=datetime.now)
    acknowledged: bool = False
    acknowledged_by: Optional[str] = None
    acknowledged_time: Optional[datetime] = None
    resolved: bool = False
    resolved_time: Optional[datetime] = None
    details: Dict[str, Any] = field(default_factory=dict)

@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    disk_usage: Dict[str, float]
    network_io: Dict[str, int]
    process_count: int
    load_average: List[float]
    uptime: float

class SystemMonitor:
    """系统监控服务"""
    
    def __init__(self):
        self.logger = logging.getLogger('smart_classroom.system_monitor')
        self.error_handler = get_error_handler()
        self.logging_service = get_logging_service()
        
        # 监控规则
        self.monitor_rules: Dict[str, MonitorRule] = {}
        
        # 告警管理
        self.active_alerts: Dict[str, SystemAlert] = {}
        self.alert_history: List[SystemAlert] = []
        
        # 系统指标历史
        self.metrics_history: List[SystemMetrics] = []
        self.max_history_size = 1440  # 24小时的分钟数
        
        # 监控线程
        self.monitor_thread: Optional[threading.Thread] = None
        self.running = False
        
        # 告警回调
        self.alert_callbacks: List[Callable] = []
        
        # 告警队列
        self.alert_queue = queue.Queue()
        self.alert_thread: Optional[threading.Thread] = None
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 初始化默认监控规则
        self._setup_default_rules()
        
        self.logger.info("系统监控服务已初始化")
    
    def _setup_default_rules(self):
        """设置默认监控规则"""
        default_rules = [
            MonitorRule(
                rule_id="cpu_high",
                monitor_type=MonitorType.CPU,
                metric_name="cpu_percent",
                threshold_value=80.0,
                comparison=">",
                alert_level=AlertLevel.WARNING,
                description="CPU使用率过高"
            ),
            MonitorRule(
                rule_id="cpu_critical",
                monitor_type=MonitorType.CPU,
                metric_name="cpu_percent",
                threshold_value=95.0,
                comparison=">",
                alert_level=AlertLevel.CRITICAL,
                description="CPU使用率严重过高"
            ),
            MonitorRule(
                rule_id="memory_high",
                monitor_type=MonitorType.MEMORY,
                metric_name="memory_percent",
                threshold_value=85.0,
                comparison=">",
                alert_level=AlertLevel.WARNING,
                description="内存使用率过高"
            ),
            MonitorRule(
                rule_id="memory_critical",
                monitor_type=MonitorType.MEMORY,
                metric_name="memory_percent",
                threshold_value=95.0,
                comparison=">",
                alert_level=AlertLevel.CRITICAL,
                description="内存使用率严重过高"
            ),
            MonitorRule(
                rule_id="disk_high",
                monitor_type=MonitorType.DISK,
                metric_name="disk_usage",
                threshold_value=85.0,
                comparison=">",
                alert_level=AlertLevel.WARNING,
                description="磁盘使用率过高"
            ),
            MonitorRule(
                rule_id="disk_critical",
                monitor_type=MonitorType.DISK,
                metric_name="disk_usage",
                threshold_value=95.0,
                comparison=">",
                alert_level=AlertLevel.CRITICAL,
                description="磁盘使用率严重过高"
            )
        ]
        
        for rule in default_rules:
            self.monitor_rules[rule.rule_id] = rule
    
    def start_monitoring(self):
        """开始监控"""
        if self.running:
            self.logger.warning("系统监控已在运行")
            return
        
        self.running = True
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitor_thread.start()
        
        # 启动告警处理线程
        self.alert_thread = threading.Thread(
            target=self._alert_processing_loop,
            daemon=True
        )
        self.alert_thread.start()
        
        self.logger.info("系统监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        
        # 停止告警处理
        self.alert_queue.put(None)
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        if self.alert_thread and self.alert_thread.is_alive():
            self.alert_thread.join(timeout=5)
        
        self.logger.info("系统监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.running:
            try:
                # 收集系统指标
                metrics = self._collect_system_metrics()
                
                # 保存指标历史
                with self.lock:
                    self.metrics_history.append(metrics)
                    if len(self.metrics_history) > self.max_history_size:
                        self.metrics_history.pop(0)
                
                # 检查监控规则
                current_time = datetime.now()
                
                for rule in self.monitor_rules.values():
                    if not rule.enabled:
                        continue
                    
                    # 检查是否需要检查此规则
                    if (rule.last_check is None or 
                        (current_time - rule.last_check).total_seconds() >= rule.check_interval):
                        
                        self._check_monitor_rule(rule, metrics)
                        rule.last_check = current_time
                
                # 记录性能指标
                self.logging_service.log_performance(
                    "cpu_usage", metrics.cpu_percent, "percent"
                )
                self.logging_service.log_performance(
                    "memory_usage", metrics.memory_percent, "percent"
                )
                
                time.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                self.error_handler.handle_error(
                    e, ErrorType.SYSTEM_ERROR, ErrorSeverity.MEDIUM,
                    context={'component': 'system_monitor'}
                )
    
    def _collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用率
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        # 磁盘使用率
        disk_usage = {}
        for partition in psutil.disk_partitions():
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                disk_usage[partition.mountpoint] = usage.percent
            except PermissionError:
                continue
        
        # 网络IO
        network_io = psutil.net_io_counters()._asdict()
        
        # 进程数量
        process_count = len(psutil.pids())
        
        # 负载平均值
        try:
            load_average = list(psutil.getloadavg())
        except AttributeError:
            # Windows系统没有getloadavg
            load_average = [0.0, 0.0, 0.0]
        
        # 系统运行时间
        uptime = time.time() - psutil.boot_time()
        
        return SystemMetrics(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            disk_usage=disk_usage,
            network_io=network_io,
            process_count=process_count,
            load_average=load_average,
            uptime=uptime
        )
    
    def _check_monitor_rule(self, rule: MonitorRule, metrics: SystemMetrics):
        """检查监控规则"""
        try:
            # 获取当前值
            current_value = self._get_metric_value(rule, metrics)
            if current_value is None:
                return
            
            # 执行比较
            threshold_exceeded = self._compare_values(
                current_value, rule.threshold_value, rule.comparison
            )
            
            if threshold_exceeded:
                rule.current_failures += 1
                
                # 检查是否达到连续失败次数
                if rule.current_failures >= rule.consecutive_failures:
                    self._trigger_alert(rule, current_value, metrics)
            else:
                # 重置失败计数
                if rule.current_failures > 0:
                    rule.current_failures = 0
                    self._resolve_alert(rule.rule_id)
        
        except Exception as e:
            self.logger.error(f"检查监控规则异常 {rule.rule_id}: {e}")
    
    def _get_metric_value(self, rule: MonitorRule, metrics: SystemMetrics):
        """获取指标值"""
        if rule.monitor_type == MonitorType.CPU:
            return metrics.cpu_percent
        elif rule.monitor_type == MonitorType.MEMORY:
            return metrics.memory_percent
        elif rule.monitor_type == MonitorType.DISK:
            if rule.metric_name == "disk_usage":
                # 返回最高的磁盘使用率
                return max(metrics.disk_usage.values()) if metrics.disk_usage else 0
        elif rule.monitor_type == MonitorType.PROCESS:
            return metrics.process_count
        
        return None
    
    def _compare_values(self, current_value, threshold_value, comparison: str) -> bool:
        """比较值"""
        if comparison == ">":
            return current_value > threshold_value
        elif comparison == "<":
            return current_value < threshold_value
        elif comparison == ">=":
            return current_value >= threshold_value
        elif comparison == "<=":
            return current_value <= threshold_value
        elif comparison == "==":
            return current_value == threshold_value
        elif comparison == "!=":
            return current_value != threshold_value
        
        return False
    
    def _trigger_alert(self, rule: MonitorRule, current_value, metrics: SystemMetrics):
        """触发告警"""
        current_time = datetime.now()
        
        # 检查告警冷却时间
        if (rule.last_alert and 
            (current_time - rule.last_alert).total_seconds() < rule.alert_cooldown):
            return
        
        alert_id = f"{rule.rule_id}_{int(time.time())}"
        
        alert = SystemAlert(
            alert_id=alert_id,
            rule_id=rule.rule_id,
            alert_level=rule.alert_level,
            message=f"{rule.description}: 当前值 {current_value}, 阈值 {rule.threshold_value}",
            current_value=current_value,
            threshold_value=rule.threshold_value,
            details={
                'metric_name': rule.metric_name,
                'comparison': rule.comparison,
                'consecutive_failures': rule.current_failures,
                'system_metrics': {
                    'cpu_percent': metrics.cpu_percent,
                    'memory_percent': metrics.memory_percent,
                    'disk_usage': metrics.disk_usage,
                    'process_count': metrics.process_count
                }
            }
        )
        
        with self.lock:
            self.active_alerts[alert_id] = alert
            self.alert_history.append(alert)
            
            # 保持历史记录在合理范围内
            if len(self.alert_history) > 1000:
                self.alert_history = self.alert_history[-500:]
        
        rule.last_alert = current_time
        
        # 添加到告警队列
        self.alert_queue.put(alert)
        
        self.logger.warning(f"触发告警: {alert.message}")
    
    def _resolve_alert(self, rule_id: str):
        """解决告警"""
        with self.lock:
            alerts_to_resolve = [
                alert for alert in self.active_alerts.values()
                if alert.rule_id == rule_id and not alert.resolved
            ]
            
            for alert in alerts_to_resolve:
                alert.resolved = True
                alert.resolved_time = datetime.now()
                
                # 从活动告警中移除
                if alert.alert_id in self.active_alerts:
                    del self.active_alerts[alert.alert_id]
                
                self.logger.info(f"告警已解决: {alert.alert_id}")
    
    def _alert_processing_loop(self):
        """告警处理循环"""
        while self.running:
            try:
                alert = self.alert_queue.get(timeout=1)
                if alert is None:
                    break
                
                # 处理告警
                self._process_alert(alert)
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"告警处理异常: {e}")
    
    def _process_alert(self, alert: SystemAlert):
        """处理告警"""
        try:
            # 记录告警日志
            self.logging_service.log_system_event(
                event_type="system_alert",
                message=alert.message,
                level=alert.alert_level.value,
                alert_id=alert.alert_id,
                rule_id=alert.rule_id,
                current_value=alert.current_value,
                threshold_value=alert.threshold_value
            )
            
            # 调用告警回调
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    self.logger.error(f"告警回调异常: {e}")
            
            # 根据告警级别执行不同操作
            if alert.alert_level == AlertLevel.CRITICAL:
                self._handle_critical_alert(alert)
            elif alert.alert_level == AlertLevel.ERROR:
                self._handle_error_alert(alert)
            elif alert.alert_level == AlertLevel.WARNING:
                self._handle_warning_alert(alert)
        
        except Exception as e:
            self.logger.error(f"处理告警异常: {e}")
    
    def _handle_critical_alert(self, alert: SystemAlert):
        """处理严重告警"""
        self.logger.critical(f"严重告警: {alert.message}")
        
        # 严重告警可能需要特殊处理，如发送邮件、短信等
        # 这里可以集成外部告警系统
    
    def _handle_error_alert(self, alert: SystemAlert):
        """处理错误告警"""
        self.logger.error(f"错误告警: {alert.message}")
    
    def _handle_warning_alert(self, alert: SystemAlert):
        """处理警告告警"""
        self.logger.warning(f"警告告警: {alert.message}")
    
    def add_monitor_rule(self, rule: MonitorRule):
        """添加监控规则"""
        with self.lock:
            self.monitor_rules[rule.rule_id] = rule
            self.logger.info(f"添加监控规则: {rule.rule_id}")
    
    def remove_monitor_rule(self, rule_id: str):
        """移除监控规则"""
        with self.lock:
            if rule_id in self.monitor_rules:
                del self.monitor_rules[rule_id]
                self.logger.info(f"移除监控规则: {rule_id}")
    
    def enable_rule(self, rule_id: str):
        """启用规则"""
        with self.lock:
            if rule_id in self.monitor_rules:
                self.monitor_rules[rule_id].enabled = True
                self.logger.info(f"启用监控规则: {rule_id}")
    
    def disable_rule(self, rule_id: str):
        """禁用规则"""
        with self.lock:
            if rule_id in self.monitor_rules:
                self.monitor_rules[rule_id].enabled = False
                self.logger.info(f"禁用监控规则: {rule_id}")
    
    def acknowledge_alert(self, alert_id: str, user_id: str = None):
        """确认告警"""
        with self.lock:
            if alert_id in self.active_alerts:
                alert = self.active_alerts[alert_id]
                alert.acknowledged = True
                alert.acknowledged_by = user_id
                alert.acknowledged_time = datetime.now()
                
                self.logger.info(f"告警已确认: {alert_id} by {user_id}")
                return True
        
        return False
    
    def get_current_metrics(self) -> Dict:
        """获取当前系统指标"""
        metrics = self._collect_system_metrics()
        
        return {
            'timestamp': metrics.timestamp.isoformat(),
            'cpu_percent': metrics.cpu_percent,
            'memory_percent': metrics.memory_percent,
            'disk_usage': metrics.disk_usage,
            'network_io': metrics.network_io,
            'process_count': metrics.process_count,
            'load_average': metrics.load_average,
            'uptime': metrics.uptime,
            'uptime_formatted': str(timedelta(seconds=int(metrics.uptime)))
        }
    
    def get_metrics_history(self, hours: int = 1) -> List[Dict]:
        """获取指标历史"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self.lock:
            filtered_metrics = [
                {
                    'timestamp': m.timestamp.isoformat(),
                    'cpu_percent': m.cpu_percent,
                    'memory_percent': m.memory_percent,
                    'disk_usage': m.disk_usage,
                    'process_count': m.process_count
                }
                for m in self.metrics_history
                if m.timestamp >= cutoff_time
            ]
        
        return filtered_metrics
    
    def get_active_alerts(self) -> List[Dict]:
        """获取活动告警"""
        with self.lock:
            return [
                {
                    'alert_id': alert.alert_id,
                    'rule_id': alert.rule_id,
                    'alert_level': alert.alert_level.value,
                    'message': alert.message,
                    'current_value': alert.current_value,
                    'threshold_value': alert.threshold_value,
                    'timestamp': alert.timestamp.isoformat(),
                    'acknowledged': alert.acknowledged,
                    'acknowledged_by': alert.acknowledged_by,
                    'acknowledged_time': alert.acknowledged_time.isoformat() if alert.acknowledged_time else None
                }
                for alert in self.active_alerts.values()
            ]
    
    def get_alert_history(self, hours: int = 24) -> List[Dict]:
        """获取告警历史"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self.lock:
            return [
                {
                    'alert_id': alert.alert_id,
                    'rule_id': alert.rule_id,
                    'alert_level': alert.alert_level.value,
                    'message': alert.message,
                    'current_value': alert.current_value,
                    'threshold_value': alert.threshold_value,
                    'timestamp': alert.timestamp.isoformat(),
                    'acknowledged': alert.acknowledged,
                    'resolved': alert.resolved,
                    'resolved_time': alert.resolved_time.isoformat() if alert.resolved_time else None
                }
                for alert in self.alert_history
                if alert.timestamp >= cutoff_time
            ]
    
    def get_monitor_rules(self) -> List[Dict]:
        """获取监控规则"""
        with self.lock:
            return [
                {
                    'rule_id': rule.rule_id,
                    'monitor_type': rule.monitor_type.value,
                    'metric_name': rule.metric_name,
                    'threshold_value': rule.threshold_value,
                    'comparison': rule.comparison,
                    'alert_level': rule.alert_level.value,
                    'description': rule.description,
                    'enabled': rule.enabled,
                    'check_interval': rule.check_interval,
                    'consecutive_failures': rule.consecutive_failures,
                    'current_failures': rule.current_failures,
                    'last_check': rule.last_check.isoformat() if rule.last_check else None,
                    'last_alert': rule.last_alert.isoformat() if rule.last_alert else None
                }
                for rule in self.monitor_rules.values()
            ]
    
    def get_system_status(self) -> Dict:
        """获取系统状态概览"""
        metrics = self.get_current_metrics()
        active_alerts = self.get_active_alerts()
        
        # 计算系统健康状态
        health_score = 100
        
        if metrics['cpu_percent'] > 80:
            health_score -= 20
        if metrics['memory_percent'] > 80:
            health_score -= 20
        
        max_disk_usage = max(metrics['disk_usage'].values()) if metrics['disk_usage'] else 0
        if max_disk_usage > 80:
            health_score -= 15
        
        # 根据告警数量调整健康分数
        critical_alerts = sum(1 for alert in active_alerts if alert['alert_level'] == 'critical')
        error_alerts = sum(1 for alert in active_alerts if alert['alert_level'] == 'error')
        warning_alerts = sum(1 for alert in active_alerts if alert['alert_level'] == 'warning')
        
        health_score -= critical_alerts * 25
        health_score -= error_alerts * 15
        health_score -= warning_alerts * 5
        
        health_score = max(0, health_score)
        
        # 确定健康状态
        if health_score >= 90:
            health_status = "excellent"
        elif health_score >= 70:
            health_status = "good"
        elif health_score >= 50:
            health_status = "fair"
        elif health_score >= 30:
            health_status = "poor"
        else:
            health_status = "critical"
        
        return {
            'health_score': health_score,
            'health_status': health_status,
            'metrics': metrics,
            'active_alerts_count': len(active_alerts),
            'critical_alerts': critical_alerts,
            'error_alerts': error_alerts,
            'warning_alerts': warning_alerts,
            'monitoring_enabled': self.running
        }
    
    def register_alert_callback(self, callback: Callable):
        """注册告警回调"""
        self.alert_callbacks.append(callback)
        self.logger.debug("已注册告警回调")

# 全局系统监控实例
_system_monitor = None

def get_system_monitor() -> SystemMonitor:
    """获取系统监控实例"""
    global _system_monitor
    if _system_monitor is None:
        _system_monitor = SystemMonitor()
    return _system_monitor