#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 数据备份和恢复服务
"""

import os
import shutil
import sqlite3
import json
import threading
import time
import logging
import zipfile
import hashlib
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import schedule

from .error_handler import get_error_handler, ErrorType, ErrorSeverity
from .logging_service import get_logging_service

class BackupType(Enum):
    """备份类型"""
    FULL = "full"
    INCREMENTAL = "incremental"
    DIFFERENTIAL = "differential"

class BackupStatus(Enum):
    """备份状态"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class BackupTask:
    """备份任务"""
    task_id: str
    backup_type: BackupType
    source_paths: List[str]
    destination_path: str
    description: str
    status: BackupStatus = BackupStatus.PENDING
    created_time: datetime = field(default_factory=datetime.now)
    started_time: Optional[datetime] = None
    completed_time: Optional[datetime] = None
    file_count: int = 0
    total_size: int = 0
    compressed_size: int = 0
    checksum: Optional[str] = None
    error_message: Optional[str] = None
    progress: float = 0.0

@dataclass
class RestoreTask:
    """恢复任务"""
    task_id: str
    backup_file: str
    destination_path: str
    description: str
    status: BackupStatus = BackupStatus.PENDING
    created_time: datetime = field(default_factory=datetime.now)
    started_time: Optional[datetime] = None
    completed_time: Optional[datetime] = None
    restored_files: int = 0
    error_message: Optional[str] = None
    progress: float = 0.0

class BackupService:
    """数据备份和恢复服务"""
    
    def __init__(self, backup_root: str = "backups"):
        self.logger = logging.getLogger('smart_classroom.backup_service')
        self.error_handler = get_error_handler()
        self.logging_service = get_logging_service()
        
        # 备份根目录
        self.backup_root = Path(backup_root)
        self.backup_root.mkdir(parents=True, exist_ok=True)
        
        # 任务管理
        self.backup_tasks: Dict[str, BackupTask] = {}
        self.restore_tasks: Dict[str, RestoreTask] = {}
        
        # 调度器
        self.scheduler_thread: Optional[threading.Thread] = None
        self.running = False
        
        # 备份配置
        self.backup_configs = {
            'database': {
                'enabled': True,
                'paths': ['instance/*.db'],
                'schedule': 'daily',
                'retention_days': 30,
                'compression': True
            },
            'logs': {
                'enabled': True,
                'paths': ['logs/*.log'],
                'schedule': 'weekly',
                'retention_days': 90,
                'compression': True
            },
            'uploads': {
                'enabled': True,
                'paths': ['instance/uploads/*'],
                'schedule': 'daily',
                'retention_days': 60,
                'compression': True
            },
            'config': {
                'enabled': True,
                'paths': ['config/*', '*.yml', '*.yaml', '*.json'],
                'schedule': 'weekly',
                'retention_days': 180,
                'compression': False
            }
        }
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 进度回调
        self.progress_callbacks: List[Callable] = []
        
        self.logger.info("备份服务已初始化")
    
    def start_scheduler(self):
        """启动调度器"""
        if self.running:
            self.logger.warning("备份调度器已在运行")
            return
        
        self.running = True
        
        # 设置定时任务
        self._setup_scheduled_backups()
        
        # 启动调度线程
        self.scheduler_thread = threading.Thread(
            target=self._scheduler_loop,
            daemon=True
        )
        self.scheduler_thread.start()
        
        self.logger.info("备份调度器已启动")
    
    def stop_scheduler(self):
        """停止调度器"""
        self.running = False
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        self.logger.info("备份调度器已停止")
    
    def _setup_scheduled_backups(self):
        """设置定时备份"""
        for config_name, config in self.backup_configs.items():
            if not config['enabled']:
                continue
            
            schedule_type = config['schedule']
            
            if schedule_type == 'daily':
                schedule.every().day.at("02:00").do(
                    self._scheduled_backup, config_name
                )
            elif schedule_type == 'weekly':
                schedule.every().sunday.at("03:00").do(
                    self._scheduled_backup, config_name
                )
            elif schedule_type == 'monthly':
                schedule.every().month.do(
                    self._scheduled_backup, config_name
                )
            
            self.logger.info(f"已设置定时备份: {config_name} - {schedule_type}")
    
    def _scheduler_loop(self):
        """调度循环"""
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                self.logger.error(f"调度循环异常: {e}")
                self.error_handler.handle_error(
                    e, ErrorType.SYSTEM_ERROR, ErrorSeverity.MEDIUM,
                    context={'component': 'backup_scheduler'}
                )
    
    def _scheduled_backup(self, config_name: str):
        """执行定时备份"""
        try:
            config = self.backup_configs[config_name]
            
            task_id = f"scheduled_{config_name}_{int(time.time())}"
            
            self.create_backup_task(
                task_id=task_id,
                backup_type=BackupType.INCREMENTAL,
                source_paths=config['paths'],
                description=f"定时备份: {config_name}",
                compression=config.get('compression', True)
            )
            
            self.logger.info(f"已创建定时备份任务: {task_id}")
            
        except Exception as e:
            self.logger.error(f"定时备份失败 {config_name}: {e}")
            self.error_handler.handle_error(
                e, ErrorType.SYSTEM_ERROR, ErrorSeverity.HIGH,
                context={'config_name': config_name}
            )
    
    def create_backup_task(self, task_id: str, backup_type: BackupType,
                          source_paths: List[str], description: str,
                          compression: bool = True) -> BackupTask:
        """创建备份任务"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        destination_path = str(
            self.backup_root / f"{task_id}_{timestamp}.{'zip' if compression else 'tar'}"
        )
        
        task = BackupTask(
            task_id=task_id,
            backup_type=backup_type,
            source_paths=source_paths,
            destination_path=destination_path,
            description=description
        )
        
        with self.lock:
            self.backup_tasks[task_id] = task
        
        # 在后台执行备份
        backup_thread = threading.Thread(
            target=self._execute_backup,
            args=(task, compression),
            daemon=True
        )
        backup_thread.start()
        
        self.logger.info(f"已创建备份任务: {task_id}")
        return task
    
    def _execute_backup(self, task: BackupTask, compression: bool = True):
        """执行备份"""
        try:
            task.status = BackupStatus.IN_PROGRESS
            task.started_time = datetime.now()
            
            self.logger.info(f"开始执行备份: {task.task_id}")
            
            # 收集要备份的文件
            files_to_backup = []
            total_size = 0
            
            for pattern in task.source_paths:
                matching_files = self._find_files(pattern)
                for file_path in matching_files:
                    if os.path.isfile(file_path):
                        file_size = os.path.getsize(file_path)
                        files_to_backup.append((file_path, file_size))
                        total_size += file_size
            
            task.file_count = len(files_to_backup)
            task.total_size = total_size
            
            if not files_to_backup:
                task.status = BackupStatus.COMPLETED
                task.completed_time = datetime.now()
                self.logger.warning(f"备份任务无文件: {task.task_id}")
                return
            
            # 执行备份
            if compression:
                self._create_zip_backup(task, files_to_backup)
            else:
                self._create_tar_backup(task, files_to_backup)
            
            # 计算校验和
            task.checksum = self._calculate_checksum(task.destination_path)
            task.compressed_size = os.path.getsize(task.destination_path)
            
            task.status = BackupStatus.COMPLETED
            task.completed_time = datetime.now()
            task.progress = 100.0
            
            self.logger.info(f"备份完成: {task.task_id}")
            
            # 记录备份日志
            self.logging_service.log_system_event(
                event_type="backup_completed",
                message=f"备份完成: {task.description}",
                task_id=task.task_id,
                file_count=task.file_count,
                total_size=task.total_size,
                compressed_size=task.compressed_size
            )
            
            # 清理旧备份
            self._cleanup_old_backups()
            
        except Exception as e:
            task.status = BackupStatus.FAILED
            task.error_message = str(e)
            task.completed_time = datetime.now()
            
            self.logger.error(f"备份失败 {task.task_id}: {e}")
            self.error_handler.handle_error(
                e, ErrorType.SYSTEM_ERROR, ErrorSeverity.HIGH,
                context={'task_id': task.task_id}
            )
    
    def _find_files(self, pattern: str) -> List[str]:
        """查找匹配模式的文件"""
        import glob
        
        # 支持相对路径和绝对路径
        if not os.path.isabs(pattern):
            pattern = os.path.join(os.getcwd(), pattern)
        
        return glob.glob(pattern, recursive=True)
    
    def _create_zip_backup(self, task: BackupTask, files_to_backup: List[tuple]):
        """创建ZIP备份"""
        processed_size = 0
        
        with zipfile.ZipFile(task.destination_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for i, (file_path, file_size) in enumerate(files_to_backup):
                try:
                    # 计算相对路径
                    arcname = os.path.relpath(file_path)
                    zipf.write(file_path, arcname)
                    
                    processed_size += file_size
                    task.progress = (processed_size / task.total_size) * 100
                    
                    # 调用进度回调
                    self._notify_progress(task)
                    
                except Exception as e:
                    self.logger.warning(f"备份文件失败 {file_path}: {e}")
    
    def _create_tar_backup(self, task: BackupTask, files_to_backup: List[tuple]):
        """创建TAR备份"""
        import tarfile
        
        processed_size = 0
        
        with tarfile.open(task.destination_path, 'w') as tarf:
            for i, (file_path, file_size) in enumerate(files_to_backup):
                try:
                    # 计算相对路径
                    arcname = os.path.relpath(file_path)
                    tarf.add(file_path, arcname)
                    
                    processed_size += file_size
                    task.progress = (processed_size / task.total_size) * 100
                    
                    # 调用进度回调
                    self._notify_progress(task)
                    
                except Exception as e:
                    self.logger.warning(f"备份文件失败 {file_path}: {e}")
    
    def _calculate_checksum(self, file_path: str) -> str:
        """计算文件校验和"""
        hash_md5 = hashlib.md5()
        
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        
        return hash_md5.hexdigest()
    
    def _notify_progress(self, task: BackupTask):
        """通知进度"""
        for callback in self.progress_callbacks:
            try:
                callback(task)
            except Exception as e:
                self.logger.error(f"进度回调异常: {e}")
    
    def create_restore_task(self, task_id: str, backup_file: str,
                           destination_path: str, description: str) -> RestoreTask:
        """创建恢复任务"""
        task = RestoreTask(
            task_id=task_id,
            backup_file=backup_file,
            destination_path=destination_path,
            description=description
        )
        
        with self.lock:
            self.restore_tasks[task_id] = task
        
        # 在后台执行恢复
        restore_thread = threading.Thread(
            target=self._execute_restore,
            args=(task,),
            daemon=True
        )
        restore_thread.start()
        
        self.logger.info(f"已创建恢复任务: {task_id}")
        return task
    
    def _execute_restore(self, task: RestoreTask):
        """执行恢复"""
        try:
            task.status = BackupStatus.IN_PROGRESS
            task.started_time = datetime.now()
            
            self.logger.info(f"开始执行恢复: {task.task_id}")
            
            if not os.path.exists(task.backup_file):
                raise FileNotFoundError(f"备份文件不存在: {task.backup_file}")
            
            # 创建目标目录
            os.makedirs(task.destination_path, exist_ok=True)
            
            # 根据文件扩展名选择解压方法
            if task.backup_file.endswith('.zip'):
                self._extract_zip_backup(task)
            elif task.backup_file.endswith('.tar'):
                self._extract_tar_backup(task)
            else:
                raise ValueError(f"不支持的备份文件格式: {task.backup_file}")
            
            task.status = BackupStatus.COMPLETED
            task.completed_time = datetime.now()
            task.progress = 100.0
            
            self.logger.info(f"恢复完成: {task.task_id}")
            
            # 记录恢复日志
            self.logging_service.log_system_event(
                event_type="restore_completed",
                message=f"恢复完成: {task.description}",
                task_id=task.task_id,
                restored_files=task.restored_files
            )
            
        except Exception as e:
            task.status = BackupStatus.FAILED
            task.error_message = str(e)
            task.completed_time = datetime.now()
            
            self.logger.error(f"恢复失败 {task.task_id}: {e}")
            self.error_handler.handle_error(
                e, ErrorType.SYSTEM_ERROR, ErrorSeverity.HIGH,
                context={'task_id': task.task_id}
            )
    
    def _extract_zip_backup(self, task: RestoreTask):
        """解压ZIP备份"""
        with zipfile.ZipFile(task.backup_file, 'r') as zipf:
            members = zipf.infolist()
            total_files = len(members)
            
            for i, member in enumerate(members):
                try:
                    zipf.extract(member, task.destination_path)
                    task.restored_files += 1
                    task.progress = ((i + 1) / total_files) * 100
                    
                except Exception as e:
                    self.logger.warning(f"解压文件失败 {member.filename}: {e}")
    
    def _extract_tar_backup(self, task: RestoreTask):
        """解压TAR备份"""
        import tarfile
        
        with tarfile.open(task.backup_file, 'r') as tarf:
            members = tarf.getmembers()
            total_files = len(members)
            
            for i, member in enumerate(members):
                try:
                    tarf.extract(member, task.destination_path)
                    task.restored_files += 1
                    task.progress = ((i + 1) / total_files) * 100
                    
                except Exception as e:
                    self.logger.warning(f"解压文件失败 {member.name}: {e}")
    
    def _cleanup_old_backups(self):
        """清理旧备份"""
        try:
            for config_name, config in self.backup_configs.items():
                retention_days = config.get('retention_days', 30)
                cutoff_time = datetime.now() - timedelta(days=retention_days)
                
                # 查找旧备份文件
                pattern = f"scheduled_{config_name}_*.zip"
                old_backups = []
                
                for backup_file in self.backup_root.glob(pattern):
                    try:
                        # 从文件名提取时间戳
                        timestamp_str = backup_file.stem.split('_')[-2:]
                        timestamp_str = '_'.join(timestamp_str)
                        file_time = datetime.strptime(timestamp_str, '%Y%m%d_%H%M%S')
                        
                        if file_time < cutoff_time:
                            old_backups.append(backup_file)
                    except Exception as e:
                        self.logger.warning(f"解析备份文件时间失败 {backup_file}: {e}")
                
                # 删除旧备份
                for backup_file in old_backups:
                    try:
                        backup_file.unlink()
                        self.logger.info(f"已删除旧备份: {backup_file}")
                    except Exception as e:
                        self.logger.error(f"删除旧备份失败 {backup_file}: {e}")
        
        except Exception as e:
            self.logger.error(f"清理旧备份失败: {e}")
    
    def backup_database(self, db_path: str, backup_name: str = None) -> str:
        """备份数据库"""
        if backup_name is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"database_backup_{timestamp}.db"
        
        backup_path = self.backup_root / backup_name
        
        try:
            # 使用SQLite的备份API
            source_conn = sqlite3.connect(db_path)
            backup_conn = sqlite3.connect(str(backup_path))
            
            source_conn.backup(backup_conn)
            
            source_conn.close()
            backup_conn.close()
            
            self.logger.info(f"数据库备份完成: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            raise
    
    def restore_database(self, backup_path: str, target_path: str):
        """恢复数据库"""
        try:
            # 备份当前数据库
            if os.path.exists(target_path):
                backup_current = f"{target_path}.backup_{int(time.time())}"
                shutil.copy2(target_path, backup_current)
                self.logger.info(f"当前数据库已备份到: {backup_current}")
            
            # 恢复数据库
            shutil.copy2(backup_path, target_path)
            
            self.logger.info(f"数据库恢复完成: {target_path}")
            
        except Exception as e:
            self.logger.error(f"数据库恢复失败: {e}")
            raise
    
    def get_backup_list(self) -> List[Dict]:
        """获取备份列表"""
        backups = []
        
        for backup_file in self.backup_root.glob('*'):
            if backup_file.is_file():
                try:
                    stat = backup_file.stat()
                    backups.append({
                        'filename': backup_file.name,
                        'path': str(backup_file),
                        'size': stat.st_size,
                        'size_mb': round(stat.st_size / 1024 / 1024, 2),
                        'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat()
                    })
                except Exception as e:
                    self.logger.error(f"获取备份文件信息失败 {backup_file}: {e}")
        
        # 按创建时间排序
        backups.sort(key=lambda x: x['created_time'], reverse=True)
        return backups
    
    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """获取任务状态"""
        with self.lock:
            # 检查备份任务
            if task_id in self.backup_tasks:
                task = self.backup_tasks[task_id]
                return {
                    'task_id': task.task_id,
                    'type': 'backup',
                    'backup_type': task.backup_type.value,
                    'description': task.description,
                    'status': task.status.value,
                    'progress': task.progress,
                    'created_time': task.created_time.isoformat(),
                    'started_time': task.started_time.isoformat() if task.started_time else None,
                    'completed_time': task.completed_time.isoformat() if task.completed_time else None,
                    'file_count': task.file_count,
                    'total_size': task.total_size,
                    'compressed_size': task.compressed_size,
                    'error_message': task.error_message
                }
            
            # 检查恢复任务
            if task_id in self.restore_tasks:
                task = self.restore_tasks[task_id]
                return {
                    'task_id': task.task_id,
                    'type': 'restore',
                    'description': task.description,
                    'status': task.status.value,
                    'progress': task.progress,
                    'created_time': task.created_time.isoformat(),
                    'started_time': task.started_time.isoformat() if task.started_time else None,
                    'completed_time': task.completed_time.isoformat() if task.completed_time else None,
                    'restored_files': task.restored_files,
                    'error_message': task.error_message
                }
        
        return None
    
    def get_all_tasks(self) -> List[Dict]:
        """获取所有任务"""
        tasks = []
        
        with self.lock:
            for task_id in self.backup_tasks:
                task_status = self.get_task_status(task_id)
                if task_status:
                    tasks.append(task_status)
            
            for task_id in self.restore_tasks:
                task_status = self.get_task_status(task_id)
                if task_status:
                    tasks.append(task_status)
        
        # 按创建时间排序
        tasks.sort(key=lambda x: x['created_time'], reverse=True)
        return tasks
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self.lock:
            if task_id in self.backup_tasks:
                task = self.backup_tasks[task_id]
                if task.status == BackupStatus.IN_PROGRESS:
                    task.status = BackupStatus.CANCELLED
                    self.logger.info(f"已取消备份任务: {task_id}")
                    return True
            
            if task_id in self.restore_tasks:
                task = self.restore_tasks[task_id]
                if task.status == BackupStatus.IN_PROGRESS:
                    task.status = BackupStatus.CANCELLED
                    self.logger.info(f"已取消恢复任务: {task_id}")
                    return True
        
        return False
    
    def register_progress_callback(self, callback: Callable):
        """注册进度回调"""
        self.progress_callbacks.append(callback)
    
    def get_backup_statistics(self) -> Dict:
        """获取备份统计"""
        with self.lock:
            total_backups = len(self.backup_tasks)
            completed_backups = sum(
                1 for task in self.backup_tasks.values()
                if task.status == BackupStatus.COMPLETED
            )
            failed_backups = sum(
                1 for task in self.backup_tasks.values()
                if task.status == BackupStatus.FAILED
            )
            
            total_restores = len(self.restore_tasks)
            completed_restores = sum(
                1 for task in self.restore_tasks.values()
                if task.status == BackupStatus.COMPLETED
            )
            
            # 计算备份文件总大小
            backup_files = self.get_backup_list()
            total_backup_size = sum(backup['size'] for backup in backup_files)
            
            return {
                'total_backups': total_backups,
                'completed_backups': completed_backups,
                'failed_backups': failed_backups,
                'success_rate': (completed_backups / max(total_backups, 1)) * 100,
                'total_restores': total_restores,
                'completed_restores': completed_restores,
                'total_backup_files': len(backup_files),
                'total_backup_size': total_backup_size,
                'total_backup_size_mb': round(total_backup_size / 1024 / 1024, 2)
            }

# 全局备份服务实例
_backup_service = None

def get_backup_service() -> BackupService:
    """获取备份服务实例"""
    global _backup_service
    if _backup_service is None:
        _backup_service = BackupService()
    return _backup_service