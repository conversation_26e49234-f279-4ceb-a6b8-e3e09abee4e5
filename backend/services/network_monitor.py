#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 网络异常检测和自动重连服务
"""

import socket
import threading
import time
import logging
import requests
import subprocess
import platform
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Set
from dataclasses import dataclass
from enum import Enum
import json

from .error_handler import get_error_handler, ErrorType, ErrorSeverity
from .logging_service import get_logging_service

class ConnectionStatus(Enum):
    """连接状态"""
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    RECONNECTING = "reconnecting"
    FAILED = "failed"

@dataclass
class NetworkEndpoint:
    """网络端点"""
    endpoint_id: str
    host: str
    port: int
    protocol: str  # 'tcp', 'udp', 'http', 'websocket'
    description: str
    status: ConnectionStatus = ConnectionStatus.DISCONNECTED
    last_check: Optional[datetime] = None
    last_success: Optional[datetime] = None
    failure_count: int = 0
    max_failures: int = 3
    check_interval: int = 30  # 秒
    timeout: int = 5  # 秒
    auto_reconnect: bool = True
    reconnect_callback: Optional[Callable] = None

class NetworkMonitor:
    """网络监控服务"""
    
    def __init__(self):
        self.logger = logging.getLogger('smart_classroom.network_monitor')
        self.error_handler = get_error_handler()
        self.logging_service = get_logging_service()
        
        # 监控的网络端点
        self.endpoints: Dict[str, NetworkEndpoint] = {}
        
        # 监控线程
        self.monitor_thread: Optional[threading.Thread] = None
        self.running = False
        
        # 事件回调
        self.connection_callbacks: Dict[str, List[Callable]] = {
            'connected': [],
            'disconnected': [],
            'reconnected': [],
            'failed': []
        }
        
        # 网络统计
        self.network_stats = {
            'total_checks': 0,
            'successful_checks': 0,
            'failed_checks': 0,
            'reconnection_attempts': 0,
            'successful_reconnections': 0
        }
        
        # 线程锁
        self.lock = threading.RLock()
        
        self.logger.info("网络监控服务已初始化")
    
    def add_endpoint(self, endpoint: NetworkEndpoint):
        """添加监控端点"""
        with self.lock:
            self.endpoints[endpoint.endpoint_id] = endpoint
            self.logger.info(f"添加监控端点: {endpoint.endpoint_id} - {endpoint.host}:{endpoint.port}")
    
    def remove_endpoint(self, endpoint_id: str):
        """移除监控端点"""
        with self.lock:
            if endpoint_id in self.endpoints:
                del self.endpoints[endpoint_id]
                self.logger.info(f"移除监控端点: {endpoint_id}")
    
    def register_callback(self, event_type: str, callback: Callable):
        """注册事件回调"""
        if event_type in self.connection_callbacks:
            self.connection_callbacks[event_type].append(callback)
            self.logger.debug(f"注册回调: {event_type}")
    
    def start_monitoring(self):
        """开始监控"""
        if self.running:
            self.logger.warning("网络监控已在运行")
            return
        
        self.running = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info("网络监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("网络监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.running:
            try:
                current_time = datetime.now()
                
                with self.lock:
                    endpoints_to_check = []
                    for endpoint in self.endpoints.values():
                        # 检查是否需要检查此端点
                        if (endpoint.last_check is None or 
                            (current_time - endpoint.last_check).total_seconds() >= endpoint.check_interval):
                            endpoints_to_check.append(endpoint)
                
                # 检查端点（在锁外执行以避免阻塞）
                for endpoint in endpoints_to_check:
                    self._check_endpoint(endpoint)
                
                # 短暂休眠
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                self.error_handler.handle_error(
                    e, ErrorType.NETWORK_ERROR, ErrorSeverity.MEDIUM,
                    context={'component': 'network_monitor'}
                )
    
    def _check_endpoint(self, endpoint: NetworkEndpoint):
        """检查单个端点"""
        try:
            endpoint.last_check = datetime.now()
            self.network_stats['total_checks'] += 1
            
            # 根据协议类型选择检查方法
            if endpoint.protocol == 'tcp':
                success = self._check_tcp_connection(endpoint)
            elif endpoint.protocol == 'udp':
                success = self._check_udp_connection(endpoint)
            elif endpoint.protocol == 'http':
                success = self._check_http_connection(endpoint)
            elif endpoint.protocol == 'websocket':
                success = self._check_websocket_connection(endpoint)
            else:
                self.logger.warning(f"不支持的协议类型: {endpoint.protocol}")
                return
            
            # 更新状态
            old_status = endpoint.status
            
            if success:
                self.network_stats['successful_checks'] += 1
                endpoint.last_success = datetime.now()
                endpoint.failure_count = 0
                
                if old_status != ConnectionStatus.CONNECTED:
                    endpoint.status = ConnectionStatus.CONNECTED
                    self._trigger_event('connected', endpoint)
                    
                    if old_status == ConnectionStatus.RECONNECTING:
                        self._trigger_event('reconnected', endpoint)
                        self.network_stats['successful_reconnections'] += 1
                        self.logger.info(f"端点重连成功: {endpoint.endpoint_id}")
            else:
                self.network_stats['failed_checks'] += 1
                endpoint.failure_count += 1
                
                if endpoint.failure_count >= endpoint.max_failures:
                    if old_status != ConnectionStatus.FAILED:
                        endpoint.status = ConnectionStatus.FAILED
                        self._trigger_event('failed', endpoint)
                        self.logger.error(f"端点连接失败: {endpoint.endpoint_id}")
                        
                        # 记录错误
                        self.error_handler.handle_error(
                            Exception(f"网络端点连接失败: {endpoint.description}"),
                            ErrorType.NETWORK_ERROR,
                            ErrorSeverity.HIGH,
                            context={
                                'endpoint_id': endpoint.endpoint_id,
                                'host': endpoint.host,
                                'port': endpoint.port,
                                'protocol': endpoint.protocol,
                                'failure_count': endpoint.failure_count
                            }
                        )
                        
                        # 尝试自动重连
                        if endpoint.auto_reconnect:
                            self._attempt_reconnection(endpoint)
                elif old_status == ConnectionStatus.CONNECTED:
                    endpoint.status = ConnectionStatus.DISCONNECTED
                    self._trigger_event('disconnected', endpoint)
                    self.logger.warning(f"端点连接断开: {endpoint.endpoint_id}")
        
        except Exception as e:
            self.logger.error(f"检查端点异常 {endpoint.endpoint_id}: {e}")
            self.error_handler.handle_error(
                e, ErrorType.NETWORK_ERROR, ErrorSeverity.MEDIUM,
                context={'endpoint_id': endpoint.endpoint_id}
            )
    
    def _check_tcp_connection(self, endpoint: NetworkEndpoint) -> bool:
        """检查TCP连接"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(endpoint.timeout)
            result = sock.connect_ex((endpoint.host, endpoint.port))
            sock.close()
            return result == 0
        except Exception as e:
            self.logger.debug(f"TCP连接检查失败 {endpoint.endpoint_id}: {e}")
            return False
    
    def _check_udp_connection(self, endpoint: NetworkEndpoint) -> bool:
        """检查UDP连接（发送测试数据包）"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(endpoint.timeout)
            
            # 发送测试数据包
            test_data = b"ping"
            sock.sendto(test_data, (endpoint.host, endpoint.port))
            
            # 尝试接收响应（可选）
            try:
                sock.recvfrom(1024)
            except socket.timeout:
                # UDP是无连接的，超时不一定表示失败
                pass
            
            sock.close()
            return True
        except Exception as e:
            self.logger.debug(f"UDP连接检查失败 {endpoint.endpoint_id}: {e}")
            return False
    
    def _check_http_connection(self, endpoint: NetworkEndpoint) -> bool:
        """检查HTTP连接"""
        try:
            url = f"http://{endpoint.host}:{endpoint.port}"
            response = requests.get(url, timeout=endpoint.timeout)
            return response.status_code < 500  # 4xx错误也算连接成功
        except Exception as e:
            self.logger.debug(f"HTTP连接检查失败 {endpoint.endpoint_id}: {e}")
            return False
    
    def _check_websocket_connection(self, endpoint: NetworkEndpoint) -> bool:
        """检查WebSocket连接"""
        try:
            # 简单的TCP连接检查，实际应用中可能需要更复杂的WebSocket握手
            return self._check_tcp_connection(endpoint)
        except Exception as e:
            self.logger.debug(f"WebSocket连接检查失败 {endpoint.endpoint_id}: {e}")
            return False
    
    def _attempt_reconnection(self, endpoint: NetworkEndpoint):
        """尝试重连"""
        if endpoint.status == ConnectionStatus.RECONNECTING:
            return  # 已在重连中
        
        endpoint.status = ConnectionStatus.RECONNECTING
        self.network_stats['reconnection_attempts'] += 1
        
        self.logger.info(f"开始重连端点: {endpoint.endpoint_id}")
        
        # 在单独线程中执行重连
        reconnect_thread = threading.Thread(
            target=self._reconnection_worker,
            args=(endpoint,),
            daemon=True
        )
        reconnect_thread.start()
    
    def _reconnection_worker(self, endpoint: NetworkEndpoint):
        """重连工作线程"""
        try:
            # 调用重连回调
            if endpoint.reconnect_callback:
                success = endpoint.reconnect_callback(endpoint)
                if success:
                    self.logger.info(f"重连回调成功: {endpoint.endpoint_id}")
                    return
            
            # 默认重连策略：等待一段时间后重新检查
            time.sleep(5)  # 等待5秒
            
            # 重新检查连接
            self._check_endpoint(endpoint)
            
        except Exception as e:
            self.logger.error(f"重连异常 {endpoint.endpoint_id}: {e}")
            endpoint.status = ConnectionStatus.FAILED
    
    def _trigger_event(self, event_type: str, endpoint: NetworkEndpoint):
        """触发事件回调"""
        callbacks = self.connection_callbacks.get(event_type, [])
        
        for callback in callbacks:
            try:
                callback(endpoint)
            except Exception as e:
                self.logger.error(f"事件回调异常 {event_type}: {e}")
        
        # 记录事件日志
        self.logging_service.log_system_event(
            event_type=f"network_{event_type}",
            message=f"网络端点{event_type}: {endpoint.description}",
            endpoint_id=endpoint.endpoint_id,
            host=endpoint.host,
            port=endpoint.port
        )
    
    def get_endpoint_status(self, endpoint_id: str) -> Optional[Dict]:
        """获取端点状态"""
        with self.lock:
            endpoint = self.endpoints.get(endpoint_id)
            if not endpoint:
                return None
            
            return {
                'endpoint_id': endpoint.endpoint_id,
                'host': endpoint.host,
                'port': endpoint.port,
                'protocol': endpoint.protocol,
                'description': endpoint.description,
                'status': endpoint.status.value,
                'last_check': endpoint.last_check.isoformat() if endpoint.last_check else None,
                'last_success': endpoint.last_success.isoformat() if endpoint.last_success else None,
                'failure_count': endpoint.failure_count,
                'max_failures': endpoint.max_failures
            }
    
    def get_all_endpoints_status(self) -> List[Dict]:
        """获取所有端点状态"""
        with self.lock:
            return [
                self.get_endpoint_status(endpoint_id)
                for endpoint_id in self.endpoints.keys()
            ]
    
    def get_network_statistics(self) -> Dict:
        """获取网络统计"""
        with self.lock:
            connected_count = sum(
                1 for ep in self.endpoints.values() 
                if ep.status == ConnectionStatus.CONNECTED
            )
            
            return {
                **self.network_stats,
                'total_endpoints': len(self.endpoints),
                'connected_endpoints': connected_count,
                'disconnected_endpoints': len(self.endpoints) - connected_count,
                'success_rate': (
                    self.network_stats['successful_checks'] / 
                    max(self.network_stats['total_checks'], 1) * 100
                ),
                'reconnection_success_rate': (
                    self.network_stats['successful_reconnections'] / 
                    max(self.network_stats['reconnection_attempts'], 1) * 100
                )
            }
    
    def force_reconnect(self, endpoint_id: str) -> bool:
        """强制重连端点"""
        with self.lock:
            endpoint = self.endpoints.get(endpoint_id)
            if not endpoint:
                return False
            
            self.logger.info(f"强制重连端点: {endpoint_id}")
            self._attempt_reconnection(endpoint)
            return True
    
    def ping_host(self, host: str, count: int = 4) -> Dict:
        """Ping主机"""
        try:
            # 根据操作系统选择ping命令
            if platform.system().lower() == 'windows':
                cmd = ['ping', '-n', str(count), host]
            else:
                cmd = ['ping', '-c', str(count), host]
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=30
            )
            
            return {
                'host': host,
                'success': result.returncode == 0,
                'output': result.stdout,
                'error': result.stderr
            }
            
        except Exception as e:
            return {
                'host': host,
                'success': False,
                'error': str(e)
            }
    
    def trace_route(self, host: str) -> Dict:
        """路由跟踪"""
        try:
            # 根据操作系统选择traceroute命令
            if platform.system().lower() == 'windows':
                cmd = ['tracert', host]
            else:
                cmd = ['traceroute', host]
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=60
            )
            
            return {
                'host': host,
                'success': result.returncode == 0,
                'output': result.stdout,
                'error': result.stderr
            }
            
        except Exception as e:
            return {
                'host': host,
                'success': False,
                'error': str(e)
            }

# 全局网络监控实例
_network_monitor = None

def get_network_monitor() -> NetworkMonitor:
    """获取网络监控实例"""
    global _network_monitor
    if _network_monitor is None:
        _network_monitor = NetworkMonitor()
    return _network_monitor

# 便捷函数
def add_monitoring_endpoint(endpoint_id: str, host: str, port: int, 
                          protocol: str, description: str, **kwargs):
    """添加监控端点的便捷函数"""
    endpoint = NetworkEndpoint(
        endpoint_id=endpoint_id,
        host=host,
        port=port,
        protocol=protocol,
        description=description,
        **kwargs
    )
    get_network_monitor().add_endpoint(endpoint)

def start_network_monitoring():
    """启动网络监控的便捷函数"""
    get_network_monitor().start_monitoring()