#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 压力测试服务
"""

import time
import threading
import requests
import websocket
import json
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import statistics
import random

@dataclass
class TestResult:
    """测试结果"""
    test_name: str
    start_time: datetime
    end_time: datetime
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_response_time: float
    min_response_time: float
    max_response_time: float
    requests_per_second: float
    error_rate: float
    errors: List[str]

@dataclass
class LoadTestConfig:
    """负载测试配置"""
    concurrent_users: int = 10
    test_duration: int = 60  # 秒
    ramp_up_time: int = 10   # 秒
    target_url: str = "http://localhost:5000"
    endpoints: List[str] = None
    request_delay: float = 0.1  # 秒

class StressTestService:
    """压力测试服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.running = False
        self.test_results: List[TestResult] = []
        self.current_test: Optional[str] = None
        
        # 测试统计
        self.request_times: List[float] = []
        self.error_count = 0
        self.success_count = 0
        self.errors: List[str] = []
        
        # 线程池
        self.executor: Optional[ThreadPoolExecutor] = None
    
    def run_api_load_test(self, config: LoadTestConfig) -> TestResult:
        """运行API负载测试"""
        self.logger.info(f"开始API负载测试: {config.concurrent_users}并发用户, {config.test_duration}秒")
        
        self.current_test = "API负载测试"
        self.running = True
        self._reset_statistics()
        
        start_time = datetime.now()
        
        # 默认测试端点
        if not config.endpoints:
            config.endpoints = [
                "/api/health",
                "/api/devices",
                "/api/classrooms",
                "/api/groups"
            ]
        
        try:
            with ThreadPoolExecutor(max_workers=config.concurrent_users) as executor:
                self.executor = executor
                
                # 提交测试任务
                futures = []
                for i in range(config.concurrent_users):
                    future = executor.submit(
                        self._api_test_worker,
                        config,
                        i,
                        start_time
                    )
                    futures.append(future)
                    
                    # 渐进式增加负载
                    if config.ramp_up_time > 0:
                        time.sleep(config.ramp_up_time / config.concurrent_users)
                
                # 等待所有任务完成
                for future in as_completed(futures):
                    try:
                        future.result()
                    except Exception as e:
                        self.logger.error(f"测试任务异常: {e}")
                        self.errors.append(str(e))
                        self.error_count += 1
        
        except Exception as e:
            self.logger.error(f"负载测试异常: {e}")
            self.errors.append(str(e))
        
        finally:
            self.running = False
            self.executor = None
        
        end_time = datetime.now()
        
        # 生成测试结果
        result = self._generate_test_result(
            "API负载测试",
            start_time,
            end_time
        )
        
        self.test_results.append(result)
        self.current_test = None
        
        self.logger.info(f"API负载测试完成: 成功率 {(1-result.error_rate)*100:.1f}%")
        return result
    
    def _api_test_worker(self, config: LoadTestConfig, worker_id: int, start_time: datetime):
        """API测试工作线程"""
        session = requests.Session()
        
        while self.running and (datetime.now() - start_time).total_seconds() < config.test_duration:
            try:
                # 随机选择端点
                endpoint = random.choice(config.endpoints)
                url = f"{config.target_url}{endpoint}"
                
                # 发送请求
                request_start = time.time()
                response = session.get(url, timeout=10)
                request_end = time.time()
                
                response_time = request_end - request_start
                self.request_times.append(response_time)
                
                if response.status_code == 200:
                    self.success_count += 1
                else:
                    self.error_count += 1
                    self.errors.append(f"HTTP {response.status_code}: {url}")
                
            except Exception as e:
                self.error_count += 1
                self.errors.append(f"请求异常: {str(e)}")
            
            # 请求间隔
            if config.request_delay > 0:
                time.sleep(config.request_delay)
    
    def run_websocket_load_test(self, concurrent_connections: int = 50, 
                               test_duration: int = 60) -> TestResult:
        """运行WebSocket负载测试"""
        self.logger.info(f"开始WebSocket负载测试: {concurrent_connections}并发连接, {test_duration}秒")
        
        self.current_test = "WebSocket负载测试"
        self.running = True
        self._reset_statistics()
        
        start_time = datetime.now()
        
        try:
            with ThreadPoolExecutor(max_workers=concurrent_connections) as executor:
                self.executor = executor
                
                # 提交WebSocket测试任务
                futures = []
                for i in range(concurrent_connections):
                    future = executor.submit(
                        self._websocket_test_worker,
                        i,
                        start_time,
                        test_duration
                    )
                    futures.append(future)
                
                # 等待所有任务完成
                for future in as_completed(futures):
                    try:
                        future.result()
                    except Exception as e:
                        self.logger.error(f"WebSocket测试任务异常: {e}")
                        self.errors.append(str(e))
                        self.error_count += 1
        
        except Exception as e:
            self.logger.error(f"WebSocket负载测试异常: {e}")
            self.errors.append(str(e))
        
        finally:
            self.running = False
            self.executor = None
        
        end_time = datetime.now()
        
        # 生成测试结果
        result = self._generate_test_result(
            "WebSocket负载测试",
            start_time,
            end_time
        )
        
        self.test_results.append(result)
        self.current_test = None
        
        self.logger.info(f"WebSocket负载测试完成: 成功率 {(1-result.error_rate)*100:.1f}%")
        return result
    
    def _websocket_test_worker(self, worker_id: int, start_time: datetime, test_duration: int):
        """WebSocket测试工作线程"""
        ws_url = "ws://localhost:5000/socket.io/?EIO=4&transport=websocket"
        
        try:
            ws = websocket.create_connection(ws_url, timeout=10)
            self.success_count += 1
            
            # 发送测试消息
            while self.running and (datetime.now() - start_time).total_seconds() < test_duration:
                try:
                    message = json.dumps({
                        "type": "test_message",
                        "worker_id": worker_id,
                        "timestamp": datetime.now().isoformat()
                    })
                    
                    send_start = time.time()
                    ws.send(message)
                    
                    # 尝试接收响应
                    try:
                        response = ws.recv()
                        send_end = time.time()
                        self.request_times.append(send_end - send_start)
                        self.success_count += 1
                    except websocket.WebSocketTimeoutError:
                        self.error_count += 1
                        self.errors.append("WebSocket接收超时")
                    
                    time.sleep(1)  # 每秒发送一次消息
                    
                except Exception as e:
                    self.error_count += 1
                    self.errors.append(f"WebSocket发送异常: {str(e)}")
                    break
            
            ws.close()
            
        except Exception as e:
            self.error_count += 1
            self.errors.append(f"WebSocket连接异常: {str(e)}")
    
    def run_video_stream_test(self, stream_count: int = 8, test_duration: int = 60) -> TestResult:
        """运行视频流压力测试"""
        self.logger.info(f"开始视频流压力测试: {stream_count}路流, {test_duration}秒")
        
        self.current_test = "视频流压力测试"
        self.running = True
        self._reset_statistics()
        
        start_time = datetime.now()
        
        try:
            with ThreadPoolExecutor(max_workers=stream_count) as executor:
                self.executor = executor
                
                # 提交视频流测试任务
                futures = []
                for i in range(stream_count):
                    future = executor.submit(
                        self._video_stream_test_worker,
                        i,
                        start_time,
                        test_duration
                    )
                    futures.append(future)
                
                # 等待所有任务完成
                for future in as_completed(futures):
                    try:
                        future.result()
                    except Exception as e:
                        self.logger.error(f"视频流测试任务异常: {e}")
                        self.errors.append(str(e))
                        self.error_count += 1
        
        except Exception as e:
            self.logger.error(f"视频流压力测试异常: {e}")
            self.errors.append(str(e))
        
        finally:
            self.running = False
            self.executor = None
        
        end_time = datetime.now()
        
        # 生成测试结果
        result = self._generate_test_result(
            "视频流压力测试",
            start_time,
            end_time
        )
        
        self.test_results.append(result)
        self.current_test = None
        
        self.logger.info(f"视频流压力测试完成: 成功率 {(1-result.error_rate)*100:.1f}%")
        return result
    
    def _video_stream_test_worker(self, stream_id: int, start_time: datetime, test_duration: int):
        """视频流测试工作线程"""
        stream_url = f"http://localhost:8889/test_stream_{stream_id}/index.m3u8"
        
        while self.running and (datetime.now() - start_time).total_seconds() < test_duration:
            try:
                request_start = time.time()
                response = requests.get(stream_url, timeout=5)
                request_end = time.time()
                
                self.request_times.append(request_end - request_start)
                
                if response.status_code == 200:
                    self.success_count += 1
                else:
                    self.error_count += 1
                    self.errors.append(f"视频流请求失败: HTTP {response.status_code}")
                
            except Exception as e:
                self.error_count += 1
                self.errors.append(f"视频流请求异常: {str(e)}")
            
            time.sleep(2)  # 每2秒检查一次流
    
    def run_comprehensive_test(self) -> Dict[str, TestResult]:
        """运行综合压力测试"""
        self.logger.info("开始综合压力测试")
        
        results = {}
        
        # API负载测试
        api_config = LoadTestConfig(
            concurrent_users=20,
            test_duration=30,
            ramp_up_time=5
        )
        results['api_load'] = self.run_api_load_test(api_config)
        
        # 等待间隔
        time.sleep(5)
        
        # WebSocket负载测试
        results['websocket_load'] = self.run_websocket_load_test(
            concurrent_connections=30,
            test_duration=30
        )
        
        # 等待间隔
        time.sleep(5)
        
        # 视频流压力测试
        results['video_stream'] = self.run_video_stream_test(
            stream_count=6,
            test_duration=30
        )
        
        self.logger.info("综合压力测试完成")
        return results
    
    def _reset_statistics(self):
        """重置统计数据"""
        self.request_times.clear()
        self.error_count = 0
        self.success_count = 0
        self.errors.clear()
    
    def _generate_test_result(self, test_name: str, start_time: datetime, 
                            end_time: datetime) -> TestResult:
        """生成测试结果"""
        total_requests = self.success_count + self.error_count
        duration = (end_time - start_time).total_seconds()
        
        if self.request_times:
            avg_response_time = statistics.mean(self.request_times)
            min_response_time = min(self.request_times)
            max_response_time = max(self.request_times)
        else:
            avg_response_time = 0
            min_response_time = 0
            max_response_time = 0
        
        requests_per_second = total_requests / duration if duration > 0 else 0
        error_rate = self.error_count / total_requests if total_requests > 0 else 0
        
        return TestResult(
            test_name=test_name,
            start_time=start_time,
            end_time=end_time,
            total_requests=total_requests,
            successful_requests=self.success_count,
            failed_requests=self.error_count,
            average_response_time=avg_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            requests_per_second=requests_per_second,
            error_rate=error_rate,
            errors=self.errors.copy()
        )
    
    def stop_test(self):
        """停止当前测试"""
        self.running = False
        self.logger.info("正在停止压力测试...")
    
    def get_test_status(self) -> Dict[str, Any]:
        """获取测试状态"""
        return {
            'running': self.running,
            'current_test': self.current_test,
            'total_requests': self.success_count + self.error_count,
            'successful_requests': self.success_count,
            'failed_requests': self.error_count,
            'error_rate': self.error_count / (self.success_count + self.error_count) 
                         if (self.success_count + self.error_count) > 0 else 0
        }
    
    def get_test_history(self) -> List[Dict[str, Any]]:
        """获取测试历史"""
        history = []
        
        for result in self.test_results:
            history.append({
                'test_name': result.test_name,
                'start_time': result.start_time.isoformat(),
                'end_time': result.end_time.isoformat(),
                'duration': (result.end_time - result.start_time).total_seconds(),
                'total_requests': result.total_requests,
                'success_rate': (1 - result.error_rate) * 100,
                'average_response_time': result.average_response_time,
                'requests_per_second': result.requests_per_second
            })
        
        return history
    
    def export_test_results(self, filename: str = None) -> str:
        """导出测试结果"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"stress_test_results_{timestamp}.json"
        
        export_data = {
            'export_time': datetime.now().isoformat(),
            'total_tests': len(self.test_results),
            'results': []
        }
        
        for result in self.test_results:
            export_data['results'].append({
                'test_name': result.test_name,
                'start_time': result.start_time.isoformat(),
                'end_time': result.end_time.isoformat(),
                'duration': (result.end_time - result.start_time).total_seconds(),
                'total_requests': result.total_requests,
                'successful_requests': result.successful_requests,
                'failed_requests': result.failed_requests,
                'average_response_time': result.average_response_time,
                'min_response_time': result.min_response_time,
                'max_response_time': result.max_response_time,
                'requests_per_second': result.requests_per_second,
                'error_rate': result.error_rate,
                'errors': result.errors[:10]  # 只保留前10个错误
            })
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"测试结果已导出到: {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"导出测试结果失败: {e}")
            raise

# 全局实例
_stress_test_service = None

def get_stress_test_service() -> StressTestService:
    """获取压力测试服务实例"""
    global _stress_test_service
    if _stress_test_service is None:
        _stress_test_service = StressTestService()
    return _stress_test_service