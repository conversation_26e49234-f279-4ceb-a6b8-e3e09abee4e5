# -*- coding: utf-8 -*-
"""
题目和答题业务服务
"""

from datetime import datetime, timedelta
from models import Question, Answer, Student, Classroom, ClassroomActivity
from models.base import db


class QuestionService:
    """题目管理服务"""
    
    @staticmethod
    def create_question(classroom_id, creator_id, creator_name, title, content, 
                       question_type, options=None, correct_answer=None, 
                       time_limit=None, target_type='all', target_groups=None, target_students=None):
        """创建题目"""
        classroom = Classroom.get_by_classroom_id(classroom_id)
        if not classroom:
            return None, "课堂不存在"
        
        import uuid
        question_id = f"q_{classroom_id}_{uuid.uuid4().hex[:8]}"
        
        question = Question(
            question_id=question_id,
            title=title,
            content=content,
            question_type=question_type,
            options=options or [],
            correct_answer=correct_answer,
            classroom_id=classroom.id,
            creator_id=creator_id,
            creator_name=creator_name,
            time_limit=time_limit,
            target_type=target_type,
            target_groups=target_groups or [],
            target_students=target_students or []
        )
        question.save()
        
        # 记录活动
        ClassroomActivity.log_activity(
            classroom.id, 'question_create', creator_id, creator_name,
            f"创建题目: {title}"
        )
        
        return question, "题目创建成功"
    
    @staticmethod
    def publish_question(question_id, creator_id, time_limit=None):
        """发布题目"""
        question = Question.get_by_question_id(question_id)
        if not question:
            return None, "题目不存在"
        
        if question.creator_id != creator_id:
            return None, "无权限操作"
        
        question.publish(time_limit)
        
        # 记录活动
        ClassroomActivity.log_activity(
            question.classroom_id, 'question_publish', 
            creator_id, question.creator_name,
            f"发布题目: {question.title}"
        )
        
        return question, "题目发布成功"
    
    @staticmethod
    def close_question(question_id, creator_id):
        """关闭题目"""
        question = Question.get_by_question_id(question_id)
        if not question:
            return None, "题目不存在"
        
        if question.creator_id != creator_id:
            return None, "无权限操作"
        
        question.close()
        
        # 记录活动
        ClassroomActivity.log_activity(
            question.classroom_id, 'question_close', 
            creator_id, question.creator_name,
            f"关闭题目: {question.title}"
        )
        
        return question, "题目已关闭"
    
    @staticmethod
    def submit_answer(question_id, student_id, content, start_time=None):
        """提交答案"""
        question = Question.get_by_question_id(question_id)
        student = Student.get_by_student_id(student_id)
        
        if not question:
            return None, "题目不存在"
        
        if not student:
            return None, "学生不存在"
        
        # 检查学生是否在题目的目标范围内
        if not QuestionService._is_student_target(question, student):
            return None, "您不在此题目的答题范围内"
        
        answer, message = Answer.submit_answer(
            question.id, student.id, content, start_time
        )
        
        if answer:
            # 记录活动
            ClassroomActivity.log_activity(
                student.classroom_id, 'answer_submit', 
                student_id, student.name,
                f"回答题目: {question.title}"
            )
        
        return answer, message
    
    @staticmethod
    def get_question_results(question_id, creator_id):
        """获取题目结果统计"""
        question = Question.get_by_question_id(question_id)
        if not question:
            return None, "题目不存在"
        
        if question.creator_id != creator_id:
            return None, "无权限查看"
        
        answers = Answer.get_question_answers(question.id)
        
        # 基本统计
        total_answers = len(answers)
        correct_answers = len([a for a in answers if a.is_correct])
        accuracy_rate = (correct_answers / total_answers * 100) if total_answers > 0 else 0
        
        # 答案分布
        answer_distribution = question.get_answer_distribution()
        
        # 响应时间统计
        response_times = [a.response_time for a in answers if a.response_time]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        # 学生答题详情
        student_answers = []
        for answer in answers:
            student_answers.append({
                'student_name': answer.student.name,
                'student_id': answer.student.student_id,
                'content': answer.content,
                'is_correct': answer.is_correct,
                'response_time': answer.response_time,
                'submitted_time': answer.submitted_time.isoformat()
            })
        
        return {
            'question': question.to_dict(),
            'statistics': {
                'total_answers': total_answers,
                'correct_answers': correct_answers,
                'accuracy_rate': accuracy_rate,
                'avg_response_time': avg_response_time
            },
            'answer_distribution': answer_distribution,
            'student_answers': student_answers
        }
    
    @staticmethod
    def get_student_questions(classroom_id, student_id):
        """获取学生可答题目列表"""
        classroom = Classroom.get_by_classroom_id(classroom_id)
        student = Student.get_by_student_id(student_id)
        
        if not classroom or not student:
            return []
        
        # 获取活跃题目
        active_questions = Question.get_active_questions(classroom.id)
        
        # 过滤学生可答的题目
        available_questions = []
        for question in active_questions:
            if QuestionService._is_student_target(question, student):
                # 检查是否已答题
                existing_answer = Answer.query.filter_by(
                    question_id=question.id,
                    student_id=student.id
                ).first()
                
                question_data = question.to_dict()
                question_data['remaining_time'] = question.get_remaining_time()
                question_data['has_answered'] = existing_answer is not None
                question_data['can_answer'] = (
                    existing_answer is None or question.allow_multiple_attempts
                )
                
                available_questions.append(question_data)
        
        return available_questions
    
    @staticmethod
    def get_classroom_questions(classroom_id, creator_id):
        """获取课堂题目列表"""
        classroom = Classroom.get_by_classroom_id(classroom_id)
        if not classroom:
            return []
        
        questions = Question.get_classroom_questions(classroom.id)
        
        question_list = []
        for question in questions:
            question_data = question.to_dict()
            question_data['remaining_time'] = question.get_remaining_time()
            question_data['is_active'] = question.is_active()
            question_data['accuracy_rate'] = question.get_accuracy_rate()
            
            question_list.append(question_data)
        
        return question_list
    
    @staticmethod
    def _is_student_target(question, student):
        """检查学生是否在题目目标范围内"""
        if question.target_type == 'all':
            return True
        elif question.target_type == 'groups':
            return student.group_id in question.target_groups
        elif question.target_type == 'students':
            return student.id in question.target_students
        
        return False
    
    @staticmethod
    def get_question_statistics(classroom_id):
        """获取课堂题目统计"""
        classroom = Classroom.get_by_classroom_id(classroom_id)
        if not classroom:
            return None
        
        questions = Question.get_classroom_questions(classroom.id)
        
        total_questions = len(questions)
        published_questions = len([q for q in questions if q.status == 'published'])
        closed_questions = len([q for q in questions if q.status == 'closed'])
        
        total_answers = sum(q.total_answers for q in questions)
        total_correct = sum(q.correct_answers for q in questions)
        
        overall_accuracy = (total_correct / total_answers * 100) if total_answers > 0 else 0
        
        return {
            'total_questions': total_questions,
            'published_questions': published_questions,
            'closed_questions': closed_questions,
            'total_answers': total_answers,
            'total_correct': total_correct,
            'overall_accuracy': overall_accuracy
        }
        
    @staticmethod
    def compare_group_results_for_question(question_id):
        """比较小组在单个题目上的答题结果"""
        question = Question.get_by_question_id(question_id)
        if not question:
            return None, "题目不存在"
            
        from models import Group
        groups = Group.query.filter_by(classroom_id=question.classroom_id).all()
        
        comparison = {
            'question': question.to_dict(),
            'groups': []
        }
        
        for group in groups:
            # 获取小组答题情况
            answers = Answer.get_group_answers(question.id, group.id)
            
            total_answers = len(answers)
            correct_answers = len([a for a in answers if a.is_correct])
            accuracy_rate = (correct_answers / total_answers * 100) if total_answers > 0 else 0
            
            # 计算平均响应时间
            response_times = [a.response_time for a in answers if a.response_time]
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
            
            # 获取答案分布
            answer_distribution = {}
            for answer in answers:
                content = str(answer.content)
                answer_distribution[content] = answer_distribution.get(content, 0) + 1
                
            group_data = {
                'group_id': group.group_id,
                'group_name': group.name,
                'total_answers': total_answers,
                'correct_answers': correct_answers,
                'accuracy_rate': accuracy_rate,
                'avg_response_time': avg_response_time,
                'answer_distribution': answer_distribution
            }
            
            comparison['groups'].append(group_data)
            
        return comparison
        
    @staticmethod
    def compare_group_results_for_classroom(classroom_id):
        """比较小组在整个课堂的答题结果"""
        classroom = Classroom.get_by_classroom_id(classroom_id)
        if not classroom:
            return None, "课堂不存在"
            
        from models import Group
        groups = Group.query.filter_by(classroom_id=classroom.id).all()
        questions = Question.get_classroom_questions(classroom.id)
        
        comparison = {
            'classroom_id': classroom_id,
            'classroom_name': classroom.name,
            'groups': [],
            'questions': []
        }
        
        # 汇总每个小组的整体表现
        for group in groups:
            group_stats = {
                'group_id': group.group_id,
                'group_name': group.name,
                'total_questions_answered': 0,
                'total_correct_answers': 0,
                'overall_accuracy': 0,
                'avg_response_time': 0,
                'question_performance': []
            }
            
            total_response_time = 0
            response_time_count = 0
            
            # 统计每个题目的表现
            for question in questions:
                answers = Answer.get_group_answers(question.id, group.id)
                
                if answers:
                    total_answers = len(answers)
                    correct_answers = len([a for a in answers if a.is_correct])
                    accuracy = (correct_answers / total_answers * 100) if total_answers > 0 else 0
                    
                    # 计算响应时间
                    q_response_times = [a.response_time for a in answers if a.response_time]
                    avg_q_response_time = sum(q_response_times) / len(q_response_times) if q_response_times else 0
                    
                    total_response_time += sum(q_response_times)
                    response_time_count += len(q_response_times)
                    
                    group_stats['total_questions_answered'] += 1
                    group_stats['total_correct_answers'] += correct_answers
                    
                    # 记录题目表现
                    group_stats['question_performance'].append({
                        'question_id': question.question_id,
                        'total_answers': total_answers,
                        'correct_answers': correct_answers,
                        'accuracy': accuracy,
                        'avg_response_time': avg_q_response_time
                    })
            
            # 计算整体准确率和平均响应时间
            if group_stats['total_questions_answered'] > 0:
                group_stats['overall_accuracy'] = (group_stats['total_correct_answers'] / 
                                                 group_stats['total_questions_answered'] * 100)
                                                 
            if response_time_count > 0:
                group_stats['avg_response_time'] = total_response_time / response_time_count
                
            comparison['groups'].append(group_stats)
            
        # 添加题目信息
        for question in questions:
            comparison['questions'].append({
                'question_id': question.question_id,
                'title': question.title,
                'question_type': question.question_type,
                'status': question.status
            })
            
        return comparison
        
    @staticmethod
    def delete_question_answers(question_id):
        """删除题目的所有答案"""
        question = Question.get_by_question_id(question_id)
        if not question:
            return False, "题目不存在"
            
        answers = Answer.get_question_answers(question.id)
        for answer in answers:
            db.session.delete(answer)
            
        db.session.commit()
        return True, "答案删除成功"
        
    @staticmethod
    def export_question_results(question_id, format='csv'):
        """导出题目结果"""
        question = Question.get_by_question_id(question_id)
        if not question:
            return None, "题目不存在"
            
        answers = Answer.get_question_answers(question.id)
        
        if format == 'csv':
            import csv
            import io
            
            output = io.StringIO()
            writer = csv.writer(output)
            
            # 写入标题行
            writer.writerow(['学生ID', '学生姓名', '答案内容', '是否正确', '提交时间', '响应时间(秒)'])
            
            # 写入数据行
            for answer in answers:
                writer.writerow([
                    answer.student.student_id,
                    answer.student.name,
                    str(answer.content),
                    '正确' if answer.is_correct else '错误',
                    answer.submitted_time.strftime('%Y-%m-%d %H:%M:%S'),
                    f"{answer.response_time:.2f}" if answer.response_time else 'N/A'
                ])
                
            return output.getvalue(), "导出成功"
        elif format == 'json':
            result = {
                'question': question.to_dict(),
                'answers': []
            }
            
            for answer in answers:
                result['answers'].append({
                    'student_id': answer.student.student_id,
                    'student_name': answer.student.name,
                    'content': answer.content,
                    'is_correct': answer.is_correct,
                    'submitted_time': answer.submitted_time.isoformat(),
                    'response_time': answer.response_time
                })
                
            return result, "导出成功"
        else:
            return None, "不支持的导出格式"