#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 系统集成管理器
"""

import threading
import time
import psutil
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from shared.config import get_config
from shared.utils import Logger

class ServiceStatus(Enum):
    """服务状态枚举"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    ERROR = "error"
    STOPPING = "stopping"

@dataclass
class ServiceInfo:
    """服务信息"""
    name: str
    status: ServiceStatus
    start_time: Optional[datetime] = None
    error_message: Optional[str] = None
    restart_count: int = 0
    health_check_url: Optional[str] = None

@dataclass
class SystemMetrics:
    """系统性能指标"""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, int]
    active_connections: int
    response_time: float

class SystemIntegrationManager:
    """系统集成管理器"""
    
    def __init__(self):
        self.config = get_config()
        self.logger = logging.getLogger(__name__)
        self.services: Dict[str, ServiceInfo] = {}
        self.metrics_history: List[SystemMetrics] = []
        self.monitoring_thread: Optional[threading.Thread] = None
        self.running = False
        self.performance_thresholds = {
            'cpu_usage_max': 80.0,
            'memory_usage_max': 85.0,
            'disk_usage_max': 90.0,
            'response_time_max': 2.0,
            'connection_limit': self.config.MAX_TERMINALS
        }
        
        # 初始化服务列表
        self._initialize_services()
    
    def _initialize_services(self):
        """初始化服务列表"""
        services = [
            'device_discovery',
            'video_service', 
            'display_service',
            'file_service',
            'whiteboard_service',
            'report_service',
            'database',
            'websocket'
        ]
        
        for service_name in services:
            self.services[service_name] = ServiceInfo(
                name=service_name,
                status=ServiceStatus.STOPPED
            )
    
    def start_integration(self):
        """启动系统集成"""
        self.logger.info("启动系统集成管理器")
        self.running = True
        
        # 启动所有服务
        self._start_all_services()
        
        # 启动监控线程
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitoring_thread.start()
        
        self.logger.info("系统集成管理器启动完成")
    
    def stop_integration(self):
        """停止系统集成"""
        self.logger.info("停止系统集成管理器")
        self.running = False
        
        # 停止所有服务
        self._stop_all_services()
        
        # 等待监控线程结束
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=5)
        
        self.logger.info("系统集成管理器已停止")
    
    def _start_all_services(self):
        """启动所有服务"""
        for service_name in self.services:
            self._start_service(service_name)
    
    def _stop_all_services(self):
        """停止所有服务"""
        for service_name in self.services:
            self._stop_service(service_name)
    
    def _start_service(self, service_name: str):
        """启动单个服务"""
        if service_name not in self.services:
            return False
        
        service = self.services[service_name]
        service.status = ServiceStatus.STARTING
        
        try:
            # 根据服务类型启动相应服务
            if service_name == 'device_discovery':
                self._start_device_discovery()
            elif service_name == 'video_service':
                self._start_video_service()
            elif service_name == 'display_service':
                self._start_display_service()
            elif service_name == 'file_service':
                self._start_file_service()
            elif service_name == 'whiteboard_service':
                self._start_whiteboard_service()
            elif service_name == 'report_service':
                self._start_report_service()
            elif service_name == 'database':
                self._start_database()
            elif service_name == 'websocket':
                self._start_websocket()
            
            service.status = ServiceStatus.RUNNING
            service.start_time = datetime.now()
            service.error_message = None
            
            self.logger.info(f"服务 {service_name} 启动成功")
            return True
            
        except Exception as e:
            service.status = ServiceStatus.ERROR
            service.error_message = str(e)
            self.logger.error(f"服务 {service_name} 启动失败: {e}")
            return False
    
    def _stop_service(self, service_name: str):
        """停止单个服务"""
        if service_name not in self.services:
            return False
        
        service = self.services[service_name]
        service.status = ServiceStatus.STOPPING
        
        try:
            # 根据服务类型停止相应服务
            # 这里可以添加具体的停止逻辑
            
            service.status = ServiceStatus.STOPPED
            service.start_time = None
            
            self.logger.info(f"服务 {service_name} 已停止")
            return True
            
        except Exception as e:
            service.status = ServiceStatus.ERROR
            service.error_message = str(e)
            self.logger.error(f"服务 {service_name} 停止失败: {e}")
            return False
    
    def _start_device_discovery(self):
        """启动设备发现服务"""
        from backend.services.device_discovery import get_discovery_service
        discovery_service = get_discovery_service()
        if not discovery_service.running:
            discovery_service.start()
    
    def _start_video_service(self):
        """启动视频服务"""
        from backend.services.video_service import get_video_service
        video_service = get_video_service()
        # 视频服务初始化逻辑
    
    def _start_display_service(self):
        """启动显示服务"""
        from backend.services.display_service import get_display_service
        display_service = get_display_service()
        # 显示服务初始化逻辑
    
    def _start_file_service(self):
        """启动文件服务"""
        # 文件服务初始化逻辑
        pass
    
    def _start_whiteboard_service(self):
        """启动白板服务"""
        # 白板服务初始化逻辑
        pass
    
    def _start_report_service(self):
        """启动报告服务"""
        # 报告服务初始化逻辑
        pass
    
    def _start_database(self):
        """启动数据库服务"""
        # 数据库连接检查
        from backend.models import db
        # 检查数据库连接
    
    def _start_websocket(self):
        """启动WebSocket服务"""
        # WebSocket服务初始化逻辑
        pass
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.running:
            try:
                # 收集系统指标
                metrics = self._collect_system_metrics()
                self.metrics_history.append(metrics)
                
                # 保持历史记录在合理范围内
                if len(self.metrics_history) > 1000:
                    self.metrics_history = self.metrics_history[-500:]
                
                # 检查服务健康状态
                self._check_service_health()
                
                # 检查性能阈值
                self._check_performance_thresholds(metrics)
                
                # 自动优化
                self._auto_optimize()
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
            
            time.sleep(5)  # 每5秒监控一次
    
    def _collect_system_metrics(self) -> SystemMetrics:
        """收集系统性能指标"""
        # CPU使用率
        cpu_usage = psutil.cpu_percent(interval=1)
        
        # 内存使用率
        memory = psutil.virtual_memory()
        memory_usage = memory.percent
        
        # 磁盘使用率
        disk = psutil.disk_usage('/')
        disk_usage = disk.percent
        
        # 网络IO
        network_io = psutil.net_io_counters()._asdict()
        
        # 活跃连接数（模拟）
        active_connections = len(psutil.net_connections())
        
        # 响应时间（模拟）
        response_time = 0.1  # 实际应该通过健康检查获取
        
        return SystemMetrics(
            timestamp=datetime.now(),
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
            disk_usage=disk_usage,
            network_io=network_io,
            active_connections=active_connections,
            response_time=response_time
        )
    
    def _check_service_health(self):
        """检查服务健康状态"""
        for service_name, service in self.services.items():
            if service.status == ServiceStatus.RUNNING:
                # 执行健康检查
                if not self._health_check_service(service_name):
                    self.logger.warning(f"服务 {service_name} 健康检查失败")
                    # 尝试重启服务
                    self._restart_service(service_name)
    
    def _health_check_service(self, service_name: str) -> bool:
        """服务健康检查"""
        try:
            # 这里可以添加具体的健康检查逻辑
            # 例如：HTTP请求、数据库连接测试等
            return True
        except Exception as e:
            self.logger.error(f"服务 {service_name} 健康检查异常: {e}")
            return False
    
    def _restart_service(self, service_name: str):
        """重启服务"""
        service = self.services[service_name]
        service.restart_count += 1
        
        self.logger.info(f"重启服务 {service_name} (第{service.restart_count}次)")
        
        # 停止服务
        self._stop_service(service_name)
        time.sleep(2)
        
        # 启动服务
        self._start_service(service_name)
    
    def _check_performance_thresholds(self, metrics: SystemMetrics):
        """检查性能阈值"""
        alerts = []
        
        if metrics.cpu_usage > self.performance_thresholds['cpu_usage_max']:
            alerts.append(f"CPU使用率过高: {metrics.cpu_usage:.1f}%")
        
        if metrics.memory_usage > self.performance_thresholds['memory_usage_max']:
            alerts.append(f"内存使用率过高: {metrics.memory_usage:.1f}%")
        
        if metrics.disk_usage > self.performance_thresholds['disk_usage_max']:
            alerts.append(f"磁盘使用率过高: {metrics.disk_usage:.1f}%")
        
        if metrics.response_time > self.performance_thresholds['response_time_max']:
            alerts.append(f"响应时间过长: {metrics.response_time:.2f}s")
        
        if metrics.active_connections > self.performance_thresholds['connection_limit']:
            alerts.append(f"连接数过多: {metrics.active_connections}")
        
        for alert in alerts:
            self.logger.warning(f"性能告警: {alert}")
    
    def _auto_optimize(self):
        """自动优化"""
        if not self.metrics_history:
            return
        
        latest_metrics = self.metrics_history[-1]
        
        # 内存优化
        if latest_metrics.memory_usage > 70:
            self._optimize_memory()
        
        # CPU优化
        if latest_metrics.cpu_usage > 70:
            self._optimize_cpu()
    
    def _optimize_memory(self):
        """内存优化"""
        import gc
        gc.collect()
        self.logger.info("执行内存垃圾回收")
    
    def _optimize_cpu(self):
        """CPU优化"""
        # 可以实现CPU优化逻辑，如调整线程池大小等
        self.logger.info("执行CPU优化")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        latest_metrics = self.metrics_history[-1] if self.metrics_history else None
        
        return {
            'services': {
                name: {
                    'status': service.status.value,
                    'start_time': service.start_time.isoformat() if service.start_time else None,
                    'error_message': service.error_message,
                    'restart_count': service.restart_count
                }
                for name, service in self.services.items()
            },
            'metrics': {
                'cpu_usage': latest_metrics.cpu_usage if latest_metrics else 0,
                'memory_usage': latest_metrics.memory_usage if latest_metrics else 0,
                'disk_usage': latest_metrics.disk_usage if latest_metrics else 0,
                'active_connections': latest_metrics.active_connections if latest_metrics else 0,
                'response_time': latest_metrics.response_time if latest_metrics else 0
            } if latest_metrics else {},
            'uptime': (datetime.now() - min(
                (s.start_time for s in self.services.values() if s.start_time),
                default=datetime.now()
            )).total_seconds() if any(s.start_time for s in self.services.values()) else 0
        }
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        if not self.metrics_history:
            return {}
        
        # 计算平均值
        cpu_avg = sum(m.cpu_usage for m in self.metrics_history) / len(self.metrics_history)
        memory_avg = sum(m.memory_usage for m in self.metrics_history) / len(self.metrics_history)
        response_time_avg = sum(m.response_time for m in self.metrics_history) / len(self.metrics_history)
        
        # 计算峰值
        cpu_peak = max(m.cpu_usage for m in self.metrics_history)
        memory_peak = max(m.memory_usage for m in self.metrics_history)
        response_time_peak = max(m.response_time for m in self.metrics_history)
        
        return {
            'period': {
                'start': self.metrics_history[0].timestamp.isoformat(),
                'end': self.metrics_history[-1].timestamp.isoformat(),
                'duration': (self.metrics_history[-1].timestamp - self.metrics_history[0].timestamp).total_seconds()
            },
            'averages': {
                'cpu_usage': cpu_avg,
                'memory_usage': memory_avg,
                'response_time': response_time_avg
            },
            'peaks': {
                'cpu_usage': cpu_peak,
                'memory_usage': memory_peak,
                'response_time': response_time_peak
            },
            'thresholds': self.performance_thresholds,
            'alerts_count': sum(1 for m in self.metrics_history 
                              if m.cpu_usage > self.performance_thresholds['cpu_usage_max'] or
                                 m.memory_usage > self.performance_thresholds['memory_usage_max'])
        }

# 全局实例
_integration_manager = None

def get_integration_manager() -> SystemIntegrationManager:
    """获取系统集成管理器实例"""
    global _integration_manager
    if _integration_manager is None:
        _integration_manager = SystemIntegrationManager()
    return _integration_manager