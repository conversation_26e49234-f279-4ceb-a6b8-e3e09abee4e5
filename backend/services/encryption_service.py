# -*- coding: utf-8 -*-
"""
数据加密服务
"""

import os
import base64
import hashlib
import secrets
from typing import Optional, Dict, Any
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import json
import datetime

from ..models.base import db
from ..models.security import DataEncryption


class EncryptionService:
    """数据加密服务类"""
    
    def __init__(self):
        self.session = db.session
        self._master_key = self._get_or_create_master_key()
        self._encryption_keys = {}
        self._load_encryption_keys()
    
    def _get_or_create_master_key(self) -> bytes:
        """获取或创建主密钥"""
        # 在实际应用中，主密钥应该从安全的密钥管理系统获取
        # 这里使用环境变量或配置文件
        master_key_b64 = os.environ.get('SMART_CLASSROOM_MASTER_KEY')
        
        if not master_key_b64:
            # 生成新的主密钥
            master_key = Fernet.generate_key()
            master_key_b64 = base64.b64encode(master_key).decode()
            
            # 在生产环境中，应该安全地存储这个密钥
            print(f"警告: 生成了新的主密钥，请将以下密钥安全存储:")
            print(f"SMART_CLASSROOM_MASTER_KEY={master_key_b64}")
            
            return master_key
        
        return base64.b64decode(master_key_b64.encode())
    
    def _load_encryption_keys(self):
        """加载加密密钥"""
        # 为不同类型的数据生成不同的密钥
        key_types = [
            'user_data', 'file_content', 'communication', 
            'session_data', 'audit_logs', 'device_info'
        ]
        
        for key_type in key_types:
            self._encryption_keys[key_type] = self._derive_key(key_type)
    
    def _derive_key(self, key_type: str) -> bytes:
        """从主密钥派生特定类型的密钥"""
        # 使用PBKDF2从主密钥派生子密钥
        salt = hashlib.sha256(key_type.encode()).digest()[:16]
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
            backend=default_backend()
        )
        
        return kdf.derive(self._master_key)
    
    def encrypt_data(self, data: str, data_type: str = 'general', 
                    data_id: str = None) -> Dict[str, Any]:
        """加密数据"""
        try:
            if not data:
                return {'encrypted_data': '', 'success': True}
            
            # 选择合适的密钥
            key_type = self._get_key_type_for_data_type(data_type)
            encryption_key = self._encryption_keys.get(key_type, self._encryption_keys['user_data'])
            
            # 使用Fernet进行对称加密
            fernet = Fernet(base64.urlsafe_b64encode(encryption_key))
            
            # 加密数据
            encrypted_data = fernet.encrypt(data.encode('utf-8'))
            encrypted_b64 = base64.b64encode(encrypted_data).decode('utf-8')
            
            # 记录加密信息
            if data_id:
                self._record_encryption(data_type, data_id, 'Fernet-AES256', key_type)
            
            return {
                'encrypted_data': encrypted_b64,
                'algorithm': 'Fernet-AES256',
                'key_type': key_type,
                'success': True
            }
            
        except Exception as e:
            return {
                'encrypted_data': data,
                'success': False,
                'error': str(e)
            }
    
    def decrypt_data(self, encrypted_data: str, data_type: str = 'general',
                    data_id: str = None) -> Dict[str, Any]:
        """解密数据"""
        try:
            if not encrypted_data:
                return {'decrypted_data': '', 'success': True}
            
            # 获取加密记录
            encryption_record = None
            if data_id:
                encryption_record = self.session.query(DataEncryption).filter_by(
                    data_type=data_type, data_id=data_id
                ).first()
            
            # 选择合适的密钥
            if encryption_record:
                key_type = encryption_record.key_id
            else:
                key_type = self._get_key_type_for_data_type(data_type)
            
            encryption_key = self._encryption_keys.get(key_type, self._encryption_keys['user_data'])
            
            # 使用Fernet进行解密
            fernet = Fernet(base64.urlsafe_b64encode(encryption_key))
            
            # 解密数据
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = fernet.decrypt(encrypted_bytes).decode('utf-8')
            
            return {
                'decrypted_data': decrypted_data,
                'success': True
            }
            
        except Exception as e:
            return {
                'decrypted_data': encrypted_data,
                'success': False,
                'error': str(e)
            }
    
    def encrypt_file(self, file_path: str, output_path: str = None) -> Dict[str, Any]:
        """加密文件"""
        try:
            if not os.path.exists(file_path):
                return {'success': False, 'error': 'File not found'}
            
            if not output_path:
                output_path = file_path + '.encrypted'
            
            # 生成文件专用密钥
            file_key = Fernet.generate_key()
            fernet = Fernet(file_key)
            
            # 读取并加密文件
            with open(file_path, 'rb') as infile:
                file_data = infile.read()
            
            encrypted_data = fernet.encrypt(file_data)
            
            # 保存加密文件
            with open(output_path, 'wb') as outfile:
                outfile.write(encrypted_data)
            
            # 使用主密钥加密文件密钥
            master_fernet = Fernet(base64.urlsafe_b64encode(self._master_key))
            encrypted_file_key = master_fernet.encrypt(file_key)
            
            # 保存密钥文件
            key_file_path = output_path + '.key'
            with open(key_file_path, 'wb') as keyfile:
                keyfile.write(encrypted_file_key)
            
            # 记录加密信息
            file_id = hashlib.sha256(file_path.encode()).hexdigest()
            self._record_encryption('file', file_id, 'Fernet-AES256', 'file_content')
            
            return {
                'success': True,
                'encrypted_file': output_path,
                'key_file': key_file_path,
                'file_id': file_id
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def decrypt_file(self, encrypted_file_path: str, key_file_path: str, 
                    output_path: str = None) -> Dict[str, Any]:
        """解密文件"""
        try:
            if not os.path.exists(encrypted_file_path) or not os.path.exists(key_file_path):
                return {'success': False, 'error': 'Encrypted file or key file not found'}
            
            if not output_path:
                output_path = encrypted_file_path.replace('.encrypted', '.decrypted')
            
            # 读取并解密文件密钥
            with open(key_file_path, 'rb') as keyfile:
                encrypted_file_key = keyfile.read()
            
            master_fernet = Fernet(base64.urlsafe_b64encode(self._master_key))
            file_key = master_fernet.decrypt(encrypted_file_key)
            
            # 读取并解密文件
            with open(encrypted_file_path, 'rb') as infile:
                encrypted_data = infile.read()
            
            fernet = Fernet(file_key)
            decrypted_data = fernet.decrypt(encrypted_data)
            
            # 保存解密文件
            with open(output_path, 'wb') as outfile:
                outfile.write(decrypted_data)
            
            return {
                'success': True,
                'decrypted_file': output_path
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def encrypt_communication(self, message: str, sender_id: str, 
                            receiver_id: str) -> Dict[str, Any]:
        """加密通信消息"""
        try:
            # 为通信生成会话密钥
            session_key = self._generate_session_key(sender_id, receiver_id)
            
            # 加密消息
            result = self.encrypt_data(message, 'communication')
            if result['success']:
                result['session_key_hash'] = hashlib.sha256(session_key).hexdigest()[:16]
            
            return result
            
        except Exception as e:
            return {
                'encrypted_data': message,
                'success': False,
                'error': str(e)
            }
    
    def _generate_session_key(self, sender_id: str, receiver_id: str) -> bytes:
        """生成通信会话密钥"""
        # 基于发送者和接收者ID生成确定性会话密钥
        key_material = f"{sender_id}:{receiver_id}".encode()
        return hashlib.sha256(key_material + self._master_key).digest()
    
    def _get_key_type_for_data_type(self, data_type: str) -> str:
        """根据数据类型获取密钥类型"""
        key_type_mapping = {
            'user': 'user_data',
            'user_data': 'user_data',
            'file': 'file_content',
            'file_content': 'file_content',
            'communication': 'communication',
            'message': 'communication',
            'session': 'session_data',
            'session_data': 'session_data',
            'audit': 'audit_logs',
            'audit_log': 'audit_logs',
            'device': 'device_info',
            'device_info': 'device_info'
        }
        
        return key_type_mapping.get(data_type, 'user_data')
    
    def _record_encryption(self, data_type: str, data_id: str, 
                          algorithm: str, key_id: str):
        """记录加密信息"""
        try:
            # 检查是否已存在记录
            existing = self.session.query(DataEncryption).filter_by(
                data_type=data_type, data_id=data_id
            ).first()
            
            if existing:
                # 更新现有记录
                existing.encryption_algorithm = algorithm
                existing.key_id = key_id
                existing.encrypted_at = datetime.datetime.utcnow()
            else:
                # 创建新记录
                encryption_record = DataEncryption(
                    data_type=data_type,
                    data_id=data_id,
                    encryption_algorithm=algorithm,
                    key_id=key_id
                )
                self.session.add(encryption_record)
            
            self.session.commit()
            
        except Exception as e:
            self.session.rollback()
            print(f"记录加密信息失败: {str(e)}")
    
    def rotate_keys(self) -> Dict[str, Any]:
        """密钥轮换"""
        try:
            # 生成新的主密钥
            new_master_key = Fernet.generate_key()
            
            # 重新加密所有数据（这是一个复杂的过程，需要谨慎处理）
            # 在实际应用中，应该分批处理，避免长时间锁定
            
            # 更新密钥
            old_master_key = self._master_key
            self._master_key = new_master_key
            self._load_encryption_keys()
            
            # 记录密钥轮换
            rotation_record = {
                'rotation_time': datetime.datetime.utcnow().isoformat(),
                'old_key_hash': hashlib.sha256(old_master_key).hexdigest()[:16],
                'new_key_hash': hashlib.sha256(new_master_key).hexdigest()[:16]
            }
            
            return {
                'success': True,
                'message': '密钥轮换成功',
                'rotation_record': rotation_record
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_encryption_status(self) -> Dict[str, Any]:
        """获取加密状态"""
        try:
            # 统计加密数据
            total_encrypted = self.session.query(DataEncryption).count()
            
            # 按类型统计
            type_stats = {}
            encryption_records = self.session.query(DataEncryption).all()
            
            for record in encryption_records:
                data_type = record.data_type
                if data_type not in type_stats:
                    type_stats[data_type] = 0
                type_stats[data_type] += 1
            
            return {
                'success': True,
                'total_encrypted_items': total_encrypted,
                'encryption_by_type': type_stats,
                'available_key_types': list(self._encryption_keys.keys()),
                'master_key_hash': hashlib.sha256(self._master_key).hexdigest()[:16]
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def verify_data_integrity(self, data_type: str = None) -> Dict[str, Any]:
        """验证数据完整性"""
        try:
            query = self.session.query(DataEncryption)
            if data_type:
                query = query.filter(DataEncryption.data_type == data_type)
            
            encryption_records = query.all()
            integrity_results = []
            
            for record in encryption_records:
                # 这里可以实现具体的完整性验证逻辑
                # 例如：重新加密数据并比较哈希值
                integrity_results.append({
                    'data_type': record.data_type,
                    'data_id': record.data_id,
                    'algorithm': record.encryption_algorithm,
                    'encrypted_at': record.encrypted_at.isoformat(),
                    'integrity_status': 'verified'  # 简化实现
                })
            
            return {
                'success': True,
                'verified_items': len(integrity_results),
                'integrity_results': integrity_results
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }


# 全局加密服务实例
_encryption_service = None

def get_encryption_service() -> EncryptionService:
    """获取加密服务实例"""
    global _encryption_service
    if _encryption_service is None:
        _encryption_service = EncryptionService()
    return _encryption_service