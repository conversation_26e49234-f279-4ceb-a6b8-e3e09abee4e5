# -*- coding: utf-8 -*-
"""
设备管理业务服务
"""

from datetime import datetime, timedelta
from models import Device, DeviceLog, Classroom
from models.base import db


class DeviceService:
    """设备管理服务"""
    
    @staticmethod
    def register_device(device_id, device_type, device_name, ip_address, 
                       port=8080, capabilities=None, screen_resolution=None, os_info=None):
        """注册设备"""
        # 检查设备是否已存在
        device = Device.get_by_device_id(device_id)
        
        if device:
            # 更新设备信息
            device.device_name = device_name
            device.ip_address = ip_address
            device.port = port
            device.capabilities = capabilities or []
            device.screen_resolution = screen_resolution
            device.os_info = os_info
            device.update_heartbeat()
            
            DeviceLog.log_event(device_id, 'update', '设备信息更新')
        else:
            # 创建新设备
            device = Device(
                device_id=device_id,
                device_type=device_type,
                device_name=device_name,
                ip_address=ip_address,
                port=port,
                capabilities=capabilities or [],
                screen_resolution=screen_resolution,
                os_info=os_info,
                status='online'
            )
            device.save()
            
            DeviceLog.log_event(device_id, 'register', '设备注册成功')
        
        return device
    
    @staticmethod
    def connect_device_to_classroom(device_id, classroom_id):
        """连接设备到课堂"""
        device = Device.get_by_device_id(device_id)
        classroom = Classroom.get_by_classroom_id(classroom_id)
        
        if not device:
            return False, "设备不存在"
        
        if not classroom:
            return False, "课堂不存在"
        
        device.classroom_id = classroom.id
        device.update_heartbeat()
        db.session.commit()
        
        DeviceLog.log_event(
            device_id, 'connect', 
            f'连接到课堂: {classroom.name}',
            {'classroom_id': classroom_id}
        )
        
        return True, "连接成功"
    
    @staticmethod
    def disconnect_device(device_id):
        """断开设备连接"""
        device = Device.get_by_device_id(device_id)
        if not device:
            return False, "设备不存在"
        
        classroom_name = ""
        if device.classroom_id:
            classroom = Classroom.get_by_id(device.classroom_id)
            classroom_name = classroom.name if classroom else ""
        
        device.classroom_id = None
        device.group_id = None
        device.set_offline()
        
        DeviceLog.log_event(
            device_id, 'disconnect', 
            f'断开连接: {classroom_name}' if classroom_name else '断开连接'
        )
        
        return True, "断开成功"
    
    @staticmethod
    def update_device_heartbeat(device_id):
        """更新设备心跳"""
        device = Device.get_by_device_id(device_id)
        if device:
            device.update_heartbeat()
            return True
        return False
    
    @staticmethod
    def get_classroom_devices(classroom_id):
        """获取课堂设备列表"""
        classroom = Classroom.get_by_classroom_id(classroom_id)
        if not classroom:
            return []
        
        devices = Device.get_devices_by_classroom(classroom.id)
        return [
            {
                'device': device.to_dict(),
                'is_online': device.is_online(),
                'last_heartbeat_ago': (datetime.utcnow() - device.last_heartbeat).total_seconds() 
                                    if device.last_heartbeat else None
            }
            for device in devices
        ]
    
    @staticmethod
    def get_device_status(device_id):
        """获取设备状态"""
        device = Device.get_by_device_id(device_id)
        if not device:
            return None
        
        # 获取最近的日志
        recent_logs = DeviceLog.get_device_logs(device_id, limit=10)
        
        return {
            'device': device.to_dict(),
            'is_online': device.is_online(),
            'last_heartbeat_ago': (datetime.utcnow() - device.last_heartbeat).total_seconds() 
                                if device.last_heartbeat else None,
            'recent_logs': [log.to_dict() for log in recent_logs]
        }
    
    @staticmethod
    def discover_devices():
        """发现网络中的设备"""
        # 这里应该实现UDP广播发现逻辑
        # 暂时返回已注册的在线设备
        online_devices = Device.get_online_devices()
        return [
            {
                'device_id': device.device_id,
                'device_type': device.device_type,
                'device_name': device.device_name,
                'ip_address': device.ip_address,
                'port': device.port,
                'capabilities': device.capabilities
            }
            for device in online_devices
        ]
    
    @staticmethod
    def cleanup_offline_devices():
        """清理离线设备"""
        # 获取超过10分钟没有心跳的设备
        cutoff_time = datetime.utcnow() - timedelta(minutes=10)
        
        offline_devices = Device.query.filter(
            Device.last_heartbeat < cutoff_time,
            Device.status == 'online'
        ).all()
        
        count = 0
        for device in offline_devices:
            device.set_offline()
            DeviceLog.log_event(device.device_id, 'timeout', '设备超时离线')
            count += 1
        
        return count
    
    @staticmethod
    def get_device_statistics():
        """获取设备统计信息"""
        total_devices = Device.query.count()
        online_devices = Device.query.filter_by(status='online').count()
        
        device_types = db.session.query(
            Device.device_type, 
            db.func.count(Device.id)
        ).group_by(Device.device_type).all()
        
        return {
            'total_devices': total_devices,
            'online_devices': online_devices,
            'offline_devices': total_devices - online_devices,
            'device_types': {device_type: count for device_type, count in device_types}
        }