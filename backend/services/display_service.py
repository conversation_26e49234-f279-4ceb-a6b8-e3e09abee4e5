#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 多屏显示控制服务
"""

import time
import json
import threading
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

from shared.config import get_config

# 获取配置
config = get_config()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('display_service')

class DisplayMode(Enum):
    """显示模式枚举"""
    BROADCAST = "broadcast"  # 广播模式
    INDEPENDENT = "independent"  # 独立显示模式
    GROUP_BROADCAST = "group_broadcast"  # 小组广播模式
    COMPARISON = "comparison"  # 对比显示模式
    DUAL_SCREEN = "dual_screen"  # 双屏联动模式

class LayoutType(Enum):
    """布局类型枚举"""
    SINGLE = "single"  # 单屏
    DUAL = "dual"  # 双屏
    QUAD = "quad"  # 四分屏
    GRID_2X2 = "grid_2x2"  # 2x2网格
    GRID_3X3 = "grid_3x3"  # 3x3网格
    PICTURE_IN_PICTURE = "pip"  # 画中画

@dataclass
class DisplayConfig:
    """显示配置数据类"""
    config_id: str
    classroom_id: str
    mode: DisplayMode
    layout: LayoutType
    target_devices: List[str]  # 目标设备ID列表
    content_sources: List[str]  # 内容源ID列表
    sync_enabled: bool = True
    created_time: float = None
    updated_time: float = None
    
    def __post_init__(self):
        if self.created_time is None:
            self.created_time = time.time()
        if self.updated_time is None:
            self.updated_time = time.time()
    
    def to_dict(self):
        """转换为字典"""
        result = asdict(self)
        result['mode'] = self.mode.value
        result['layout'] = self.layout.value
        return result

@dataclass
class GroupTimer:
    """分组计时器数据类"""
    timer_id: str
    classroom_id: str
    group_ids: List[str]
    title: str
    duration: int  # 秒
    start_time: float = None
    end_time: float = None
    is_active: bool = False
    is_paused: bool = False
    pause_time: float = None
    remaining_time: int = None
    
    def start(self):
        """启动计时器"""
        self.start_time = time.time()
        self.end_time = self.start_time + self.duration
        self.is_active = True
        self.is_paused = False
        self.remaining_time = self.duration
    
    def pause(self):
        """暂停计时器"""
        if self.is_active and not self.is_paused:
            self.pause_time = time.time()
            self.is_paused = True
            self.remaining_time = max(0, int(self.end_time - self.pause_time))
    
    def resume(self):
        """恢复计时器"""
        if self.is_active and self.is_paused:
            pause_duration = time.time() - self.pause_time
            self.end_time += pause_duration
            self.is_paused = False
            self.pause_time = None
    
    def stop(self):
        """停止计时器"""
        self.is_active = False
        self.is_paused = False
        self.remaining_time = 0
    
    def get_remaining_time(self) -> int:
        """获取剩余时间（秒）"""
        if not self.is_active:
            return 0
        
        if self.is_paused:
            return self.remaining_time
        
        current_time = time.time()
        remaining = max(0, int(self.end_time - current_time))
        
        if remaining == 0:
            self.is_active = False
        
        return remaining
    
    def to_dict(self):
        """转换为字典"""
        return {
            'timer_id': self.timer_id,
            'classroom_id': self.classroom_id,
            'group_ids': self.group_ids,
            'title': self.title,
            'duration': self.duration,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'is_active': self.is_active,
            'is_paused': self.is_paused,
            'remaining_time': self.get_remaining_time()
        }

@dataclass
class DiscussionTopic:
    """讨论主题数据类"""
    topic_id: str
    classroom_id: str
    title: str
    content: str
    image_data: Optional[str] = None  # Base64编码的图片数据
    target_groups: List[str] = None
    created_time: float = None
    
    def __post_init__(self):
        if self.created_time is None:
            self.created_time = time.time()
        if self.target_groups is None:
            self.target_groups = []
    
    def to_dict(self):
        """转换为字典"""
        return asdict(self)

class MultiScreenDisplayService:
    """多屏显示控制服务"""
    
    def __init__(self):
        """初始化多屏显示服务"""
        self.display_configs: Dict[str, DisplayConfig] = {}
        self.group_timers: Dict[str, GroupTimer] = {}
        self.discussion_topics: Dict[str, DiscussionTopic] = {}
        self.active_displays: Dict[str, Dict] = {}  # 活动显示状态
        self.lock = threading.RLock()
        
        # 启动定时器更新线程
        self.timer_thread = threading.Thread(target=self._timer_update_loop, daemon=True)
        self.timer_thread.start()
        
        logger.info("多屏显示控制服务已启动")
    
    def create_display_config(self, classroom_id: str, mode: str, layout: str, 
                            target_devices: List[str], content_sources: List[str],
                            sync_enabled: bool = True) -> DisplayConfig:
        """
        创建显示配置
        
        Args:
            classroom_id: 课堂ID
            mode: 显示模式
            layout: 布局类型
            target_devices: 目标设备ID列表
            content_sources: 内容源ID列表
            sync_enabled: 是否启用同步
            
        Returns:
            DisplayConfig: 显示配置对象
        """
        config_id = f"display_{classroom_id}_{int(time.time())}"
        
        try:
            display_mode = DisplayMode(mode)
            layout_type = LayoutType(layout)
        except ValueError as e:
            logger.error(f"无效的显示模式或布局类型: {str(e)}")
            raise ValueError(f"无效的显示模式或布局类型: {str(e)}")
        
        config = DisplayConfig(
            config_id=config_id,
            classroom_id=classroom_id,
            mode=display_mode,
            layout=layout_type,
            target_devices=target_devices,
            content_sources=content_sources,
            sync_enabled=sync_enabled
        )
        
        with self.lock:
            self.display_configs[config_id] = config
        
        logger.info(f"创建显示配置: {config_id}")
        return config
    
    def update_display_config(self, config_id: str, **kwargs) -> Optional[DisplayConfig]:
        """
        更新显示配置
        
        Args:
            config_id: 配置ID
            **kwargs: 要更新的字段
            
        Returns:
            DisplayConfig: 更新后的配置对象，如果不存在则返回None
        """
        with self.lock:
            if config_id not in self.display_configs:
                logger.warning(f"找不到显示配置: {config_id}")
                return None
            
            config = self.display_configs[config_id]
            
            # 更新字段
            for key, value in kwargs.items():
                if hasattr(config, key):
                    if key == 'mode' and isinstance(value, str):
                        config.mode = DisplayMode(value)
                    elif key == 'layout' and isinstance(value, str):
                        config.layout = LayoutType(value)
                    else:
                        setattr(config, key, value)
            
            config.updated_time = time.time()
            
            logger.info(f"更新显示配置: {config_id}")
            return config
    
    def delete_display_config(self, config_id: str) -> bool:
        """
        删除显示配置
        
        Args:
            config_id: 配置ID
            
        Returns:
            bool: 是否成功删除
        """
        with self.lock:
            if config_id in self.display_configs:
                del self.display_configs[config_id]
                logger.info(f"删除显示配置: {config_id}")
                return True
            return False
    
    def get_display_config(self, config_id: str) -> Optional[DisplayConfig]:
        """
        获取显示配置
        
        Args:
            config_id: 配置ID
            
        Returns:
            DisplayConfig: 配置对象，如果不存在则返回None
        """
        with self.lock:
            return self.display_configs.get(config_id)
    
    def list_display_configs(self, classroom_id: str = None) -> List[DisplayConfig]:
        """
        列出显示配置
        
        Args:
            classroom_id: 课堂ID，如果指定则只返回该课堂的配置
            
        Returns:
            List[DisplayConfig]: 配置列表
        """
        with self.lock:
            configs = list(self.display_configs.values())
            if classroom_id:
                configs = [c for c in configs if c.classroom_id == classroom_id]
            return configs
    
    def apply_display_config(self, config_id: str) -> bool:
        """
        应用显示配置
        
        Args:
            config_id: 配置ID
            
        Returns:
            bool: 是否成功应用
        """
        config = self.get_display_config(config_id)
        if not config:
            logger.error(f"找不到显示配置: {config_id}")
            return False
        
        try:
            # 根据显示模式执行相应操作
            if config.mode == DisplayMode.BROADCAST:
                return self._apply_broadcast_mode(config)
            elif config.mode == DisplayMode.INDEPENDENT:
                return self._apply_independent_mode(config)
            elif config.mode == DisplayMode.GROUP_BROADCAST:
                return self._apply_group_broadcast_mode(config)
            elif config.mode == DisplayMode.COMPARISON:
                return self._apply_comparison_mode(config)
            elif config.mode == DisplayMode.DUAL_SCREEN:
                return self._apply_dual_screen_mode(config)
            else:
                logger.error(f"不支持的显示模式: {config.mode}")
                return False
                
        except Exception as e:
            logger.error(f"应用显示配置时出错: {str(e)}")
            return False
    
    def _apply_broadcast_mode(self, config: DisplayConfig) -> bool:
        """应用广播模式"""
        logger.info(f"应用广播模式: {config.config_id}")
        
        # 将内容源广播到所有目标设备
        broadcast_data = {
            'mode': 'broadcast',
            'layout': config.layout.value,
            'content_sources': config.content_sources,
            'sync_enabled': config.sync_enabled,
            'timestamp': time.time()
        }
        
        # 记录活动显示状态
        with self.lock:
            self.active_displays[config.config_id] = {
                'config': config.to_dict(),
                'status': 'active',
                'start_time': time.time(),
                'target_devices': config.target_devices
            }
        
        return True
    
    def _apply_independent_mode(self, config: DisplayConfig) -> bool:
        """应用独立显示模式"""
        logger.info(f"应用独立显示模式: {config.config_id}")
        
        # 每个设备显示独立内容
        independent_data = {
            'mode': 'independent',
            'layout': config.layout.value,
            'device_content_mapping': dict(zip(config.target_devices, config.content_sources)),
            'sync_enabled': False,
            'timestamp': time.time()
        }
        
        with self.lock:
            self.active_displays[config.config_id] = {
                'config': config.to_dict(),
                'status': 'active',
                'start_time': time.time(),
                'target_devices': config.target_devices
            }
        
        return True
    
    def _apply_group_broadcast_mode(self, config: DisplayConfig) -> bool:
        """应用小组广播模式"""
        logger.info(f"应用小组广播模式: {config.config_id}")
        
        # 向指定小组广播内容
        group_broadcast_data = {
            'mode': 'group_broadcast',
            'layout': config.layout.value,
            'content_sources': config.content_sources,
            'target_groups': config.target_devices,  # 在小组广播模式下，target_devices实际是group_ids
            'sync_enabled': config.sync_enabled,
            'timestamp': time.time()
        }
        
        with self.lock:
            self.active_displays[config.config_id] = {
                'config': config.to_dict(),
                'status': 'active',
                'start_time': time.time(),
                'target_devices': config.target_devices
            }
        
        return True
    
    def _apply_comparison_mode(self, config: DisplayConfig) -> bool:
        """应用对比显示模式"""
        logger.info(f"应用对比显示模式: {config.config_id}")
        
        # 对比显示多个内容源
        comparison_data = {
            'mode': 'comparison',
            'layout': config.layout.value,
            'content_sources': config.content_sources,
            'comparison_layout': self._get_comparison_layout(config.layout, len(config.content_sources)),
            'sync_enabled': config.sync_enabled,
            'timestamp': time.time()
        }
        
        with self.lock:
            self.active_displays[config.config_id] = {
                'config': config.to_dict(),
                'status': 'active',
                'start_time': time.time(),
                'target_devices': config.target_devices
            }
        
        return True
    
    def _apply_dual_screen_mode(self, config: DisplayConfig) -> bool:
        """应用双屏联动模式"""
        logger.info(f"应用双屏联动模式: {config.config_id}")
        
        # 双屏联动显示
        dual_screen_data = {
            'mode': 'dual_screen',
            'layout': config.layout.value,
            'primary_content': config.content_sources[0] if config.content_sources else None,
            'secondary_content': config.content_sources[1] if len(config.content_sources) > 1 else None,
            'sync_enabled': config.sync_enabled,
            'timestamp': time.time()
        }
        
        with self.lock:
            self.active_displays[config.config_id] = {
                'config': config.to_dict(),
                'status': 'active',
                'start_time': time.time(),
                'target_devices': config.target_devices
            }
        
        return True
    
    def _get_comparison_layout(self, layout_type: LayoutType, content_count: int) -> Dict:
        """获取对比布局配置"""
        if layout_type == LayoutType.DUAL and content_count == 2:
            return {'rows': 1, 'cols': 2, 'positions': [(0, 0), (0, 1)]}
        elif layout_type == LayoutType.QUAD and content_count <= 4:
            return {'rows': 2, 'cols': 2, 'positions': [(0, 0), (0, 1), (1, 0), (1, 1)]}
        elif layout_type == LayoutType.GRID_2X2:
            return {'rows': 2, 'cols': 2, 'positions': [(i//2, i%2) for i in range(min(4, content_count))]}
        elif layout_type == LayoutType.GRID_3X3:
            return {'rows': 3, 'cols': 3, 'positions': [(i//3, i%3) for i in range(min(9, content_count))]}
        else:
            return {'rows': 1, 'cols': content_count, 'positions': [(0, i) for i in range(content_count)]}
    
    def create_group_timer(self, classroom_id: str, group_ids: List[str], 
                          title: str, duration: int) -> GroupTimer:
        """
        创建分组计时器
        
        Args:
            classroom_id: 课堂ID
            group_ids: 小组ID列表
            title: 计时器标题
            duration: 持续时间（秒）
            
        Returns:
            GroupTimer: 计时器对象
        """
        timer_id = f"timer_{classroom_id}_{int(time.time())}"
        
        timer = GroupTimer(
            timer_id=timer_id,
            classroom_id=classroom_id,
            group_ids=group_ids,
            title=title,
            duration=duration
        )
        
        with self.lock:
            self.group_timers[timer_id] = timer
        
        logger.info(f"创建分组计时器: {timer_id}")
        return timer
    
    def start_group_timer(self, timer_id: str) -> bool:
        """
        启动分组计时器
        
        Args:
            timer_id: 计时器ID
            
        Returns:
            bool: 是否成功启动
        """
        with self.lock:
            if timer_id not in self.group_timers:
                logger.warning(f"找不到计时器: {timer_id}")
                return False
            
            timer = self.group_timers[timer_id]
            timer.start()
            
            logger.info(f"启动分组计时器: {timer_id}")
            return True
    
    def pause_group_timer(self, timer_id: str) -> bool:
        """
        暂停分组计时器
        
        Args:
            timer_id: 计时器ID
            
        Returns:
            bool: 是否成功暂停
        """
        with self.lock:
            if timer_id not in self.group_timers:
                return False
            
            timer = self.group_timers[timer_id]
            timer.pause()
            
            logger.info(f"暂停分组计时器: {timer_id}")
            return True
    
    def resume_group_timer(self, timer_id: str) -> bool:
        """
        恢复分组计时器
        
        Args:
            timer_id: 计时器ID
            
        Returns:
            bool: 是否成功恢复
        """
        with self.lock:
            if timer_id not in self.group_timers:
                return False
            
            timer = self.group_timers[timer_id]
            timer.resume()
            
            logger.info(f"恢复分组计时器: {timer_id}")
            return True
    
    def stop_group_timer(self, timer_id: str) -> bool:
        """
        停止分组计时器
        
        Args:
            timer_id: 计时器ID
            
        Returns:
            bool: 是否成功停止
        """
        with self.lock:
            if timer_id not in self.group_timers:
                return False
            
            timer = self.group_timers[timer_id]
            timer.stop()
            
            logger.info(f"停止分组计时器: {timer_id}")
            return True
    
    def get_group_timer(self, timer_id: str) -> Optional[GroupTimer]:
        """
        获取分组计时器
        
        Args:
            timer_id: 计时器ID
            
        Returns:
            GroupTimer: 计时器对象，如果不存在则返回None
        """
        with self.lock:
            return self.group_timers.get(timer_id)
    
    def list_group_timers(self, classroom_id: str = None) -> List[GroupTimer]:
        """
        列出分组计时器
        
        Args:
            classroom_id: 课堂ID，如果指定则只返回该课堂的计时器
            
        Returns:
            List[GroupTimer]: 计时器列表
        """
        with self.lock:
            timers = list(self.group_timers.values())
            if classroom_id:
                timers = [t for t in timers if t.classroom_id == classroom_id]
            return timers
    
    def create_discussion_topic(self, classroom_id: str, title: str, content: str,
                              image_data: str = None, target_groups: List[str] = None) -> DiscussionTopic:
        """
        创建讨论主题
        
        Args:
            classroom_id: 课堂ID
            title: 主题标题
            content: 主题内容
            image_data: 图片数据（Base64编码）
            target_groups: 目标小组ID列表
            
        Returns:
            DiscussionTopic: 讨论主题对象
        """
        topic_id = f"topic_{classroom_id}_{int(time.time())}"
        
        topic = DiscussionTopic(
            topic_id=topic_id,
            classroom_id=classroom_id,
            title=title,
            content=content,
            image_data=image_data,
            target_groups=target_groups or []
        )
        
        with self.lock:
            self.discussion_topics[topic_id] = topic
        
        logger.info(f"创建讨论主题: {topic_id}")
        return topic
    
    def distribute_discussion_topic(self, topic_id: str) -> bool:
        """
        分发讨论主题
        
        Args:
            topic_id: 主题ID
            
        Returns:
            bool: 是否成功分发
        """
        with self.lock:
            if topic_id not in self.discussion_topics:
                logger.warning(f"找不到讨论主题: {topic_id}")
                return False
            
            topic = self.discussion_topics[topic_id]
            
            # 这里应该通过WebSocket或其他方式将主题分发到目标设备
            # 暂时记录分发日志
            logger.info(f"分发讨论主题: {topic_id} 到小组: {topic.target_groups}")
            return True
    
    def get_discussion_topic(self, topic_id: str) -> Optional[DiscussionTopic]:
        """
        获取讨论主题
        
        Args:
            topic_id: 主题ID
            
        Returns:
            DiscussionTopic: 主题对象，如果不存在则返回None
        """
        with self.lock:
            return self.discussion_topics.get(topic_id)
    
    def list_discussion_topics(self, classroom_id: str = None) -> List[DiscussionTopic]:
        """
        列出讨论主题
        
        Args:
            classroom_id: 课堂ID，如果指定则只返回该课堂的主题
            
        Returns:
            List[DiscussionTopic]: 主题列表
        """
        with self.lock:
            topics = list(self.discussion_topics.values())
            if classroom_id:
                topics = [t for t in topics if t.classroom_id == classroom_id]
            return topics
    
    def get_active_displays(self, classroom_id: str = None) -> List[Dict]:
        """
        获取活动显示状态
        
        Args:
            classroom_id: 课堂ID，如果指定则只返回该课堂的显示状态
            
        Returns:
            List[Dict]: 活动显示状态列表
        """
        with self.lock:
            displays = list(self.active_displays.values())
            if classroom_id:
                displays = [d for d in displays if d['config']['classroom_id'] == classroom_id]
            return displays
    
    def stop_display(self, config_id: str) -> bool:
        """
        停止显示
        
        Args:
            config_id: 配置ID
            
        Returns:
            bool: 是否成功停止
        """
        with self.lock:
            if config_id in self.active_displays:
                self.active_displays[config_id]['status'] = 'stopped'
                self.active_displays[config_id]['stop_time'] = time.time()
                logger.info(f"停止显示: {config_id}")
                return True
            return False
    
    def _timer_update_loop(self):
        """计时器更新循环"""
        while True:
            try:
                with self.lock:
                    # 更新所有活动计时器
                    for timer in self.group_timers.values():
                        if timer.is_active:
                            remaining = timer.get_remaining_time()
                            if remaining == 0:
                                logger.info(f"计时器已结束: {timer.timer_id}")
                
                time.sleep(1)  # 每秒更新一次
                
            except Exception as e:
                logger.error(f"计时器更新循环出错: {str(e)}")
                time.sleep(5)  # 出错时等待5秒再继续
    
    def cleanup(self):
        """清理资源"""
        with self.lock:
            # 停止所有活动显示
            for config_id in list(self.active_displays.keys()):
                self.stop_display(config_id)
            
            # 停止所有计时器
            for timer_id in list(self.group_timers.keys()):
                self.stop_group_timer(timer_id)
            
            logger.info("多屏显示服务资源已清理")

# 单例模式
_display_service_instance = None

def get_display_service() -> MultiScreenDisplayService:
    """获取多屏显示服务单例"""
    global _display_service_instance
    if _display_service_instance is None:
        _display_service_instance = MultiScreenDisplayService()
    return _display_service_instance