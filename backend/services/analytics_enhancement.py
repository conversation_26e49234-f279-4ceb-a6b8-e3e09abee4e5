# -*- coding: utf-8 -*-
"""
学习分析增强服务 - 历史数据对比和个性化推荐
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy import func, and_, or_, desc
from collections import defaultdict

from ..models.base import db
from ..models.classroom import Classroom, ClassroomActivity
from ..models.student import Student, StudentActivity
from ..models.question import Question, Answer
from ..models.group import Group
from ..models.report import ClassroomReport, LearningAnalytics


class HistoricalAnalyticsService:
    """历史数据分析服务"""
    
    @staticmethod
    def compare_classroom_performance(classroom_ids: List[int], 
                                    comparison_type: str = 'attendance') -> Dict[str, Any]:
        """对比多个课堂的表现"""
        results = {
            'comparison_type': comparison_type,
            'classrooms': [],
            'metrics': {},
            'insights': []
        }
        
        for classroom_id in classroom_ids:
            classroom = Classroom.get_by_id(classroom_id)
            if not classroom:
                continue
                
            classroom_data = {
                'classroom_