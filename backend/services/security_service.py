# -*- coding: utf-8 -*-
"""
安全服务
"""

import secrets
import hashlib
import datetime
import jwt
import ipaddress
from typing import Optional, List, Dict, Any
from flask import current_app, request, g
from sqlalchemy.orm import sessionmaker
from sqlalchemy import and_, or_

from models.base import db
from models.security import (
    User, UserSession, UserPermission, DeviceAccess, 
    AuditLog, SecurityConfig, DataEncryption,
    UserRole, Permission
)


class SecurityService:
    """安全服务类"""
    
    def __init__(self):
        self.session = db.session
        self.configs = {}
        self._configs_loaded = False
    
    def _ensure_configs_loaded(self):
        """确保配置已加载"""
        if not self._configs_loaded:
            self._load_security_configs()
            self._configs_loaded = True
    def _load_security_configs(self):
        """加载安全配置"""
        try: 
            configs = self.session.query(SecurityConfig).all()
            for config in configs:
                self.configs[config.key] = config.value
        except Exception as e:
            print(f"Error loading security configs: {e}")
        # 设置默认配置
        self._set_default_configs()
    
    def _set_default_configs(self):
        """设置默认安全配置"""
        default_configs = {
            'password_policy': {
                'min_length': 8,
                'require_uppercase': True,
                'require_lowercase': True,
                'require_numbers': True,
                'require_special_chars': True,
                'max_age_days': 90,
                'history_count': 5
            },
            'session_policy': {
                'max_duration_hours': 24,
                'idle_timeout_minutes': 30,
                'max_concurrent_sessions': 3
            },
            'login_policy': {
                'max_failed_attempts': 5,
                'lockout_duration_minutes': 30,
                'require_2fa': False
            },
            'encryption_policy': {
                'algorithm': 'AES-256-GCM',
                'key_rotation_days': 30,
                'encrypt_sensitive_data': True
            },
            'audit_policy': {
                'log_all_actions': True,
                'retention_days': 365,
                'log_failed_attempts': True
            }
        }
        
        for key, value in default_configs.items():
            if key not in self.configs:
                self.configs[key] = value
    
    # 用户认证相关方法
    def authenticate_user(self, username: str, password: str, device_info: Dict = None) -> Dict:
        """用户认证"""
        self._ensure_configs_loaded()
        try:
            # 查找用户
            user = self.session.query(User).filter_by(username=username).first()
            if not user:
                self._log_audit('login_failed', details={'username': username, 'reason': 'user_not_found'})
                return {'success': False, 'message': '用户名或密码错误'}
            
            # 检查账户状态
            if not user.is_active:
                self._log_audit('login_failed', user_id=user.id, details={'reason': 'account_inactive'})
                return {'success': False, 'message': '账户已被禁用'}
            
            # 检查账户锁定
            if user.is_locked():
                self._log_audit('login_failed', user_id=user.id, details={'reason': 'account_locked'})
                return {'success': False, 'message': '账户已被锁定，请稍后再试'}
            
            # 验证密码
            if not user.check_password(password):
                user.failed_login_attempts += 1
                
                # 检查是否需要锁定账户
                max_attempts = self.configs['login_policy']['max_failed_attempts']
                if user.failed_login_attempts >= max_attempts:
                    lockout_duration = self.configs['login_policy']['lockout_duration_minutes']
                    user.lock_account(lockout_duration)
                    self._log_audit('account_locked', user_id=user.id, 
                                  details={'failed_attempts': user.failed_login_attempts})
                
                self.session.commit()
                self._log_audit('login_failed', user_id=user.id, details={'reason': 'invalid_password'})
                return {'success': False, 'message': '用户名或密码错误'}
            
            # 检查密码是否过期
            if user.password_expires_at and user.password_expires_at < datetime.datetime.utcnow():
                self._log_audit('login_failed', user_id=user.id, details={'reason': 'password_expired'})
                return {'success': False, 'message': '密码已过期，请联系管理员重置', 'require_password_change': True}
            
            # 重置失败尝试次数
            user.failed_login_attempts = 0
            user.last_login = datetime.datetime.utcnow()
            
            # 创建会话
            session_data = self._create_user_session(user, device_info)
            
            self.session.commit()
            self._log_audit('login_success', user_id=user.id, details={'device_info': device_info})
            
            return {
                'success': True,
                'message': '登录成功',
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'name': user.name,
                    'role': user.role,
                    'permissions': self._get_user_permissions(user)
                },
                'session': session_data
            }
            
        except Exception as e:
            self.session.rollback()
            self._log_audit('login_error', details={'error': str(e)})
            return {'success': False, 'message': '登录过程中发生错误'}
    
    def _create_user_session(self, user: User, device_info: Dict = None) -> Dict:
        """创建用户会话"""
        # 检查并清理过期会话
        self._cleanup_expired_sessions(user.id)
        
        # 检查并发会话限制
        max_sessions = self.configs['session_policy']['max_concurrent_sessions']
        active_sessions = self.session.query(UserSession).filter(
            and_(UserSession.user_id == user.id, UserSession.is_active == True)
        ).count()
        
        if active_sessions >= max_sessions:
            # 删除最旧的会话
            oldest_session = self.session.query(UserSession).filter(
                and_(UserSession.user_id == user.id, UserSession.is_active == True)
            ).order_by(UserSession.last_activity).first()
            if oldest_session:
                oldest_session.is_active = False
        
        # 创建新会话
        session_token = secrets.token_urlsafe(32)
        refresh_token = secrets.token_urlsafe(32)
        
        duration_hours = self.configs['session_policy']['max_duration_hours']
        expires_at = datetime.datetime.utcnow() + datetime.timedelta(hours=duration_hours)
        
        user_session = UserSession(
            user_id=user.id,
            session_token=session_token,
            refresh_token=refresh_token,
            device_info=device_info,
            ip_address=self._get_client_ip(),
            user_agent=request.headers.get('User-Agent'),
            expires_at=expires_at
        )
        
        self.session.add(user_session)
        
        return {
            'token': session_token,
            'refresh_token': refresh_token,
            'expires_at': expires_at.isoformat()
        }
    
    def validate_session(self, session_token: str) -> Optional[User]:
        """验证会话"""
        try:
            user_session = self.session.query(UserSession).filter_by(
                session_token=session_token, is_active=True
            ).first()
            
            if not user_session:
                return None
            
            # 检查会话是否过期
            if user_session.is_expired():
                user_session.is_active = False
                self.session.commit()
                return None
            
            # 检查空闲超时
            idle_timeout = self.configs['session_policy']['idle_timeout_minutes']
            idle_limit = datetime.datetime.utcnow() - datetime.timedelta(minutes=idle_timeout)
            if user_session.last_activity < idle_limit:
                user_session.is_active = False
                self.session.commit()
                self._log_audit('session_timeout', user_id=user_session.user_id)
                return None
            
            # 更新最后活动时间
            user_session.last_activity = datetime.datetime.utcnow()
            self.session.commit()
            
            return user_session.user
            
        except Exception as e:
            self.session.rollback()
            self._log_audit('session_validation_error', details={'error': str(e)})
            return None
    
    def logout_user(self, session_token: str) -> bool:
        """用户登出"""
        try:
            user_session = self.session.query(UserSession).filter_by(
                session_token=session_token, is_active=True
            ).first()
            
            if user_session:
                user_session.is_active = False
                self.session.commit()
                self._log_audit('logout', user_id=user_session.user_id)
                return True
            
            return False
            
        except Exception as e:
            self.session.rollback()
            self._log_audit('logout_error', details={'error': str(e)})
            return False
    
    def _cleanup_expired_sessions(self, user_id: int = None):
        """清理过期会话"""
        query = self.session.query(UserSession).filter(
            UserSession.expires_at < datetime.datetime.utcnow()
        )
        
        if user_id:
            query = query.filter(UserSession.user_id == user_id)
        
        expired_sessions = query.all()
        for session in expired_sessions:
            session.is_active = False
    
    # 权限管理相关方法
    def check_permission(self, user: User, permission: Permission, resource_id: str = None) -> bool:
        """检查用户权限"""
        try:
            # 基本权限检查
            if not user.has_permission(permission):
                return False
            
            # 资源级权限检查（如果需要）
            if resource_id:
                return self._check_resource_permission(user, permission, resource_id)
            
            return True
            
        except Exception as e:
            self._log_audit('permission_check_error', user_id=user.id, 
                          details={'permission': permission.value, 'error': str(e)})
            return False
    
    def _check_resource_permission(self, user: User, permission: Permission, resource_id: str) -> bool:
        """检查资源级权限"""
        # 这里可以实现更细粒度的资源权限检查
        # 例如：检查用户是否有权限访问特定的课堂、小组等
        return True
    
    def grant_permission(self, user_id: int, permission: Permission, granted_by: int, 
                        expires_at: datetime.datetime = None) -> bool:
        """授予权限"""
        try:
            # 检查权限是否已存在
            existing = self.session.query(UserPermission).filter_by(
                user_id=user_id, permission=permission.value
            ).first()
            
            if existing:
                # 更新现有权限
                existing.granted_by = granted_by
                existing.granted_at = datetime.datetime.utcnow()
                existing.expires_at = expires_at
            else:
                # 创建新权限
                user_permission = UserPermission(
                    user_id=user_id,
                    permission=permission.value,
                    granted_by=granted_by,
                    expires_at=expires_at
                )
                self.session.add(user_permission)
            
            self.session.commit()
            self._log_audit('permission_granted', user_id=granted_by,
                          details={'target_user': user_id, 'permission': permission.value})
            return True
            
        except Exception as e:
            self.session.rollback()
            self._log_audit('permission_grant_error', user_id=granted_by,
                          details={'error': str(e)})
            return False
    
    def revoke_permission(self, user_id: int, permission: Permission, revoked_by: int) -> bool:
        """撤销权限"""
        try:
            user_permission = self.session.query(UserPermission).filter_by(
                user_id=user_id, permission=permission.value
            ).first()
            
            if user_permission:
                self.session.delete(user_permission)
                self.session.commit()
                self._log_audit('permission_revoked', user_id=revoked_by,
                              details={'target_user': user_id, 'permission': permission.value})
                return True
            
            return False
            
        except Exception as e:
            self.session.rollback()
            self._log_audit('permission_revoke_error', user_id=revoked_by,
                          details={'error': str(e)})
            return False
    
    def _get_user_permissions(self, user: User) -> List[str]:
        """获取用户权限列表"""
        permissions = []
        
        # 添加角色默认权限
        if user.role == UserRole.ADMIN.value:
            permissions = [perm.value for perm in Permission]
        elif user.role == UserRole.TEACHER.value:
            teacher_permissions = [
                Permission.CLASSROOM_CREATE, Permission.CLASSROOM_MANAGE,
                Permission.GROUP_CREATE, Permission.GROUP_MANAGE,
                Permission.CONTENT_CREATE, Permission.CONTENT_EDIT, Permission.CONTENT_SHARE,
                Permission.FILE_UPLOAD, Permission.FILE_DOWNLOAD, Permission.FILE_SHARE,
                Permission.VIDEO_BROADCAST, Permission.VIDEO_RECORD, Permission.VIDEO_VIEW,
                Permission.WHITEBOARD_CREATE, Permission.WHITEBOARD_EDIT, Permission.WHITEBOARD_VIEW,
                Permission.QUESTION_CREATE, Permission.QUESTION_PUBLISH, Permission.QUESTION_VIEW_RESULTS,
                Permission.REPORT_VIEW, Permission.REPORT_EXPORT
            ]
            permissions = [perm.value for perm in teacher_permissions]
        elif user.role == UserRole.STUDENT.value:
            student_permissions = [
                Permission.CONTENT_CREATE, Permission.FILE_DOWNLOAD,
                Permission.VIDEO_VIEW, Permission.WHITEBOARD_VIEW, Permission.WHITEBOARD_EDIT
            ]
            permissions = [perm.value for perm in student_permissions]
        
        # 添加用户特定权限
        for user_perm in user.permissions:
            if user_perm.permission not in permissions:
                # 检查权限是否过期
                if not user_perm.expires_at or user_perm.expires_at > datetime.datetime.utcnow():
                    permissions.append(user_perm.permission)
        
        return permissions
    
    # 设备访问控制相关方法
    def check_device_access(self, device_id: str, user_id: int, operation: str) -> bool:
        """检查设备访问权限"""
        try:
            # 查找设备访问规则
            device_access = self.session.query(DeviceAccess).filter_by(
                device_id=device_id, user_id=user_id
            ).first()
            
            if not device_access:
                # 检查通用规则
                device_access = self.session.query(DeviceAccess).filter_by(
                    device_id=device_id, user_id=None
                ).first()
            
            if not device_access:
                # 默认允许（可根据需要修改）
                return True
            
            # 检查访问类型
            if device_access.access_type == 'denied':
                return False
            
            # 检查操作权限
            if device_access.allowed_operations:
                if operation not in device_access.allowed_operations:
                    return False
            
            # 检查时间限制
            if device_access.time_restrictions:
                if not self._check_time_restrictions(device_access.time_restrictions):
                    return False
            
            # 检查IP限制
            if device_access.ip_restrictions:
                if not self._check_ip_restrictions(device_access.ip_restrictions):
                    return False
            
            return True
            
        except Exception as e:
            self._log_audit('device_access_check_error', user_id=user_id,
                          details={'device_id': device_id, 'error': str(e)})
            return False
    
    def _check_time_restrictions(self, time_restrictions: Dict) -> bool:
        """检查时间限制"""
        now = datetime.datetime.now()
        
        # 检查时间段限制
        if 'allowed_hours' in time_restrictions:
            current_hour = now.hour
            allowed_hours = time_restrictions['allowed_hours']
            if current_hour not in allowed_hours:
                return False
        
        # 检查星期限制
        if 'allowed_weekdays' in time_restrictions:
            current_weekday = now.weekday()  # 0=Monday, 6=Sunday
            allowed_weekdays = time_restrictions['allowed_weekdays']
            if current_weekday not in allowed_weekdays:
                return False
        
        return True
    
    def _check_ip_restrictions(self, ip_restrictions: Dict) -> bool:
        """检查IP限制"""
        client_ip = self._get_client_ip()
        
        if 'allowed_ips' in ip_restrictions:
            allowed_ips = ip_restrictions['allowed_ips']
            if client_ip not in allowed_ips:
                return False
        
        if 'allowed_networks' in ip_restrictions:
            allowed_networks = ip_restrictions['allowed_networks']
            client_in_network = False
            for network in allowed_networks:
                try:
                    if ipaddress.ip_address(client_ip) in ipaddress.ip_network(network):
                        client_in_network = True
                        break
                except ValueError:
                    continue
            
            if not client_in_network:
                return False
        
        return True
    
    # 审计日志相关方法
    def _log_audit(self, action: str, user_id: int = None, resource_type: str = None,
                   resource_id: str = None, details: Dict = None, result: str = 'success',
                   error_message: str = None):
        """记录审计日志"""
        try:
            audit_log = AuditLog(
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                ip_address=self._get_client_ip(),
                user_agent=request.headers.get('User-Agent') if request else None,
                request_method=request.method if request else None,
                request_url=request.url if request else None,
                details=details,
                result=result,
                error_message=error_message
            )
            
            self.session.add(audit_log)
            self.session.commit()
            
        except Exception as e:
            # 审计日志记录失败不应影响主要功能
            print(f"审计日志记录失败: {str(e)}")
    
    def get_audit_logs(self, user_id: int = None, action: str = None, 
                      start_date: datetime.datetime = None, 
                      end_date: datetime.datetime = None,
                      limit: int = 100) -> List[AuditLog]:
        """获取审计日志"""
        query = self.session.query(AuditLog)
        
        if user_id:
            query = query.filter(AuditLog.user_id == user_id)
        
        if action:
            query = query.filter(AuditLog.action == action)
        
        if start_date:
            query = query.filter(AuditLog.created_at >= start_date)
        
        if end_date:
            query = query.filter(AuditLog.created_at <= end_date)
        
        return query.order_by(AuditLog.created_at.desc()).limit(limit).all()
    
    # 数据加密相关方法
    def encrypt_sensitive_data(self, data: str, data_type: str, data_id: str) -> str:
        """加密敏感数据"""
        if not self.configs['encryption_policy']['encrypt_sensitive_data']:
            return data
        
        try:
            # 这里使用简单的加密示例，实际应用中应使用更强的加密算法
            key = self._get_encryption_key()
            encrypted_data = self._encrypt_data(data, key)
            
            # 记录加密信息
            encryption_record = DataEncryption(
                data_type=data_type,
                data_id=data_id,
                encryption_algorithm=self.configs['encryption_policy']['algorithm'],
                key_id=self._get_key_id()
            )
            
            self.session.add(encryption_record)
            self.session.commit()
            
            return encrypted_data
            
        except Exception as e:
            self._log_audit('encryption_error', details={'error': str(e)})
            return data
    
    def decrypt_sensitive_data(self, encrypted_data: str, data_type: str, data_id: str) -> str:
        """解密敏感数据"""
        try:
            # 查找加密记录
            encryption_record = self.session.query(DataEncryption).filter_by(
                data_type=data_type, data_id=data_id
            ).first()
            
            if not encryption_record or not encryption_record.is_encrypted:
                return encrypted_data
            
            key = self._get_encryption_key(encryption_record.key_id)
            decrypted_data = self._decrypt_data(encrypted_data, key)
            
            return decrypted_data
            
        except Exception as e:
            self._log_audit('decryption_error', details={'error': str(e)})
            return encrypted_data
    
    def _encrypt_data(self, data: str, key: str) -> str:
        """数据加密实现"""
        # 简单的加密实现，实际应用中应使用AES等强加密算法
        return hashlib.sha256((data + key).encode()).hexdigest()
    
    def _decrypt_data(self, encrypted_data: str, key: str) -> str:
        """数据解密实现"""
        # 这里只是示例，实际应用中需要实现真正的解密
        return encrypted_data
    
    def _get_encryption_key(self, key_id: str = None) -> str:
        """获取加密密钥"""
        # 实际应用中应从安全的密钥管理系统获取
        return current_app.config.get('ENCRYPTION_KEY', 'default_key')
    
    def _get_key_id(self) -> str:
        """获取密钥ID"""
        return 'default_key_id'
    
    # 工具方法
    def _get_client_ip(self) -> str:
        """获取客户端IP地址"""
        if not request:
            return '127.0.0.1'
        
        # 检查代理头
        if request.headers.get('X-Forwarded-For'):
            return request.headers.get('X-Forwarded-For').split(',')[0].strip()
        elif request.headers.get('X-Real-IP'):
            return request.headers.get('X-Real-IP')
        else:
            return request.remote_addr or '127.0.0.1'
    
    def validate_password_policy(self, password: str) -> Dict:
        """验证密码策略"""
        policy = self.configs['password_policy']
        errors = []
        
        if len(password) < policy['min_length']:
            errors.append(f"密码长度至少{policy['min_length']}位")
        
        if policy['require_uppercase'] and not any(c.isupper() for c in password):
            errors.append("密码必须包含大写字母")
        
        if policy['require_lowercase'] and not any(c.islower() for c in password):
            errors.append("密码必须包含小写字母")
        
        if policy['require_numbers'] and not any(c.isdigit() for c in password):
            errors.append("密码必须包含数字")
        
        if policy['require_special_chars'] and not any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in password):
            errors.append("密码必须包含特殊字符")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }


# 全局安全服务实例
_security_service = None

def get_security_service() -> SecurityService:
    """获取安全服务实例"""
    global _security_service
    if _security_service is None:
        from flask import has_app_context
        if has_app_context():
            _security_service = SecurityService()
        else:
            # 如果没有应用上下文，返回一个延迟初始化的实例
            _security_service = SecurityService()
    return _security_service