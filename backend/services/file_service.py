# -*- coding: utf-8 -*-
"""
文件管理服务 - 增强版文件管理和分发系统
"""

import os
import uuid
import hashlib
import mimetypes
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from werkzeug.utils import secure_filename
from sqlalchemy import and_, or_, desc, func
try:
    from ..models import db
except ImportError:
    # For standalone testing
    db = None


class FileVersionService:
    """文件版本管理服务"""
    
    def __init__(self):
        self.upload_folder = 'instance/uploads'
        self.version_folder = 'instance/versions'
        os.makedirs(self.upload_folder, exist_ok=True)
        os.makedirs(self.version_folder, exist_ok=True)
    
    def calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def create_version(self, file_record, file_path: str, version_note: str = None) -> dict:
        """创建文件版本"""
        from ..api.file_api import FileVersion
        
        file_hash = self.calculate_file_hash(file_path)
        version_number = self.get_next_version_number(file_record.id)
        
        # 创建版本目录
        version_dir = os.path.join(self.version_folder, file_record.id)
        os.makedirs(version_dir, exist_ok=True)
        
        # 复制文件到版本目录
        version_filename = f"v{version_number}_{file_record.stored_filename}"
        version_path = os.path.join(version_dir, version_filename)
        
        import shutil
        shutil.copy2(file_path, version_path)
        
        # 创建版本记录
        version = FileVersion(
            id=uuid.uuid4().hex,
            file_id=file_record.id,
            version_number=version_number,
            file_hash=file_hash,
            file_size=os.path.getsize(version_path),
            stored_path=version_path,
            version_note=version_note,
            created_by=file_record.uploader_id
        )
        
        db.session.add(version)
        db.session.commit()
        
        return version.to_dict()
    
    def get_next_version_number(self, file_id: str) -> int:
        """获取下一个版本号"""
        from ..api.file_api import FileVersion
        
        latest_version = FileVersion.query.filter_by(file_id=file_id)\
                                         .order_by(desc(FileVersion.version_number))\
                                         .first()
        
        return (latest_version.version_number + 1) if latest_version else 1
    
    def get_file_versions(self, file_id: str) -> List[dict]:
        """获取文件所有版本"""
        from ..api.file_api import FileVersion
        
        versions = FileVersion.query.filter_by(file_id=file_id)\
                                   .order_by(desc(FileVersion.version_number))\
                                   .all()
        
        return [version.to_dict() for version in versions]
    
    def restore_version(self, file_id: str, version_number: int) -> bool:
        """恢复到指定版本"""
        from ..api.file_api import FileRecord, FileVersion
        
        file_record = FileRecord.query.get(file_id)
        version = FileVersion.query.filter_by(
            file_id=file_id, 
            version_number=version_number
        ).first()
        
        if not file_record or not version:
            return False
        
        # 复制版本文件到当前位置
        current_path = os.path.join(self.upload_folder, file_record.stored_filename)
        import shutil
        shutil.copy2(version.stored_path, current_path)
        
        # 更新文件记录
        file_record.file_size = version.file_size
        file_record.modified_time = datetime.utcnow()
        
        db.session.commit()
        return True


class ChunkedUploadService:
    """分块上传服务 - 支持大文件和断点续传"""
    
    def __init__(self):
        self.chunk_folder = 'instance/chunks'
        self.chunk_size = 1024 * 1024  # 1MB chunks
        os.makedirs(self.chunk_folder, exist_ok=True)
    
    def init_upload(self, filename: str, total_size: int, total_chunks: int, 
                   uploader_id: str, classroom_id: str = None) -> dict:
        """初始化分块上传"""
        from ..api.file_api import ChunkedUpload
        
        upload_id = uuid.uuid4().hex
        secure_name = secure_filename(filename)
        
        upload_session = ChunkedUpload(
            id=upload_id,
            original_filename=secure_name,
            total_size=total_size,
            total_chunks=total_chunks,
            uploader_id=uploader_id,
            classroom_id=classroom_id,
            status='initialized'
        )
        
        db.session.add(upload_session)
        db.session.commit()
        
        # 创建上传目录
        upload_dir = os.path.join(self.chunk_folder, upload_id)
        os.makedirs(upload_dir, exist_ok=True)
        
        return {
            'upload_id': upload_id,
            'chunk_size': self.chunk_size,
            'upload_url': f'/api/files/chunk/{upload_id}'
        }
    
    def upload_chunk(self, upload_id: str, chunk_number: int, chunk_data: bytes) -> dict:
        """上传文件块"""
        from ..api.file_api import ChunkedUpload
        
        upload_session = ChunkedUpload.query.get(upload_id)
        if not upload_session:
            raise ValueError("Upload session not found")
        
        # 保存块文件
        chunk_path = os.path.join(self.chunk_folder, upload_id, f"chunk_{chunk_number}")
        with open(chunk_path, 'wb') as f:
            f.write(chunk_data)
        
        # 更新上传进度
        upload_session.uploaded_chunks = upload_session.uploaded_chunks or []
        if chunk_number not in upload_session.uploaded_chunks:
            upload_session.uploaded_chunks.append(chunk_number)
        
        upload_session.last_chunk_time = datetime.utcnow()
        
        # 检查是否完成
        if len(upload_session.uploaded_chunks) == upload_session.total_chunks:
            upload_session.status = 'completed'
            self._merge_chunks(upload_session)
        
        db.session.commit()
        
        return {
            'chunk_number': chunk_number,
            'uploaded_chunks': len(upload_session.uploaded_chunks),
            'total_chunks': upload_session.total_chunks,
            'progress': len(upload_session.uploaded_chunks) / upload_session.total_chunks * 100,
            'status': upload_session.status
        }
    
    def _merge_chunks(self, upload_session) -> str:
        """合并文件块"""
        from ..api.file_api import FileRecord
        
        upload_dir = os.path.join(self.chunk_folder, upload_session.id)
        
        # 生成最终文件名
        file_ext = upload_session.original_filename.rsplit('.', 1)[1].lower()
        stored_filename = f"{uuid.uuid4().hex}.{file_ext}"
        final_path = os.path.join('instance/uploads', stored_filename)
        
        # 合并块文件
        with open(final_path, 'wb') as outfile:
            for i in range(upload_session.total_chunks):
                chunk_path = os.path.join(upload_dir, f"chunk_{i}")
                if os.path.exists(chunk_path):
                    with open(chunk_path, 'rb') as infile:
                        outfile.write(infile.read())
        
        # 创建文件记录
        file_record = FileRecord(
            id=uuid.uuid4().hex,
            original_filename=upload_session.original_filename,
            stored_filename=stored_filename,
            file_type=file_ext,
            file_size=os.path.getsize(final_path),
            uploader_id=upload_session.uploader_id,
            classroom_id=upload_session.classroom_id
        )
        
        db.session.add(file_record)
        
        # 清理块文件
        import shutil
        shutil.rmtree(upload_dir)
        
        return file_record.id
    
    def get_upload_status(self, upload_id: str) -> dict:
        """获取上传状态"""
        from ..api.file_api import ChunkedUpload
        
        upload_session = ChunkedUpload.query.get(upload_id)
        if not upload_session:
            return None
        
        return {
            'upload_id': upload_id,
            'status': upload_session.status,
            'uploaded_chunks': len(upload_session.uploaded_chunks or []),
            'total_chunks': upload_session.total_chunks,
            'progress': len(upload_session.uploaded_chunks or []) / upload_session.total_chunks * 100,
            'last_activity': upload_session.last_chunk_time.isoformat() if upload_session.last_chunk_time else None
        }


class FilePreviewService:
    """文件预览服务"""
    
    def __init__(self):
        self.preview_folder = 'instance/previews'
        os.makedirs(self.preview_folder, exist_ok=True)
        
        # 支持的预览格式
        self.preview_handlers = {
            'pdf': self._generate_pdf_preview,
            'doc': self._generate_doc_preview,
            'docx': self._generate_doc_preview,
            'ppt': self._generate_ppt_preview,
            'pptx': self._generate_ppt_preview,
            'xls': self._generate_excel_preview,
            'xlsx': self._generate_excel_preview,
            'jpg': self._generate_image_preview,
            'jpeg': self._generate_image_preview,
            'png': self._generate_image_preview,
            'gif': self._generate_image_preview,
            'mp4': self._generate_video_preview,
            'avi': self._generate_video_preview,
            'txt': self._generate_text_preview,
            'md': self._generate_markdown_preview
        }
    
    def generate_preview(self, file_record) -> dict:
        """生成文件预览"""
        file_type = file_record.file_type.lower()
        
        if file_type not in self.preview_handlers:
            return {
                'supported': False,
                'message': f'不支持 {file_type} 格式的预览'
            }
        
        try:
            preview_data = self.preview_handlers[file_type](file_record)
            return {
                'supported': True,
                'file_id': file_record.id,
                'preview_type': preview_data.get('type', 'html'),
                'preview_url': preview_data.get('url'),
                'preview_content': preview_data.get('content'),
                'thumbnail_url': preview_data.get('thumbnail')
            }
        except Exception as e:
            return {
                'supported': False,
                'error': str(e)
            }
    
    def _generate_pdf_preview(self, file_record) -> dict:
        """生成PDF预览"""
        # 使用pdf2image或PyMuPDF生成缩略图
        return {
            'type': 'pdf',
            'url': f'/api/files/preview/{file_record.id}/pdf',
            'thumbnail': f'/api/files/preview/{file_record.id}/thumbnail'
        }
    
    def _generate_doc_preview(self, file_record) -> dict:
        """生成Word文档预览"""
        # 使用python-docx或mammoth转换为HTML
        return {
            'type': 'html',
            'url': f'/api/files/preview/{file_record.id}/html'
        }
    
    def _generate_ppt_preview(self, file_record) -> dict:
        """生成PPT预览"""
        # 使用python-pptx生成幻灯片预览
        return {
            'type': 'slides',
            'url': f'/api/files/preview/{file_record.id}/slides'
        }
    
    def _generate_excel_preview(self, file_record) -> dict:
        """生成Excel预览"""
        # 使用openpyxl或pandas生成表格预览
        return {
            'type': 'table',
            'url': f'/api/files/preview/{file_record.id}/table'
        }
    
    def _generate_image_preview(self, file_record) -> dict:
        """生成图片预览"""
        return {
            'type': 'image',
            'url': f'/api/files/download/{file_record.id}',
            'thumbnail': f'/api/files/preview/{file_record.id}/thumbnail'
        }
    
    def _generate_video_preview(self, file_record) -> dict:
        """生成视频预览"""
        # 使用ffmpeg生成视频缩略图和预览
        return {
            'type': 'video',
            'url': f'/api/files/download/{file_record.id}',
            'thumbnail': f'/api/files/preview/{file_record.id}/thumbnail',
            'poster': f'/api/files/preview/{file_record.id}/poster'
        }
    
    def _generate_text_preview(self, file_record) -> dict:
        """生成文本预览"""
        file_path = os.path.join('instance/uploads', file_record.stored_filename)
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read(5000)  # 前5000字符
            return {
                'type': 'text',
                'content': content
            }
        except UnicodeDecodeError:
            return {
                'type': 'binary',
                'message': '二进制文件，无法预览'
            }
    
    def _generate_markdown_preview(self, file_record) -> dict:
        """生成Markdown预览"""
        file_path = os.path.join('instance/uploads', file_record.stored_filename)
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 可以使用markdown库转换为HTML
        return {
            'type': 'markdown',
            'content': content,
            'html_url': f'/api/files/preview/{file_record.id}/markdown'
        }


class FilePermissionService:
    """文件权限控制服务"""
    
    def __init__(self):
        self.default_permissions = {
            'read': True,
            'write': False,
            'delete': False,
            'share': False
        }
    
    def set_file_permissions(self, file_id: str, user_id: str, permissions: dict) -> bool:
        """设置文件权限"""
        from ..api.file_api import FilePermission
        
        # 删除现有权限
        FilePermission.query.filter_by(file_id=file_id, user_id=user_id).delete()
        
        # 创建新权限
        permission = FilePermission(
            id=uuid.uuid4().hex,
            file_id=file_id,
            user_id=user_id,
            can_read=permissions.get('read', True),
            can_write=permissions.get('write', False),
            can_delete=permissions.get('delete', False),
            can_share=permissions.get('share', False),
            granted_by=permissions.get('granted_by'),
            granted_at=datetime.utcnow()
        )
        
        db.session.add(permission)
        db.session.commit()
        return True
    
    def check_permission(self, file_id: str, user_id: str, action: str) -> bool:
        """检查用户权限"""
        from ..api.file_api import FilePermission, FileRecord
        
        # 检查是否是文件所有者
        file_record = FileRecord.query.get(file_id)
        if file_record and file_record.uploader_id == user_id:
            return True
        
        # 检查显式权限
        permission = FilePermission.query.filter_by(
            file_id=file_id, 
            user_id=user_id
        ).first()
        
        if not permission:
            return self.default_permissions.get(action, False)
        
        permission_map = {
            'read': permission.can_read,
            'write': permission.can_write,
            'delete': permission.can_delete,
            'share': permission.can_share
        }
        
        return permission_map.get(action, False)
    
    def get_file_permissions(self, file_id: str) -> List[dict]:
        """获取文件权限列表"""
        from ..api.file_api import FilePermission
        
        permissions = FilePermission.query.filter_by(file_id=file_id).all()
        return [perm.to_dict() for perm in permissions]


class FileAuditService:
    """文件访问审计服务"""
    
    def log_access(self, file_id: str, user_id: str, action: str, 
                   ip_address: str = None, user_agent: str = None) -> None:
        """记录文件访问日志"""
        from ..api.file_api import FileAccessLog
        
        log_entry = FileAccessLog(
            id=uuid.uuid4().hex,
            file_id=file_id,
            user_id=user_id,
            action=action,
            ip_address=ip_address,
            user_agent=user_agent,
            timestamp=datetime.utcnow()
        )
        
        db.session.add(log_entry)
        db.session.commit()
    
    def get_access_logs(self, file_id: str = None, user_id: str = None, 
                       start_date: datetime = None, end_date: datetime = None,
                       limit: int = 100) -> List[dict]:
        """获取访问日志"""
        from ..api.file_api import FileAccessLog
        
        query = FileAccessLog.query
        
        if file_id:
            query = query.filter_by(file_id=file_id)
        
        if user_id:
            query = query.filter_by(user_id=user_id)
        
        if start_date:
            query = query.filter(FileAccessLog.timestamp >= start_date)
        
        if end_date:
            query = query.filter(FileAccessLog.timestamp <= end_date)
        
        logs = query.order_by(desc(FileAccessLog.timestamp)).limit(limit).all()
        return [log.to_dict() for log in logs]
    
    def get_access_statistics(self, file_id: str) -> dict:
        """获取文件访问统计"""
        from ..api.file_api import FileAccessLog
        
        # 总访问次数
        total_access = FileAccessLog.query.filter_by(file_id=file_id).count()
        
        # 按操作类型统计
        action_stats = db.session.query(
            FileAccessLog.action,
            func.count(FileAccessLog.id).label('count')
        ).filter_by(file_id=file_id).group_by(FileAccessLog.action).all()
        
        # 按用户统计
        user_stats = db.session.query(
            FileAccessLog.user_id,
            func.count(FileAccessLog.id).label('count')
        ).filter_by(file_id=file_id).group_by(FileAccessLog.user_id).all()
        
        # 最近访问
        recent_access = FileAccessLog.query.filter_by(file_id=file_id)\
                                          .order_by(desc(FileAccessLog.timestamp))\
                                          .first()
        
        return {
            'total_access': total_access,
            'by_action': {action: count for action, count in action_stats},
            'by_user': {user_id: count for user_id, count in user_stats},
            'last_access': recent_access.timestamp.isoformat() if recent_access else None
        }

class FileTagService:
    """文件标签和分类服务"""
    
    def __init__(self):
        # 预定义标签类别
        self.tag_categories = {
            'type': ['课件', '作业', '资料', '视频', '音频', '图片'],
            'subject': ['数学', '语文', '英语', '物理', '化学', '生物', '历史', '地理'],
            'difficulty': ['简单', '中等', '困难'],
            'status': ['草稿', '已发布', '已归档']
        }
    
    def add_tag_to_file(self, file_id: str, tag_name: str, category: str = None) -> bool:
        """为文件添加标签"""
        from ..api.file_api import FileTag
        
        # 检查标签是否已存在
        existing_tag = FileTag.query.filter_by(
            file_id=file_id, 
            tag_name=tag_name
        ).first()
        
        if existing_tag:
            return False
        
        tag = FileTag(
            id=uuid.uuid4().hex,
            file_id=file_id,
            tag_name=tag_name,
            category=category,
            created_at=datetime.utcnow()
        )
        
        db.session.add(tag)
        db.session.commit()
        return True
    
    def remove_tag_from_file(self, file_id: str, tag_name: str) -> bool:
        """从文件移除标签"""
        from ..api.file_api import FileTag
        
        tag = FileTag.query.filter_by(
            file_id=file_id, 
            tag_name=tag_name
        ).first()
        
        if tag:
            db.session.delete(tag)
            db.session.commit()
            return True
        
        return False
    
    def get_file_tags(self, file_id: str) -> List[dict]:
        """获取文件标签"""
        from ..api.file_api import FileTag
        
        tags = FileTag.query.filter_by(file_id=file_id).all()
        return [tag.to_dict() for tag in tags]
    
    def search_files_by_tags(self, tags: List[str], classroom_id: str = None) -> List[dict]:
        """根据标签搜索文件"""
        from ..api.file_api import FileRecord, FileTag
        
        query = db.session.query(FileRecord)\
                          .join(FileTag, FileRecord.id == FileTag.file_id)\
                          .filter(FileTag.tag_name.in_(tags))
        
        if classroom_id:
            query = query.filter(FileRecord.classroom_id == classroom_id)
        
        files = query.distinct().all()
        return [file.to_dict() for file in files]
    
    def auto_classify_file(self, file_record) -> List[str]:
        """自动分类文件"""
        suggested_tags = []
        
        # 根据文件类型自动分类
        file_type = file_record.file_type.lower()
        type_mapping = {
            'pdf': ['课件', '资料'],
            'doc': ['作业', '资料'],
            'docx': ['作业', '资料'],
            'ppt': ['课件'],
            'pptx': ['课件'],
            'mp4': ['视频'],
            'avi': ['视频'],
            'mp3': ['音频'],
            'wav': ['音频'],
            'jpg': ['图片'],
            'png': ['图片']
        }
        
        if file_type in type_mapping:
            suggested_tags.extend(type_mapping[file_type])
        
        # 根据文件名关键词分类
        filename = file_record.original_filename.lower()
        keyword_mapping = {
            '作业': ['homework', 'assignment', '作业'],
            '课件': ['slide', 'presentation', '课件', 'ppt'],
            '测试': ['test', 'exam', '测试', '考试'],
            '资料': ['material', 'resource', '资料']
        }
        
        for tag, keywords in keyword_mapping.items():
            if any(keyword in filename for keyword in keywords):
                if tag not in suggested_tags:
                    suggested_tags.append(tag)
        
        return suggested_tags
    
    def get_popular_tags(self, classroom_id: str = None, limit: int = 20) -> List[dict]:
        """获取热门标签"""
        from ..api.file_api import FileTag, FileRecord
        
        query = db.session.query(
            FileTag.tag_name,
            func.count(FileTag.id).label('usage_count')
        ).join(FileRecord, FileTag.file_id == FileRecord.id)
        
        if classroom_id:
            query = query.filter(FileRecord.classroom_id == classroom_id)
        
        tags = query.group_by(FileTag.tag_name)\
                   .order_by(desc('usage_count'))\
                   .limit(limit).all()
        
        return [{'tag_name': tag, 'count': count} for tag, count in tags]


class FileBatchService:
    """文件批量操作服务"""
    
    def __init__(self):
        self.batch_folder = 'instance/batch'
        os.makedirs(self.batch_folder, exist_ok=True)
    
    def create_batch_operation(self, operation_type: str, file_ids: List[str], 
                              user_id: str, parameters: dict = None) -> str:
        """创建批量操作任务"""
        from ..api.file_api import BatchOperation
        
        batch_id = uuid.uuid4().hex
        
        batch_op = BatchOperation(
            id=batch_id,
            operation_type=operation_type,
            file_ids=json.dumps(file_ids),
            user_id=user_id,
            parameters=json.dumps(parameters or {}),
            status='pending',
            created_at=datetime.utcnow()
        )
        
        db.session.add(batch_op)
        db.session.commit()
        
        return batch_id
    
    def execute_batch_operation(self, batch_id: str) -> dict:
        """执行批量操作"""
        from ..api.file_api import BatchOperation
        
        batch_op = BatchOperation.query.get(batch_id)
        if not batch_op:
            return {'success': False, 'message': '批量操作不存在'}
        
        batch_op.status = 'running'
        batch_op.started_at = datetime.utcnow()
        db.session.commit()
        
        try:
            file_ids = json.loads(batch_op.file_ids)
            parameters = json.loads(batch_op.parameters)
            
            result = self._execute_operation(
                batch_op.operation_type, 
                file_ids, 
                parameters
            )
            
            batch_op.status = 'completed'
            batch_op.completed_at = datetime.utcnow()
            batch_op.result = json.dumps(result)
            
        except Exception as e:
            batch_op.status = 'failed'
            batch_op.error_message = str(e)
            result = {'success': False, 'error': str(e)}
        
        db.session.commit()
        return result
    
    def _execute_operation(self, operation_type: str, file_ids: List[str], 
                          parameters: dict) -> dict:
        """执行具体的批量操作"""
        if operation_type == 'delete':
            return self._batch_delete(file_ids)
        elif operation_type == 'move':
            return self._batch_move(file_ids, parameters.get('target_folder'))
        elif operation_type == 'tag':
            return self._batch_tag(file_ids, parameters.get('tags', []))
        elif operation_type == 'permission':
            return self._batch_permission(file_ids, parameters.get('permissions', {}))
        elif operation_type == 'compress':
            return self._batch_compress(file_ids, parameters.get('archive_name'))
        else:
            raise ValueError(f"不支持的操作类型: {operation_type}")
    
    def _batch_delete(self, file_ids: List[str]) -> dict:
        """批量删除文件"""
        from ..api.file_api import FileRecord
        
        deleted_count = 0
        failed_files = []
        
        for file_id in file_ids:
            try:
                file_record = FileRecord.query.get(file_id)
                if file_record:
                    # 删除物理文件
                    file_path = os.path.join('instance/uploads', file_record.stored_filename)
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    
                    # 删除数据库记录
                    db.session.delete(file_record)
                    deleted_count += 1
                
            except Exception as e:
                failed_files.append({'file_id': file_id, 'error': str(e)})
        
        db.session.commit()
        
        return {
            'success': True,
            'deleted_count': deleted_count,
            'failed_files': failed_files
        }
    
    def _batch_move(self, file_ids: List[str], target_folder: str) -> dict:
        """批量移动文件"""
        from ..api.file_api import FileRecord
        
        moved_count = 0
        failed_files = []
        
        # 创建目标目录
        target_path = os.path.join('instance/uploads', target_folder)
        os.makedirs(target_path, exist_ok=True)
        
        for file_id in file_ids:
            try:
                file_record = FileRecord.query.get(file_id)
                if file_record:
                    old_path = os.path.join('instance/uploads', file_record.stored_filename)
                    new_path = os.path.join(target_path, file_record.stored_filename)
                    
                    if os.path.exists(old_path):
                        import shutil
                        shutil.move(old_path, new_path)
                        
                        # 更新数据库记录
                        file_record.stored_filename = os.path.join(target_folder, file_record.stored_filename)
                        moved_count += 1
                
            except Exception as e:
                failed_files.append({'file_id': file_id, 'error': str(e)})
        
        db.session.commit()
        
        return {
            'success': True,
            'moved_count': moved_count,
            'failed_files': failed_files
        }
    
    def _batch_tag(self, file_ids: List[str], tags: List[str]) -> dict:
        """批量添加标签"""
        tag_service = FileTagService()
        tagged_count = 0
        
        for file_id in file_ids:
            for tag in tags:
                if tag_service.add_tag_to_file(file_id, tag):
                    tagged_count += 1
        
        return {
            'success': True,
            'tagged_count': tagged_count
        }
    
    def _batch_permission(self, file_ids: List[str], permissions: dict) -> dict:
        """批量设置权限"""
        permission_service = FilePermissionService()
        updated_count = 0
        
        for file_id in file_ids:
            for user_id, perms in permissions.items():
                if permission_service.set_file_permissions(file_id, user_id, perms):
                    updated_count += 1
        
        return {
            'success': True,
            'updated_count': updated_count
        }
    
    def _batch_compress(self, file_ids: List[str], archive_name: str) -> dict:
        """批量压缩文件"""
        from ..api.file_api import FileRecord
        import zipfile
        
        archive_path = os.path.join(self.batch_folder, f"{archive_name}.zip")
        
        with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            compressed_count = 0
            
            for file_id in file_ids:
                file_record = FileRecord.query.get(file_id)
                if file_record:
                    file_path = os.path.join('instance/uploads', file_record.stored_filename)
                    if os.path.exists(file_path):
                        zipf.write(file_path, file_record.original_filename)
                        compressed_count += 1
        
        return {
            'success': True,
            'compressed_count': compressed_count,
            'archive_path': archive_path,
            'archive_size': os.path.getsize(archive_path)
        }
    
    def get_batch_status(self, batch_id: str) -> dict:
        """获取批量操作状态"""
        from ..api.file_api import BatchOperation
        
        batch_op = BatchOperation.query.get(batch_id)
        if not batch_op:
            return None
        
        return batch_op.to_dict()
    
    def get_user_batch_operations(self, user_id: str, limit: int = 20) -> List[dict]:
        """获取用户的批量操作历史"""
        from ..api.file_api import BatchOperation
        
        operations = BatchOperation.query.filter_by(user_id=user_id)\
                                        .order_by(desc(BatchOperation.created_at))\
                                        .limit(limit).all()
        
        return [op.to_dict() for op in operations]


class FileDistributionService:
    """文件分发服务"""
    
    def __init__(self):
        self.distribution_folder = 'instance/distributions'
        os.makedirs(self.distribution_folder, exist_ok=True)
    
    def create_distribution(self, file_ids: List[str], target_type: str, 
                           target_ids: List[str], distributor_id: str,
                           distribution_note: str = None) -> str:
        """创建文件分发任务"""
        from ..api.file_api import FileDistribution
        
        distribution_id = uuid.uuid4().hex
        
        distribution = FileDistribution(
            id=distribution_id,
            file_ids=json.dumps(file_ids),
            target_type=target_type,
            target_ids=json.dumps(target_ids),
            distributor_id=distributor_id,
            distribution_note=distribution_note,
            status='pending',
            created_at=datetime.utcnow()
        )
        
        db.session.add(distribution)
        db.session.commit()
        
        return distribution_id
    
    def execute_distribution(self, distribution_id: str) -> dict:
        """执行文件分发"""
        from ..api.file_api import FileDistribution, FileRecord
        
        distribution = FileDistribution.query.get(distribution_id)
        if not distribution:
            return {'success': False, 'message': '分发任务不存在'}
        
        distribution.status = 'distributing'
        distribution.started_at = datetime.utcnow()
        db.session.commit()
        
        try:
            file_ids = json.loads(distribution.file_ids)
            target_ids = json.loads(distribution.target_ids)
            
            # 获取文件信息
            files = FileRecord.query.filter(FileRecord.id.in_(file_ids)).all()
            
            # 执行分发逻辑（这里可以集成WebSocket通知）
            distribution_results = []
            
            for target_id in target_ids:
                for file_record in files:
                    result = self._distribute_to_target(file_record, target_id, distribution.target_type)
                    distribution_results.append({
                        'file_id': file_record.id,
                        'target_id': target_id,
                        'success': result['success'],
                        'message': result.get('message')
                    })
            
            distribution.status = 'completed'
            distribution.completed_at = datetime.utcnow()
            distribution.result = json.dumps(distribution_results)
            
            success_count = sum(1 for r in distribution_results if r['success'])
            total_count = len(distribution_results)
            
            result = {
                'success': True,
                'distribution_id': distribution_id,
                'success_count': success_count,
                'total_count': total_count,
                'results': distribution_results
            }
            
        except Exception as e:
            distribution.status = 'failed'
            distribution.error_message = str(e)
            result = {'success': False, 'error': str(e)}
        
        db.session.commit()
        return result
    
    def _distribute_to_target(self, file_record, target_id: str, target_type: str) -> dict:
        """分发文件到指定目标"""
        try:
            # 这里应该实现实际的分发逻辑
            # 例如：通过WebSocket通知客户端、复制文件到目标位置等
            
            if target_type == 'group':
                # 分发到小组
                return self._distribute_to_group(file_record, target_id)
            elif target_type == 'student':
                # 分发到学生
                return self._distribute_to_student(file_record, target_id)
            elif target_type == 'all':
                # 分发到所有人
                return self._distribute_to_all(file_record)
            else:
                return {'success': False, 'message': f'不支持的目标类型: {target_type}'}
                
        except Exception as e:
            return {'success': False, 'message': str(e)}
    
    def _distribute_to_group(self, file_record, group_id: str) -> dict:
        """分发到小组"""
        # 实现小组分发逻辑
        return {'success': True, 'message': f'文件已分发到小组 {group_id}'}
    
    def _distribute_to_student(self, file_record, student_id: str) -> dict:
        """分发到学生"""
        # 实现学生分发逻辑
        return {'success': True, 'message': f'文件已分发到学生 {student_id}'}
    
    def _distribute_to_all(self, file_record) -> dict:
        """分发到所有人"""
        # 实现全员分发逻辑
        return {'success': True, 'message': '文件已分发到所有人'}
    
    def get_distribution_status(self, distribution_id: str) -> dict:
        """获取分发状态"""
        from ..api.file_api import FileDistribution
        
        distribution = FileDistribution.query.get(distribution_id)
        if not distribution:
            return None
        
        return distribution.to_dict()
    
    def get_distribution_history(self, classroom_id: str = None, limit: int = 50) -> List[dict]:
        """获取分发历史"""
        from ..api.file_api import FileDistribution
        
        query = FileDistribution.query
        
        # 这里可以根据classroom_id过滤，需要关联文件表
        
        distributions = query.order_by(desc(FileDistribution.created_at))\
                            .limit(limit).all()
        
        return [dist.to_dict() for dist in distributions]