#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 视频流处理服务
"""

import os
import time
import json
import subprocess
import threading
import logging
import socket
import requests
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from shared.config import get_config

# 获取配置
config = get_config()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('video_service')

@dataclass
class StreamInfo:
    """流信息数据类"""
    stream_id: str
    source_id: str  # 设备ID或小组ID
    stream_type: str  # 'screen', 'camera', 'group'
    rtmp_url: str
    http_url: str
    status: str  # 'active', 'inactive', 'error'
    start_time: float
    viewers: List[str]
    ffmpeg_process: Optional[subprocess.Popen] = None
    
    def to_dict(self):
        """转换为字典，排除ffmpeg进程"""
        result = {
            'stream_id': self.stream_id,
            'source_id': self.source_id,
            'stream_type': self.stream_type,
            'rtmp_url': self.rtmp_url,
            'http_url': self.http_url,
            'status': self.status,
            'start_time': self.start_time,
            'viewers': self.viewers,
            'uptime': time.time() - self.start_time if self.status == 'active' else 0
        }
        return result

class VideoStreamService:
    """视频流处理服务"""
    
    def __init__(self):
        """初始化视频流服务"""
        self.streams: Dict[str, StreamInfo] = {}
        self.lock = threading.RLock()
        self.mediamtx_host = config.MEDIAMTX_HOST
        self.mediamtx_rtmp_port = config.MEDIAMTX_RTMP_PORT
        self.mediamtx_http_port = config.MEDIAMTX_HTTP_PORT
        self.video_resolution = config.VIDEO_RESOLUTION
        self.video_fps = config.VIDEO_FPS
        self.video_bitrate = config.VIDEO_BITRATE
        
        # 检查MediaMTX是否运行
        self._check_mediamtx()
    
    def _check_mediamtx(self) -> bool:
        """检查MediaMTX服务是否运行"""
        try:
            response = requests.get(f"http://{self.mediamtx_host}:{self.mediamtx_http_port}/metrics")
            if response.status_code == 200:
                logger.info("MediaMTX服务正在运行")
                return True
            else:
                logger.warning(f"MediaMTX服务返回状态码: {response.status_code}")
                return False
        except requests.RequestException as e:
            logger.error(f"MediaMTX服务未运行或无法访问: {str(e)}")
            return False
    
    def start_screen_capture(self, device_id: str, display: str = ":0.0") -> Optional[StreamInfo]:
        """
        开始屏幕捕获
        
        Args:
            device_id: 设备ID
            display: X11显示器标识
            
        Returns:
            StreamInfo: 流信息对象，如果失败则返回None
        """
        stream_id = f"screen_{device_id}_{int(time.time())}"
        rtmp_url = f"rtmp://{self.mediamtx_host}:{self.mediamtx_rtmp_port}/{stream_id}"
        http_url = f"http://{self.mediamtx_host}:{self.mediamtx_http_port}/{stream_id}"
        
        try:
            # 使用FFmpeg捕获屏幕并推流到MediaMTX
            command = [
                "ffmpeg",
                "-f", "x11grab",
                "-s", self.video_resolution,
                "-r", str(self.video_fps),
                "-i", display,
                "-c:v", "libx264",
                "-preset", "ultrafast",
                "-tune", "zerolatency",
                "-b:v", self.video_bitrate,
                "-f", "flv",
                rtmp_url
            ]
            
            logger.info(f"启动屏幕捕获: {' '.join(command)}")
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # 等待确认流已启动
            time.sleep(2)
            
            if process.poll() is not None:
                # 进程已退出
                stdout, stderr = process.communicate()
                logger.error(f"FFmpeg进程启动失败: {stderr}")
                return None
            
            # 创建流信息
            stream_info = StreamInfo(
                stream_id=stream_id,
                source_id=device_id,
                stream_type='screen',
                rtmp_url=rtmp_url,
                http_url=http_url,
                status='active',
                start_time=time.time(),
                viewers=[],
                ffmpeg_process=process
            )
            
            # 保存流信息
            with self.lock:
                self.streams[stream_id] = stream_info
            
            logger.info(f"屏幕捕获已启动: {stream_id}")
            return stream_info
            
        except Exception as e:
            logger.error(f"启动屏幕捕获时出错: {str(e)}")
            return None
    
    def start_camera_capture(self, device_id: str, camera_device: str = "/dev/video0") -> Optional[StreamInfo]:
        """
        开始摄像头捕获
        
        Args:
            device_id: 设备ID
            camera_device: 摄像头设备路径
            
        Returns:
            StreamInfo: 流信息对象，如果失败则返回None
        """
        stream_id = f"camera_{device_id}_{int(time.time())}"
        rtmp_url = f"rtmp://{self.mediamtx_host}:{self.mediamtx_rtmp_port}/{stream_id}"
        http_url = f"http://{self.mediamtx_host}:{self.mediamtx_http_port}/{stream_id}"
        
        try:
            # 使用FFmpeg捕获摄像头并推流到MediaMTX
            command = [
                "ffmpeg",
                "-f", "v4l2",
                "-i", camera_device,
                "-c:v", "libx264",
                "-preset", "ultrafast",
                "-tune", "zerolatency",
                "-b:v", self.video_bitrate,
                "-f", "flv",
                rtmp_url
            ]
            
            logger.info(f"启动摄像头捕获: {' '.join(command)}")
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # 等待确认流已启动
            time.sleep(2)
            
            if process.poll() is not None:
                # 进程已退出
                stdout, stderr = process.communicate()
                logger.error(f"FFmpeg进程启动失败: {stderr}")
                return None
            
            # 创建流信息
            stream_info = StreamInfo(
                stream_id=stream_id,
                source_id=device_id,
                stream_type='camera',
                rtmp_url=rtmp_url,
                http_url=http_url,
                status='active',
                start_time=time.time(),
                viewers=[],
                ffmpeg_process=process
            )
            
            # 保存流信息
            with self.lock:
                self.streams[stream_id] = stream_info
            
            logger.info(f"摄像头捕获已启动: {stream_id}")
            return stream_info
            
        except Exception as e:
            logger.error(f"启动摄像头捕获时出错: {str(e)}")
            return None
    
    def stop_stream(self, stream_id: str) -> bool:
        """
        停止流
        
        Args:
            stream_id: 流ID
            
        Returns:
            bool: 是否成功停止
        """
        with self.lock:
            if stream_id not in self.streams:
                logger.warning(f"找不到流: {stream_id}")
                return False
            
            stream_info = self.streams[stream_id]
            
            # 停止FFmpeg进程
            if stream_info.ffmpeg_process is not None:
                try:
                    stream_info.ffmpeg_process.terminate()
                    stream_info.ffmpeg_process.wait(timeout=5)
                    logger.info(f"已停止流: {stream_id}")
                except subprocess.TimeoutExpired:
                    stream_info.ffmpeg_process.kill()
                    logger.warning(f"强制终止流: {stream_id}")
                except Exception as e:
                    logger.error(f"停止流时出错: {str(e)}")
            
            # 更新状态
            stream_info.status = 'inactive'
            stream_info.ffmpeg_process = None
            
            return True
    
    def get_stream_info(self, stream_id: str) -> Optional[dict]:
        """
        获取流信息
        
        Args:
            stream_id: 流ID
            
        Returns:
            dict: 流信息字典，如果不存在则返回None
        """
        with self.lock:
            if stream_id in self.streams:
                return self.streams[stream_id].to_dict()
            return None
    
    def list_streams(self) -> List[dict]:
        """
        列出所有流
        
        Returns:
            List[dict]: 流信息字典列表
        """
        with self.lock:
            return [stream.to_dict() for stream in self.streams.values()]
    
    def add_viewer(self, stream_id: str, viewer_id: str) -> bool:
        """
        添加观看者
        
        Args:
            stream_id: 流ID
            viewer_id: 观看者ID
            
        Returns:
            bool: 是否成功添加
        """
        with self.lock:
            if stream_id not in self.streams:
                return False
            
            if viewer_id not in self.streams[stream_id].viewers:
                self.streams[stream_id].viewers.append(viewer_id)
                logger.info(f"添加观看者 {viewer_id} 到流 {stream_id}")
            
            return True
    
    def remove_viewer(self, stream_id: str, viewer_id: str) -> bool:
        """
        移除观看者
        
        Args:
            stream_id: 流ID
            viewer_id: 观看者ID
            
        Returns:
            bool: 是否成功移除
        """
        with self.lock:
            if stream_id not in self.streams:
                return False
            
            if viewer_id in self.streams[stream_id].viewers:
                self.streams[stream_id].viewers.remove(viewer_id)
                logger.info(f"从流 {stream_id} 移除观看者 {viewer_id}")
            
            return True
    
    def get_active_streams_by_source(self, source_id: str) -> List[dict]:
        """
        获取指定源的活动流
        
        Args:
            source_id: 源ID（设备ID或小组ID）
            
        Returns:
            List[dict]: 流信息字典列表
        """
        with self.lock:
            return [
                stream.to_dict() 
                for stream in self.streams.values() 
                if stream.source_id == source_id and stream.status == 'active'
            ]
    
    def cleanup(self):
        """清理所有流"""
        with self.lock:
            for stream_id in list(self.streams.keys()):
                self.stop_stream(stream_id)
            
            self.streams.clear()
            logger.info("已清理所有流")

# 单例模式
_video_service_instance = None

def get_video_service() -> VideoStreamService:
    """获取视频服务单例"""
    global _video_service_instance
    if _video_service_instance is None:
        _video_service_instance = VideoStreamService()
    return _video_service_instance