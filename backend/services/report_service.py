# -*- coding: utf-8 -*-
"""
课堂报告和数据分析服务
"""

import json
import csv
import io
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy import func, and_, or_

from models.base import db
from models.classroom import Classroom, ClassroomActivity
from models.student import Student, StudentActivity
from models.question import Question, Answer
from models.group import Group
from models.device import Device
from models.report import ClassroomReport, LearningAnalytics, DataExport, ReportTemplate


class ReportService:
    """报告服务"""
    
    @staticmethod
    def generate_comprehensive_report(classroom_id: int, teacher_id: str, 
                                    start_time: Optional[datetime] = None,
                                    end_time: Optional[datetime] = None) -> ClassroomReport:
        """生成综合课堂报告"""
        classroom = Classroom.get_by_id(classroom_id)
        if not classroom:
            raise ValueError("课堂不存在")
        
        # 设置时间范围
        if not start_time:
            start_time = classroom.start_time or classroom.created_at
        if not end_time:
            end_time = classroom.end_time or datetime.utcnow()
        
        # 创建报告
        title = f"{classroom.name} - 综合课堂报告"
        report = ClassroomReport.create_report(
            classroom_id=classroom_id,
            report_type='comprehensive',
            title=title,
            generated_by=teacher_id,
            start_time=start_time,
            end_time=end_time
        )
        
        try:
            # 收集基础数据
            basic_data = ReportService._collect_basic_data(classroom_id, start_time, end_time)
            report.update_data('basic', basic_data)
            
            # 收集考勤数据
            attendance_data = ReportService._collect_attendance_data(classroom_id, start_time, end_time)
            report.update_data('attendance', attendance_data)
            
            # 收集互动数据
            interaction_data = ReportService._collect_interaction_data(classroom_id, start_time, end_time)
            report.update_data('interaction', interaction_data)
            
            # 收集答题数据
            quiz_data = ReportService._collect_quiz_data(classroom_id, start_time, end_time)
            report.update_data('quiz', quiz_data)
            
            # 收集设备数据
            device_data = ReportService._collect_device_data(classroom_id, start_time, end_time)
            report.update_data('device', device_data)
            
            # 生成图表
            ReportService._generate_charts(report)
            
            # 生成摘要
            summary = ReportService._generate_summary(report)
            report.summary = summary
            
            report.mark_completed()
            
        except Exception as e:
            report.mark_failed(str(e))
            raise
        
        return report
    
    @staticmethod
    def _collect_basic_data(classroom_id: int, start_time: datetime, end_time: datetime) -> Dict:
        """收集基础数据"""
        classroom = Classroom.get_by_id(classroom_id)
        
        # 课堂基本信息
        duration_minutes = 0
        if classroom.start_time and classroom.end_time:
            duration_minutes = int((classroom.end_time - classroom.start_time).total_seconds() / 60)
        elif classroom.start_time:
            duration_minutes = int((datetime.utcnow() - classroom.start_time).total_seconds() / 60)
        
        # 学生统计
        total_students = Student.query.filter_by(classroom_id=classroom_id).count()
        
        return {
            'classroom_name': classroom.name,
            'teacher_name': classroom.teacher_name,
            'start_time': start_time.isoformat() if start_time else None,
            'end_time': end_time.isoformat() if end_time else None,
            'duration_minutes': duration_minutes,
            'total_students': total_students,
            'total_groups': Group.query.filter_by(classroom_id=classroom_id).count(),
            'status': classroom.status
        }
    
    @staticmethod
    def _collect_attendance_data(classroom_id: int, start_time: datetime, end_time: datetime) -> Dict:
        """收集考勤数据"""
        # 考勤统计
        attendance_stats = db.session.query(
            Student.attendance_status,
            func.count(Student.id).label('count')
        ).filter(Student.classroom_id == classroom_id)\
         .group_by(Student.attendance_status).all()
        
        attendance_summary = {status: count for status, count in attendance_stats}
        
        # 签到方式统计
        checkin_method_stats = db.session.query(
            Student.check_in_method,
            func.count(Student.id).label('count')
        ).filter(
            Student.classroom_id == classroom_id,
            Student.check_in_method.isnot(None)
        ).group_by(Student.check_in_method).all()
        
        checkin_methods = {method: count for method, count in checkin_method_stats}
        
        # 签到时间分布
        checkin_times = db.session.query(Student.check_in_time)\
                                 .filter(
                                     Student.classroom_id == classroom_id,
                                     Student.check_in_time.isnot(None)
                                 ).all()
        
        # 按小时统计签到分布
        hourly_checkins = {}
        for checkin_time, in checkin_times:
            hour = checkin_time.hour
            hourly_checkins[hour] = hourly_checkins.get(hour, 0) + 1
        
        return {
            'summary': attendance_summary,
            'checkin_methods': checkin_methods,
            'hourly_distribution': hourly_checkins,
            'total_present': attendance_summary.get('present', 0),
            'total_absent': attendance_summary.get('absent', 0),
            'total_late': attendance_summary.get('late', 0),
            'attendance_rate': (attendance_summary.get('present', 0) / 
                              sum(attendance_summary.values()) * 100) if attendance_summary else 0
        }
    
    @staticmethod
    def _collect_interaction_data(classroom_id: int, start_time: datetime, end_time: datetime) -> Dict:
        """收集互动数据"""
        # 学生互动统计
        interaction_stats = db.session.query(
            Student.student_id,
            Student.name,
            Student.interaction_count,
            Student.participation_score
        ).filter(Student.classroom_id == classroom_id)\
         .order_by(Student.interaction_count.desc()).all()
        
        # 课堂活动统计
        activity_stats = db.session.query(
            ClassroomActivity.activity_type,
            func.count(ClassroomActivity.id).label('count')
        ).filter(
            ClassroomActivity.classroom_id == classroom_id,
            ClassroomActivity.created_at.between(start_time, end_time)
        ).group_by(ClassroomActivity.activity_type).all()
        
        activity_summary = {activity_type: count for activity_type, count in activity_stats}
        
        # 参与度排行
        top_participants = [
            {
                'student_id': student_id,
                'name': name,
                'interaction_count': interaction_count,
                'participation_score': participation_score
            }
            for student_id, name, interaction_count, participation_score in interaction_stats[:10]
        ]
        
        # 互动时间分布
        hourly_activities = db.session.query(
            func.extract('hour', ClassroomActivity.created_at).label('hour'),
            func.count(ClassroomActivity.id).label('count')
        ).filter(
            ClassroomActivity.classroom_id == classroom_id,
            ClassroomActivity.created_at.between(start_time, end_time)
        ).group_by(func.extract('hour', ClassroomActivity.created_at)).all()
        
        hourly_distribution = {int(hour): count for hour, count in hourly_activities}
        
        return {
            'activity_summary': activity_summary,
            'top_participants': top_participants,
            'hourly_distribution': hourly_distribution,
            'total_interactions': sum(activity_summary.values()),
            'average_participation': sum(s[3] for s in interaction_stats) / len(interaction_stats) if interaction_stats else 0
        }
    
    @staticmethod
    def _collect_quiz_data(classroom_id: int, start_time: datetime, end_time: datetime) -> Dict:
        """收集答题数据"""
        # 题目统计
        questions = Question.query.filter_by(classroom_id=classroom_id).all()
        
        quiz_summary = {
            'total_questions': len(questions),
            'published_questions': len([q for q in questions if q.status == 'published']),
            'closed_questions': len([q for q in questions if q.status == 'closed'])
        }
        
        # 答题统计
        question_stats = []
        for question in questions:
            answers = Answer.query.filter_by(question_id=question.id).all()
            correct_answers = [a for a in answers if a.is_correct]
            
            # 响应时间统计
            response_times = [a.response_time for a in answers if a.response_time]
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
            
            question_stats.append({
                'question_id': question.question_id,
                'title': question.title,
                'type': question.question_type,
                'total_answers': len(answers),
                'correct_answers': len(correct_answers),
                'accuracy_rate': (len(correct_answers) / len(answers) * 100) if answers else 0,
                'avg_response_time': avg_response_time,
                'answer_distribution': question.get_answer_distribution()
            })
        
        # 学生答题表现
        student_quiz_stats = db.session.query(
            Student.student_id,
            Student.name,
            Student.answer_count,
            Student.correct_answer_count
        ).filter(Student.classroom_id == classroom_id)\
         .order_by(Student.correct_answer_count.desc()).all()
        
        top_performers = [
            {
                'student_id': student_id,
                'name': name,
                'total_answers': answer_count,
                'correct_answers': correct_answer_count,
                'accuracy_rate': (correct_answer_count / answer_count * 100) if answer_count > 0 else 0
            }
            for student_id, name, answer_count, correct_answer_count in student_quiz_stats[:10]
        ]
        
        return {
            'summary': quiz_summary,
            'question_stats': question_stats,
            'top_performers': top_performers,
            'total_answers': sum(len(Answer.query.filter_by(question_id=q.id).all()) for q in questions),
            'overall_accuracy': sum(q['accuracy_rate'] for q in question_stats) / len(question_stats) if question_stats else 0
        }
    
    @staticmethod
    def _collect_device_data(classroom_id: int, start_time: datetime, end_time: datetime) -> Dict:
        """收集设备数据"""
        # 设备统计
        devices = Device.query.filter_by(classroom_id=classroom_id).all()
        
        device_stats = {
            'total_devices': len(devices),
            'online_devices': len([d for d in devices if d.status == 'online']),
            'offline_devices': len([d for d in devices if d.status == 'offline'])
        }
        
        # 设备类型分布
        device_types = {}
        for device in devices:
            device_types[device.device_type] = device_types.get(device.device_type, 0) + 1
        
        # 设备健康状况
        healthy_devices = len([d for d in devices if d.is_healthy()])
        device_health_rate = (healthy_devices / len(devices) * 100) if devices else 0
        
        return {
            'summary': device_stats,
            'type_distribution': device_types,
            'health_rate': device_health_rate,
            'healthy_devices': healthy_devices
        }
    
    @staticmethod
    def _generate_charts(report: ClassroomReport):
        """生成图表"""
        data = report.data
        
        # 考勤饼图
        if 'attendance' in data:
            attendance_data = data['attendance']['summary']
            report.add_chart('pie', attendance_data, '考勤分布')
        
        # 互动柱状图
        if 'interaction' in data:
            hourly_data = data['interaction']['hourly_distribution']
            report.add_chart('bar', hourly_data, '互动时间分布')
        
        # 答题准确率图
        if 'quiz' in data:
            question_stats = data['quiz']['question_stats']
            accuracy_data = {q['title'][:20]: q['accuracy_rate'] for q in question_stats}
            report.add_chart('bar', accuracy_data, '题目准确率')
        
        # 设备状态图
        if 'device' in data:
            device_data = data['device']['summary']
            report.add_chart('pie', device_data, '设备状态分布')
    
    @staticmethod
    def _generate_summary(report: ClassroomReport) -> str:
        """生成报告摘要"""
        data = report.data
        summary_parts = []
        
        # 基础信息摘要
        if 'basic' in data:
            basic = data['basic']
            summary_parts.append(
                f"课堂《{basic['classroom_name']}》由{basic['teacher_name']}老师主讲，"
                f"持续{basic['duration_minutes']}分钟，共有{basic['total_students']}名学生参与。"
            )
        
        # 考勤摘要
        if 'attendance' in data:
            attendance = data['attendance']
            summary_parts.append(
                f"出勤率为{attendance['attendance_rate']:.1f}%，"
                f"其中{attendance['total_present']}人出席，"
                f"{attendance['total_late']}人迟到，"
                f"{attendance['total_absent']}人缺席。"
            )
        
        # 互动摘要
        if 'interaction' in data:
            interaction = data['interaction']
            summary_parts.append(
                f"课堂共产生{interaction['total_interactions']}次互动，"
                f"平均参与度为{interaction['average_participation']:.1f}分。"
            )
        
        # 答题摘要
        if 'quiz' in data:
            quiz = data['quiz']
            summary_parts.append(
                f"共发布{quiz['summary']['total_questions']}道题目，"
                f"收到{quiz['total_answers']}个答案，"
                f"整体正确率为{quiz['overall_accuracy']:.1f}%。"
            )
        
        return " ".join(summary_parts)


class AnalyticsService:
    """学习分析服务"""
    
    @staticmethod
    def analyze_student_participation(student_id: int, classroom_id: int) -> LearningAnalytics:
        """分析学生参与度"""
        student = Student.get_by_id(student_id)
        if not student:
            raise ValueError("学生不存在")
        
        classroom = Classroom.get_by_id(classroom_id)
        if not classroom:
            raise ValueError("课堂不存在")
        
        # 创建分析记录
        start_time = classroom.start_time or classroom.created_at
        end_time = classroom.end_time or datetime.utcnow()
        
        analytics = LearningAnalytics.create_analysis(
            student_id=student_id,
            classroom_id=classroom_id,
            analysis_type='participation',
            start_time=start_time,
            end_time=end_time
        )
        
        try:
            # 收集参与度指标
            metrics = {
                'interaction_count': student.interaction_count,
                'answer_count': student.answer_count,
                'correct_answer_count': student.correct_answer_count,
                'participation_score': student.participation_score,
                'attendance_status': student.attendance_status,
                'online_time_minutes': AnalyticsService._calculate_online_time(student)
            }
            
            analytics.metrics = metrics
            
            # 生成洞察
            AnalyticsService._generate_participation_insights(analytics, metrics)
            
            # 生成推荐
            AnalyticsService._generate_participation_recommendations(analytics, metrics)
            
            analytics.status = 'completed'
            analytics.confidence_score = 0.8
            db.session.commit()
            
        except Exception as e:
            analytics.status = 'failed'
            db.session.commit()
            raise
        
        return analytics
    
    @staticmethod
    def _calculate_online_time(student: Student) -> int:
        """计算学生在线时长（分钟）"""
        # 简化实现，基于最后活动时间估算
        if student.last_active and student.check_in_time:
            duration = student.last_active - student.check_in_time
            return int(duration.total_seconds() / 60)
        return 0
    
    @staticmethod
    def _generate_participation_insights(analytics: LearningAnalytics, metrics: Dict):
        """生成参与度洞察"""
        # 参与度水平判断
        participation_score = metrics.get('participation_score', 0)
        if participation_score >= 80:
            analytics.add_insight('high_participation', '学生参与度很高，积极参与课堂活动', 0.9)
        elif participation_score >= 60:
            analytics.add_insight('medium_participation', '学生参与度中等，有一定的课堂参与', 0.8)
        else:
            analytics.add_insight('low_participation', '学生参与度较低，需要更多鼓励', 0.8)
        
        # 答题表现分析
        answer_count = metrics.get('answer_count', 0)
        correct_count = metrics.get('correct_answer_count', 0)
        if answer_count > 0:
            accuracy = correct_count / answer_count
            if accuracy >= 0.8:
                analytics.add_insight('high_accuracy', '答题准确率很高，理解能力强', 0.9)
            elif accuracy >= 0.6:
                analytics.add_insight('medium_accuracy', '答题准确率中等，基本掌握知识点', 0.8)
            else:
                analytics.add_insight('low_accuracy', '答题准确率较低，需要加强理解', 0.8)
    
    @staticmethod
    def _generate_participation_recommendations(analytics: LearningAnalytics, metrics: Dict):
        """生成参与度推荐"""
        participation_score = metrics.get('participation_score', 0)
        
        if participation_score < 60:
            analytics.add_recommendation(
                'increase_interaction',
                '建议教师多提问该学生，增加互动机会',
                'high'
            )
        
        answer_count = metrics.get('answer_count', 0)
        if answer_count < 3:
            analytics.add_recommendation(
                'encourage_answering',
                '鼓励学生更多参与答题活动',
                'medium'
            )
        
        accuracy = (metrics.get('correct_answer_count', 0) / 
                   max(metrics.get('answer_count', 1), 1))
        if accuracy < 0.6:
            analytics.add_recommendation(
                'provide_support',
                '建议提供额外的学习支持和辅导',
                'high'
            )


class ExportService:
    """数据导出服务"""
    
    @staticmethod
    def export_attendance_data(classroom_id: int, export_format: str, user_id: str) -> DataExport:
        """导出考勤数据"""
        filename = f"attendance_{classroom_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{export_format}"
        
        export_task = DataExport.create_export(
            classroom_id=classroom_id,
            export_type=export_format,
            data_type='attendance',
            filename=filename,
            requested_by=user_id
        )
        
        try:
            # 获取考勤数据
            students = Student.query.filter_by(classroom_id=classroom_id).all()
            
            if export_format == 'csv':
                file_path = ExportService._export_attendance_csv(students, filename)
            elif export_format == 'excel':
                file_path = ExportService._export_attendance_excel(students, filename)
            else:
                raise ValueError(f"不支持的导出格式: {export_format}")
            
            export_task.file_path = file_path
            export_task.file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
            export_task.update_progress(100)
            
        except Exception as e:
            export_task.mark_failed(str(e))
            raise
        
        return export_task
    
    @staticmethod
    def _export_attendance_csv(students: List[Student], filename: str) -> str:
        """导出考勤CSV文件"""
        # 确保导出目录存在
        export_dir = os.path.join(os.path.dirname(__file__), '../../exports')
        os.makedirs(export_dir, exist_ok=True)
        
        file_path = os.path.join(export_dir, filename)
        
        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # 写入表头
            writer.writerow([
                '学生ID', '姓名', '学号', '出勤状态', '签到时间', 
                '签到方式', '互动次数', '参与度评分'
            ])
            
            # 写入数据
            for student in students:
                writer.writerow([
                    student.student_id,
                    student.name,
                    student.student_number or '',
                    student.attendance_status,
                    student.check_in_time.strftime('%Y-%m-%d %H:%M:%S') if student.check_in_time else '',
                    student.check_in_method or '',
                    student.interaction_count,
                    student.participation_score
                ])
        
        return file_path
    
    @staticmethod
    def _export_attendance_excel(students: List[Student], filename: str) -> str:
        """导出考勤Excel文件"""
        try:
            import openpyxl
            from openpyxl.styles import Font, Alignment
        except ImportError:
            raise ImportError("需要安装openpyxl库来支持Excel导出")
        
        # 确保导出目录存在
        export_dir = os.path.join(os.path.dirname(__file__), '../../exports')
        os.makedirs(export_dir, exist_ok=True)
        
        file_path = os.path.join(export_dir, filename)
        
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "考勤数据"
        
        # 设置表头
        headers = ['学生ID', '姓名', '学号', '出勤状态', '签到时间', '签到方式', '互动次数', '参与度评分']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
        
        # 写入数据
        for row, student in enumerate(students, 2):
            ws.cell(row=row, column=1, value=student.student_id)
            ws.cell(row=row, column=2, value=student.name)
            ws.cell(row=row, column=3, value=student.student_number or '')
            ws.cell(row=row, column=4, value=student.attendance_status)
            ws.cell(row=row, column=5, value=student.check_in_time.strftime('%Y-%m-%d %H:%M:%S') if student.check_in_time else '')
            ws.cell(row=row, column=6, value=student.check_in_method or '')
            ws.cell(row=row, column=7, value=student.interaction_count)
            ws.cell(row=row, column=8, value=student.participation_score)
        
        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        wb.save(file_path)
        return file_path