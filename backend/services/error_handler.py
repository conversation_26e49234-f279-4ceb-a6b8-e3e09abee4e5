#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 统一错误处理机制
"""

import logging
import traceback
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from dataclasses import dataclass, field
from collections import defaultdict
import json

from .logging_service import get_logging_service

class ErrorType(Enum):
    """错误类型枚举"""
    NETWORK_ERROR = "network_error"
    DEVICE_ERROR = "device_error"
    VIDEO_ERROR = "video_error"
    DATABASE_ERROR = "database_error"
    SYSTEM_ERROR = "system_error"
    BUSINESS_ERROR = "business_error"
    AUTHENTICATION_ERROR = "auth_error"
    PERMISSION_ERROR = "permission_error"

class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class ErrorContext:
    """错误上下文信息"""
    error_id: str
    error_type: ErrorType
    severity: ErrorSeverity
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    user_id: Optional[str] = None
    device_id: Optional[str] = None
    classroom_id: Optional[str] = None
    stack_trace: Optional[str] = None
    recovery_attempts: int = 0
    max_recovery_attempts: int = 3
    recovery_actions: List[str] = field(default_factory=list)
    resolved: bool = False
    resolution_time: Optional[datetime] = None

class ErrorHandler:
    """统一错误处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger('smart_classroom.error_handler')
        self.logging_service = get_logging_service()
        
        # 错误统计
        self.error_stats = defaultdict(int)
        self.error_history: List[ErrorContext] = []
        self.active_errors: Dict[str, ErrorContext] = {}
        
        # 错误处理策略
        self.error_handlers: Dict[ErrorType, List[Callable]] = defaultdict(list)
        self.recovery_strategies: Dict[ErrorType, List[Callable]] = defaultdict(list)
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 注册默认错误处理器
        self._register_default_handlers()
        
        self.logger.info("错误处理器已初始化")
    
    def _register_default_handlers(self):
        """注册默认错误处理器"""
        # 网络错误处理器
        self.register_error_handler(ErrorType.NETWORK_ERROR, self._handle_network_error)
        self.register_recovery_strategy(ErrorType.NETWORK_ERROR, self._recover_network_connection)
        
        # 设备错误处理器
        self.register_error_handler(ErrorType.DEVICE_ERROR, self._handle_device_error)
        self.register_recovery_strategy(ErrorType.DEVICE_ERROR, self._recover_device_connection)
        
        # 视频错误处理器
        self.register_error_handler(ErrorType.VIDEO_ERROR, self._handle_video_error)
        self.register_recovery_strategy(ErrorType.VIDEO_ERROR, self._recover_video_stream)
        
        # 数据库错误处理器
        self.register_error_handler(ErrorType.DATABASE_ERROR, self._handle_database_error)
        self.register_recovery_strategy(ErrorType.DATABASE_ERROR, self._recover_database_connection)
        
        # 系统错误处理器
        self.register_error_handler(ErrorType.SYSTEM_ERROR, self._handle_system_error)
    
    def register_error_handler(self, error_type: ErrorType, handler: Callable):
        """注册错误处理器"""
        with self.lock:
            self.error_handlers[error_type].append(handler)
            self.logger.debug(f"已注册错误处理器: {error_type.value}")
    
    def register_recovery_strategy(self, error_type: ErrorType, strategy: Callable):
        """注册恢复策略"""
        with self.lock:
            self.recovery_strategies[error_type].append(strategy)
            self.logger.debug(f"已注册恢复策略: {error_type.value}")
    
    def handle_error(self, error: Exception, error_type: ErrorType, 
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                    context: Dict[str, Any] = None, 
                    user_id: str = None, device_id: str = None,
                    classroom_id: str = None) -> ErrorContext:
        """处理错误"""
        error_id = f"{error_type.value}_{int(time.time() * 1000)}"
        
        # 创建错误上下文
        error_context = ErrorContext(
            error_id=error_id,
            error_type=error_type,
            severity=severity,
            message=str(error),
            details=context or {},
            user_id=user_id,
            device_id=device_id,
            classroom_id=classroom_id,
            stack_trace=traceback.format_exc()
        )
        
        with self.lock:
            # 更新统计
            self.error_stats[error_type.value] += 1
            self.error_history.append(error_context)
            self.active_errors[error_id] = error_context
            
            # 保持历史记录在合理范围内
            if len(self.error_history) > 1000:
                self.error_history = self.error_history[-500:]
        
        # 记录错误日志
        self._log_error(error_context)
        
        # 执行错误处理器
        self._execute_error_handlers(error_context)
        
        # 尝试自动恢复
        if error_context.recovery_attempts < error_context.max_recovery_attempts:
            self._attempt_recovery(error_context)
        
        return error_context
    
    def _log_error(self, error_context: ErrorContext):
        """记录错误日志"""
        log_data = {
            'error_id': error_context.error_id,
            'error_type': error_context.error_type.value,
            'severity': error_context.severity.value,
            'message': error_context.message,
            'user_id': error_context.user_id,
            'device_id': error_context.device_id,
            'classroom_id': error_context.classroom_id,
            'details': error_context.details,
            'timestamp': error_context.timestamp.isoformat()
        }
        
        # 根据严重程度选择日志级别
        if error_context.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(f"严重错误: {error_context.message}", extra=log_data)
        elif error_context.severity == ErrorSeverity.HIGH:
            self.logger.error(f"高级错误: {error_context.message}", extra=log_data)
        elif error_context.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(f"中级错误: {error_context.message}", extra=log_data)
        else:
            self.logger.info(f"低级错误: {error_context.message}", extra=log_data)
        
        # 记录到审计日志
        self.logging_service.log_audit(
            action="error_occurred",
            user_id=error_context.user_id,
            resource=error_context.device_id or error_context.classroom_id,
            result="error",
            details=log_data
        )
    
    def _execute_error_handlers(self, error_context: ErrorContext):
        """执行错误处理器"""
        handlers = self.error_handlers.get(error_context.error_type, [])
        
        for handler in handlers:
            try:
                handler(error_context)
            except Exception as e:
                self.logger.error(f"错误处理器执行失败: {e}")
    
    def _attempt_recovery(self, error_context: ErrorContext):
        """尝试自动恢复"""
        strategies = self.recovery_strategies.get(error_context.error_type, [])
        
        if not strategies:
            return
        
        error_context.recovery_attempts += 1
        
        for strategy in strategies:
            try:
                success = strategy(error_context)
                if success:
                    self._mark_error_resolved(error_context, f"自动恢复成功 - 策略: {strategy.__name__}")
                    return
            except Exception as e:
                self.logger.error(f"恢复策略执行失败: {e}")
                error_context.recovery_actions.append(f"恢复失败: {strategy.__name__} - {str(e)}")
    
    def _mark_error_resolved(self, error_context: ErrorContext, resolution_note: str):
        """标记错误已解决"""
        with self.lock:
            error_context.resolved = True
            error_context.resolution_time = datetime.now()
            error_context.recovery_actions.append(resolution_note)
            
            # 从活动错误中移除
            if error_context.error_id in self.active_errors:
                del self.active_errors[error_context.error_id]
        
        self.logger.info(f"错误已解决: {error_context.error_id} - {resolution_note}")
        
        # 记录解决日志
        self.logging_service.log_audit(
            action="error_resolved",
            user_id=error_context.user_id,
            resource=error_context.device_id or error_context.classroom_id,
            result="success",
            details={
                'error_id': error_context.error_id,
                'resolution_note': resolution_note,
                'recovery_attempts': error_context.recovery_attempts
            }
        )
    
    # 默认错误处理器实现
    def _handle_network_error(self, error_context: ErrorContext):
        """处理网络错误"""
        self.logger.warning(f"网络错误: {error_context.message}")
        
        # 通知相关设备
        if error_context.device_id:
            self._notify_device_error(error_context.device_id, "网络连接异常")
    
    def _handle_device_error(self, error_context: ErrorContext):
        """处理设备错误"""
        self.logger.warning(f"设备错误: {error_context.message}")
        
        # 更新设备状态
        if error_context.device_id:
            self._update_device_status(error_context.device_id, "error")
    
    def _handle_video_error(self, error_context: ErrorContext):
        """处理视频错误"""
        self.logger.warning(f"视频错误: {error_context.message}")
        
        # 停止有问题的视频流
        if 'stream_id' in error_context.details:
            self._stop_problematic_stream(error_context.details['stream_id'])
    
    def _handle_database_error(self, error_context: ErrorContext):
        """处理数据库错误"""
        self.logger.error(f"数据库错误: {error_context.message}")
        
        # 数据库错误通常比较严重，需要特殊处理
        if error_context.severity == ErrorSeverity.CRITICAL:
            self._trigger_database_backup()
    
    def _handle_system_error(self, error_context: ErrorContext):
        """处理系统错误"""
        self.logger.error(f"系统错误: {error_context.message}")
        
        # 系统错误可能需要重启相关服务
        if error_context.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            self._schedule_service_restart()
    
    # 默认恢复策略实现
    def _recover_network_connection(self, error_context: ErrorContext) -> bool:
        """恢复网络连接"""
        try:
            # 这里实现网络连接恢复逻辑
            # 例如：重新建立WebSocket连接、重试HTTP请求等
            self.logger.info(f"尝试恢复网络连接: {error_context.device_id}")
            
            # 模拟恢复过程
            time.sleep(1)
            
            # 实际实现中应该调用相应的网络恢复方法
            return True
            
        except Exception as e:
            self.logger.error(f"网络连接恢复失败: {e}")
            return False
    
    def _recover_device_connection(self, error_context: ErrorContext) -> bool:
        """恢复设备连接"""
        try:
            if not error_context.device_id:
                return False
            
            self.logger.info(f"尝试恢复设备连接: {error_context.device_id}")
            
            # 实际实现中应该调用设备重连方法
            # 例如：重新发送UDP广播、重新建立TCP连接等
            
            return True
            
        except Exception as e:
            self.logger.error(f"设备连接恢复失败: {e}")
            return False
    
    def _recover_video_stream(self, error_context: ErrorContext) -> bool:
        """恢复视频流"""
        try:
            stream_id = error_context.details.get('stream_id')
            if not stream_id:
                return False
            
            self.logger.info(f"尝试恢复视频流: {stream_id}")
            
            # 实际实现中应该调用视频流恢复方法
            # 例如：重启FFmpeg进程、重新发布流等
            
            return True
            
        except Exception as e:
            self.logger.error(f"视频流恢复失败: {e}")
            return False
    
    def _recover_database_connection(self, error_context: ErrorContext) -> bool:
        """恢复数据库连接"""
        try:
            self.logger.info("尝试恢复数据库连接")
            
            # 实际实现中应该调用数据库重连方法
            # 例如：重新创建连接池、重试事务等
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据库连接恢复失败: {e}")
            return False
    
    # 辅助方法
    def _notify_device_error(self, device_id: str, message: str):
        """通知设备错误"""
        # 实际实现中应该通过WebSocket或其他方式通知设备
        self.logger.debug(f"通知设备错误: {device_id} - {message}")
    
    def _update_device_status(self, device_id: str, status: str):
        """更新设备状态"""
        # 实际实现中应该更新数据库中的设备状态
        self.logger.debug(f"更新设备状态: {device_id} - {status}")
    
    def _stop_problematic_stream(self, stream_id: str):
        """停止有问题的视频流"""
        # 实际实现中应该调用视频服务停止流
        self.logger.debug(f"停止有问题的视频流: {stream_id}")
    
    def _trigger_database_backup(self):
        """触发数据库备份"""
        # 实际实现中应该启动数据库备份流程
        self.logger.info("触发数据库备份")
    
    def _schedule_service_restart(self):
        """安排服务重启"""
        # 实际实现中应该安排相关服务重启
        self.logger.warning("安排服务重启")
    
    # 查询和统计方法
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计"""
        with self.lock:
            return {
                'total_errors': len(self.error_history),
                'active_errors': len(self.active_errors),
                'error_types': dict(self.error_stats),
                'recent_errors': [
                    {
                        'error_id': error.error_id,
                        'type': error.error_type.value,
                        'severity': error.severity.value,
                        'message': error.message,
                        'timestamp': error.timestamp.isoformat(),
                        'resolved': error.resolved
                    }
                    for error in self.error_history[-10:]
                ]
            }
    
    def get_active_errors(self) -> List[Dict[str, Any]]:
        """获取活动错误"""
        with self.lock:
            return [
                {
                    'error_id': error.error_id,
                    'type': error.error_type.value,
                    'severity': error.severity.value,
                    'message': error.message,
                    'timestamp': error.timestamp.isoformat(),
                    'recovery_attempts': error.recovery_attempts,
                    'max_recovery_attempts': error.max_recovery_attempts,
                    'recovery_actions': error.recovery_actions
                }
                for error in self.active_errors.values()
            ]
    
    def resolve_error(self, error_id: str, resolution_note: str = "手动解决") -> bool:
        """手动解决错误"""
        with self.lock:
            if error_id in self.active_errors:
                error_context = self.active_errors[error_id]
                self._mark_error_resolved(error_context, resolution_note)
                return True
            return False
    
    def clear_resolved_errors(self):
        """清理已解决的错误"""
        with self.lock:
            # 保留最近100个已解决的错误
            resolved_errors = [e for e in self.error_history if e.resolved]
            if len(resolved_errors) > 100:
                # 移除最旧的已解决错误
                cutoff_time = datetime.now() - timedelta(hours=24)
                self.error_history = [
                    e for e in self.error_history 
                    if not e.resolved or e.resolution_time > cutoff_time
                ]
        
        self.logger.info("已清理旧的已解决错误")

# 全局错误处理器实例
_error_handler = None

def get_error_handler() -> ErrorHandler:
    """获取错误处理器实例"""
    global _error_handler
    if _error_handler is None:
        _error_handler = ErrorHandler()
    return _error_handler

# 便捷函数
def handle_error(error: Exception, error_type: ErrorType, 
                severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                **kwargs) -> ErrorContext:
    """处理错误的便捷函数"""
    return get_error_handler().handle_error(error, error_type, severity, **kwargs)