#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 配置管理器
"""

import os
import yaml
import json
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path
from datetime import datetime
import threading

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "smart_classroom/config"):
        self.config_dir = Path(config_dir)
        self.logger = logging.getLogger(__name__)
        self.configs: Dict[str, Dict[str, Any]] = {}
        self.config_lock = threading.RLock()
        self.watchers: Dict[str, list] = {}
        
        # 确保配置目录存在
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载所有配置
        self._load_all_configs()
    
    def _load_all_configs(self):
        """加载所有配置文件"""
        config_files = [
            'development.yml',
            'production.yml',
            'testing.yml',
            'performance.yml',
            'logging.yml'
        ]
        
        for config_file in config_files:
            config_path = self.config_dir / config_file
            if config_path.exists():
                self._load_config_file(config_file)
            else:
                # 创建默认配置
                self._create_default_config(config_file)
    
    def _load_config_file(self, filename: str):
        """加载配置文件"""
        config_path = self.config_dir / filename
        config_name = filename.split('.')[0]
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if filename.endswith('.yml') or filename.endswith('.yaml'):
                    config_data = yaml.safe_load(f)
                elif filename.endswith('.json'):
                    config_data = json.load(f)
                else:
                    self.logger.warning(f"不支持的配置文件格式: {filename}")
                    return
            
            with self.config_lock:
                self.configs[config_name] = config_data or {}
            
            self.logger.info(f"已加载配置文件: {filename}")
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败 {filename}: {e}")
    
    def _create_default_config(self, filename: str):
        """创建默认配置文件"""
        config_name = filename.split('.')[0]
        default_config = self._get_default_config(config_name)
        
        if default_config:
            self.save_config(config_name, default_config)
    
    def _get_default_config(self, config_name: str) -> Optional[Dict[str, Any]]:
        """获取默认配置"""
        defaults = {
            'development': {
                'app': {
                    'name': '智慧课堂系统',
                    'version': '1.0.0',
                    'debug': True
                },
                'network': {
                    'udp_discovery_port': 8888,
                    'backend_host': 'localhost',
                    'backend_port': 5000
                },
                'video': {
                    'mediamtx_host': 'localhost',
                    'mediamtx_rtmp_port': 1935,
                    'mediamtx_http_port': 8889,
                    'resolution': '1920x1080',
                    'fps': 30,
                    'bitrate': '2M'
                },
                'performance': {
                    'screen_share_delay_max': 1.0,
                    'broadcast_delay_max': 1.5,
                    'max_terminals': 75,
                    'max_groups': 8,
                    'max_screen_shares': 6
                },
                'database': {
                    'url': 'sqlite:///smart_classroom.db',
                    'echo': True
                },
                'files': {
                    'upload_folder': 'uploads',
                    'max_file_size': 104857600,
                    'allowed_extensions': {
                        'image': ['jpg', 'jpeg', 'png', 'gif'],
                        'document': ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'],
                        'video': ['mp4', 'avi', 'mov', 'wmv'],
                        'audio': ['mp3', 'wav', 'aac']
                    }
                }
            },
            'production': {
                'app': {
                    'name': '智慧课堂系统',
                    'version': '1.0.0',
                    'debug': False
                },
                'network': {
                    'udp_discovery_port': 8888,
                    'backend_host': '0.0.0.0',
                    'backend_port': 5000
                },
                'video': {
                    'mediamtx_host': 'localhost',
                    'mediamtx_rtmp_port': 1935,
                    'mediamtx_http_port': 8889,
                    'resolution': '1920x1080',
                    'fps': 30,
                    'bitrate': '4M'
                },
                'performance': {
                    'screen_share_delay_max': 1.0,
                    'broadcast_delay_max': 1.5,
                    'max_terminals': 75,
                    'max_groups': 8,
                    'max_screen_shares': 6
                },
                'database': {
                    'url': 'sqlite:///smart_classroom_prod.db',
                    'echo': False
                }
            },
            'performance': {
                'monitoring': {
                    'enabled': True,
                    'interval': 5,
                    'history_size': 1000
                },
                'thresholds': {
                    'cpu_usage': {'warning': 70.0, 'critical': 85.0},
                    'memory_usage': {'warning': 75.0, 'critical': 90.0},
                    'disk_usage': {'warning': 80.0, 'critical': 95.0},
                    'response_time': {'warning': 1.0, 'critical': 3.0},
                    'video_stream_delay': {'warning': 0.8, 'critical': 1.2},
                    'broadcast_delay': {'warning': 1.2, 'critical': 1.8}
                },
                'optimization': {
                    'auto_gc': True,
                    'memory_limit': 1024,  # MB
                    'cpu_limit': 80,  # %
                    'connection_pool_size': 20
                }
            },
            'logging': {
                'version': 1,
                'disable_existing_loggers': False,
                'formatters': {
                    'standard': {
                        'format': '[%(levelname)s] %(asctime)s - %(name)s - %(message)s'
                    },
                    'detailed': {
                        'format': '[%(levelname)s] %(asctime)s - %(name)s:%(lineno)d - %(funcName)s() - %(message)s'
                    }
                },
                'handlers': {
                    'console': {
                        'class': 'logging.StreamHandler',
                        'level': 'INFO',
                        'formatter': 'standard',
                        'stream': 'ext://sys.stdout'
                    },
                    'file': {
                        'class': 'logging.handlers.RotatingFileHandler',
                        'level': 'DEBUG',
                        'formatter': 'detailed',
                        'filename': 'logs/smart_classroom.log',
                        'maxBytes': 10485760,
                        'backupCount': 5
                    },
                    'error_file': {
                        'class': 'logging.handlers.RotatingFileHandler',
                        'level': 'ERROR',
                        'formatter': 'detailed',
                        'filename': 'logs/smart_classroom_error.log',
                        'maxBytes': 10485760,
                        'backupCount': 5
                    }
                },
                'loggers': {
                    'smart_classroom': {
                        'level': 'DEBUG',
                        'handlers': ['console', 'file', 'error_file'],
                        'propagate': False
                    }
                },
                'root': {
                    'level': 'INFO',
                    'handlers': ['console']
                }
            }
        }
        
        return defaults.get(config_name)
    
    def get_config(self, config_name: str, key_path: str = None) -> Any:
        """获取配置值"""
        with self.config_lock:
            if config_name not in self.configs:
                self.logger.warning(f"配置不存在: {config_name}")
                return None
            
            config = self.configs[config_name]
            
            if key_path is None:
                return config
            
            # 支持点号分隔的路径，如 "app.name"
            keys = key_path.split('.')
            value = config
            
            try:
                for key in keys:
                    value = value[key]
                return value
            except (KeyError, TypeError):
                self.logger.warning(f"配置键不存在: {config_name}.{key_path}")
                return None
    
    def set_config(self, config_name: str, key_path: str, value: Any):
        """设置配置值"""
        with self.config_lock:
            if config_name not in self.configs:
                self.configs[config_name] = {}
            
            config = self.configs[config_name]
            keys = key_path.split('.')
            
            # 导航到父级字典
            current = config
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]
            
            # 设置值
            current[keys[-1]] = value
            
            # 通知观察者
            self._notify_watchers(config_name, key_path, value)
            
            self.logger.info(f"配置已更新: {config_name}.{key_path} = {value}")
    
    def save_config(self, config_name: str, config_data: Dict[str, Any] = None):
        """保存配置到文件"""
        if config_data is not None:
            with self.config_lock:
                self.configs[config_name] = config_data
        
        config_path = self.config_dir / f"{config_name}.yml"
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(
                    self.configs.get(config_name, {}),
                    f,
                    default_flow_style=False,
                    allow_unicode=True,
                    indent=2
                )
            
            self.logger.info(f"配置已保存: {config_path}")
            
        except Exception as e:
            self.logger.error(f"保存配置失败 {config_path}: {e}")
    
    def reload_config(self, config_name: str):
        """重新加载配置"""
        filename = f"{config_name}.yml"
        self._load_config_file(filename)
        self.logger.info(f"配置已重新加载: {config_name}")
    
    def add_watcher(self, config_name: str, callback):
        """添加配置变更监听器"""
        if config_name not in self.watchers:
            self.watchers[config_name] = []
        self.watchers[config_name].append(callback)
    
    def _notify_watchers(self, config_name: str, key_path: str, value: Any):
        """通知配置变更监听器"""
        if config_name in self.watchers:
            for callback in self.watchers[config_name]:
                try:
                    callback(config_name, key_path, value)
                except Exception as e:
                    self.logger.error(f"配置监听器异常: {e}")
    
    def get_environment_config(self) -> Dict[str, Any]:
        """获取当前环境配置"""
        env = os.environ.get('FLASK_ENV', 'development')
        return self.get_config(env) or {}
    
    def merge_configs(self, *config_names: str) -> Dict[str, Any]:
        """合并多个配置"""
        merged = {}
        
        for config_name in config_names:
            config = self.get_config(config_name)
            if config:
                merged = self._deep_merge(merged, config)
        
        return merged
    
    def _deep_merge(self, dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并字典"""
        result = dict1.copy()
        
        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def validate_config(self, config_name: str) -> Dict[str, list]:
        """验证配置"""
        config = self.get_config(config_name)
        if not config:
            return {'errors': [f'配置不存在: {config_name}']}
        
        errors = []
        warnings = []
        
        # 验证网络配置
        if 'network' in config:
            network = config['network']
            if 'backend_port' in network:
                port = network['backend_port']
                if not isinstance(port, int) or port < 1 or port > 65535:
                    errors.append('backend_port 必须是1-65535之间的整数')
        
        # 验证性能配置
        if 'performance' in config:
            perf = config['performance']
            if 'max_terminals' in perf:
                max_terminals = perf['max_terminals']
                if not isinstance(max_terminals, int) or max_terminals < 1:
                    errors.append('max_terminals 必须是正整数')
                elif max_terminals > 100:
                    warnings.append('max_terminals 过大可能影响性能')
        
        # 验证视频配置
        if 'video' in config:
            video = config['video']
            if 'fps' in video:
                fps = video['fps']
                if not isinstance(fps, int) or fps < 1 or fps > 60:
                    errors.append('fps 必须是1-60之间的整数')
        
        return {
            'errors': errors,
            'warnings': warnings,
            'valid': len(errors) == 0
        }
    
    def backup_config(self, config_name: str):
        """备份配置"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = self.config_dir / 'backups'
        backup_dir.mkdir(exist_ok=True)
        
        source_path = self.config_dir / f"{config_name}.yml"
        backup_path = backup_dir / f"{config_name}_{timestamp}.yml"
        
        try:
            import shutil
            shutil.copy2(source_path, backup_path)
            self.logger.info(f"配置已备份: {backup_path}")
            return str(backup_path)
        except Exception as e:
            self.logger.error(f"配置备份失败: {e}")
            return None
    
    def list_configs(self) -> list:
        """列出所有配置"""
        return list(self.configs.keys())
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        info = {
            'config_dir': str(self.config_dir),
            'configs': {},
            'total_configs': len(self.configs)
        }
        
        for name, config in self.configs.items():
            config_file = self.config_dir / f"{name}.yml"
            info['configs'][name] = {
                'file_exists': config_file.exists(),
                'file_size': config_file.stat().st_size if config_file.exists() else 0,
                'last_modified': datetime.fromtimestamp(
                    config_file.stat().st_mtime
                ).isoformat() if config_file.exists() else None,
                'keys_count': len(config) if isinstance(config, dict) else 0
            }
        
        return info

# 全局实例
_config_manager = None

def get_config_manager() -> ConfigManager:
    """获取配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager