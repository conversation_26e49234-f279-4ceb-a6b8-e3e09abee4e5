# 智慧课堂系统 - 后端API服务

## 项目概述

智慧课堂系统后端API服务是基于Flask框架开发的RESTful API服务，为智慧课堂系统提供核心功能支持，包括课堂管理、设备管理、小组管理、文件管理、互动答题等功能。

## 技术栈

- Python 3.8+
- Flask Web框架
- Flask-SQLAlchemy (ORM)
- Flask-SocketIO (WebSocket)
- SQLite数据库
- JWT认证

## 目录结构

```
backend/
├── api/                # API接口模块
│   ├── __init__.py     # API包初始化
│   ├── auth_api.py     # 认证API
│   ├── classroom_api.py # 课堂管理API
│   ├── device_api.py   # 设备管理API
│   ├── file_api.py     # 文件管理API
│   ├── group_api.py    # 小组管理API
│   ├── question_api.py # 互动答题API
│   └── api_docs.md     # API文档
├── models/             # 数据模型
│   ├── __init__.py     # 模型包初始化
│   ├── base.py         # 基础模型
│   ├── classroom.py    # 课堂模型
│   ├── device.py       # 设备模型
│   ├── group.py        # 小组模型
│   ├── question.py     # 题目模型
│   ├── repositories.py # 数据仓库
│   └── student.py      # 学生模型
├── services/           # 业务服务
│   ├── __init__.py     # 服务包初始化
│   ├── classroom_service.py # 课堂服务
│   ├── device_discovery.py  # 设备发现服务
│   ├── device_service.py    # 设备服务
│   └── question_service.py  # 题目服务
├── instance/           # 实例文件夹（数据库、上传文件等）
│   └── uploads/        # 上传文件存储目录
├── __init__.py         # 包初始化
├── app.py              # 应用入口
└── README.md           # 说明文档
```

## 功能模块

1. **认证与授权**
   - 用户登录/登出
   - JWT令牌认证
   - 角色权限控制

2. **课堂管理**
   - 创建/开始课堂
   - 学生加入课堂
   - 课堂状态管理

3. **设备管理**
   - 设备发现与注册
   - 设备连接与心跳
   - 设备状态监控

4. **小组管理**
   - 创建/管理小组
   - 学生分组
   - 自动随机分组

5. **文件管理**
   - 文件上传/下载
   - 文件分发
   - 文件统计

6. **互动答题**
   - 创建/发布题目
   - 学生答题
   - 答题结果统计与分析

## 安装与运行

### 环境要求

- Python 3.8+
- pip包管理器

### 安装依赖

```bash
pip install -r requirements.txt
```

### 初始化数据库

```bash
python -c "from app import app; from models import db; app.app_context().push(); db.create_all()"
```

### 运行服务

```bash
python app.py
```

服务默认运行在 `http://0.0.0.0:5000`

## API文档

详细的API文档请参考 [API文档](api/api_docs.md)

## WebSocket事件

系统使用WebSocket进行实时通信，主要事件包括：

- `connect`: 客户端连接
- `disconnect`: 客户端断开
- `classroom_update`: 课堂状态更新
- `question_publish`: 发布题目
- `answer_submit`: 提交答案
- `group_update`: 小组更新
- `file_distribute`: 文件分发

## 开发指南

### 添加新API

1. 在`api/`目录下创建新的API模块
2. 在`api/__init__.py`中导入并注册新的蓝图
3. 在`app.py`中注册新的蓝图

### 添加新模型

1. 在`models/`目录下创建新的模型模块
2. 在`models/__init__.py`中导入新模型

### 添加新服务

1. 在`services/`目录下创建新的服务模块
2. 在需要的地方导入并使用新服务

## 测试

运行单元测试：

```bash
python -m unittest discover tests
```

## 安全注意事项

- 所有API请求应使用HTTPS
- 敏感操作需要JWT令牌认证
- 文件上传需要验证文件类型和大小
- 定期清理过期的令牌和会话