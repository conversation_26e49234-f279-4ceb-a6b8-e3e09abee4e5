# -*- coding: utf-8 -*-
"""
小组管理数据模型
"""

from datetime import datetime
from sqlalchemy import J<PERSON><PERSON>
from .base import BaseModel, db


class Group(BaseModel):
    """小组信息模型"""
    __tablename__ = 'groups'
    
    # 小组基本信息
    group_id = db.Column(db.String(64), unique=True, nullable=False, index=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    
    # 关联信息
    classroom_id = db.Column(db.Integer, db.ForeignKey('classrooms.id'), nullable=False)
    device_id = db.Column(db.String(64), nullable=True)  # 小组端设备ID
    
    # 小组状态
    status = db.Column(db.String(20), default='created')  # created, active, discussing, presenting
    max_members = db.Column(db.Integer, default=6)  # 最大成员数
    current_members = db.Column(db.Integer, default=0)  # 当前成员数
    
    # 小组配置
    settings = db.Column(JSON, default=dict)  # 小组设置
    
    # 讨论信息
    discussion_topic = db.Column(db.Text, nullable=True)  # 讨论主题
    discussion_start_time = db.Column(db.DateTime, nullable=True)
    discussion_duration = db.Column(db.Integer, nullable=True)  # 讨论时长（分钟）
    
    # 关联关系
    students = db.relationship('Student', backref='group', lazy='dynamic')
    devices = db.relationship('Device', backref='group', lazy='dynamic')
    whiteboards = db.relationship('Whiteboard', back_populates='group', lazy='dynamic')
    
    def __repr__(self):
        return f'<Group {self.group_id}: {self.name}>'
    
    def add_student(self, student):
        """添加学生到小组"""
        if self.current_members >= self.max_members:
            return False, "小组人数已满"
        
        student.group_id = self.id
        self.current_members += 1
        db.session.commit()
        return True, "添加成功"
    
    def remove_student(self, student):
        """从小组移除学生"""
        if student.group_id == self.id:
            student.group_id = None
            self.current_members = max(0, self.current_members - 1)
            db.session.commit()
            return True, "移除成功"
        return False, "学生不在此小组"
    
    def start_discussion(self, topic=None, duration=None):
        """开始讨论"""
        self.status = 'discussing'
        self.discussion_topic = topic
        self.discussion_start_time = datetime.utcnow()
        self.discussion_duration = duration
        db.session.commit()
    
    def end_discussion(self):
        """结束讨论"""
        self.status = 'active'
        db.session.commit()
    
    def start_presentation(self):
        """开始展示"""
        self.status = 'presenting'
        db.session.commit()
    
    def end_presentation(self):
        """结束展示"""
        self.status = 'active'
        db.session.commit()
    
    def get_discussion_duration_minutes(self):
        """获取讨论持续时间（分钟）"""
        if self.discussion_start_time:
            end_time = datetime.utcnow()
            duration = end_time - self.discussion_start_time
            return int(duration.total_seconds() / 60)
        return 0
    
    def is_full(self):
        """检查小组是否已满"""
        return self.current_members >= self.max_members
    
    def get_student_list(self):
        """获取小组学生列表"""
        return self.students.all()
    
    @classmethod
    def get_by_group_id(cls, group_id):
        """根据小组ID获取小组"""
        return cls.query.filter_by(group_id=group_id).first()
    
    @classmethod
    def get_classroom_groups(cls, classroom_id):
        """获取课堂的所有小组"""
        return cls.query.filter_by(classroom_id=classroom_id).all()
    
    @classmethod
    def create_groups_for_classroom(cls, classroom_id, group_count, group_prefix="小组"):
        """为课堂创建指定数量的小组"""
        groups = []
        for i in range(1, group_count + 1):
            group = cls(
                group_id=f"{classroom_id}_group_{i}",
                name=f"{group_prefix}{i}",
                classroom_id=classroom_id
            )
            group.save()
            groups.append(group)
        return groups


class GroupScore(BaseModel):
    """小组评分模型"""
    __tablename__ = 'group_scores'
    
    group_id = db.Column(db.Integer, db.ForeignKey('groups.id'), nullable=False)
    scorer_id = db.Column(db.String(64), nullable=False)  # 评分者ID（通常是教师）
    scorer_name = db.Column(db.String(100), nullable=False)
    
    # 评分信息
    score_type = db.Column(db.String(50), nullable=False)  # discussion, presentation, participation
    score = db.Column(db.Float, nullable=False)
    max_score = db.Column(db.Float, default=100.0)
    
    # 评分详情
    criteria = db.Column(db.String(200), nullable=True)  # 评分标准
    comment = db.Column(db.Text, nullable=True)  # 评分备注
    
    # 关联关系
    group = db.relationship('Group', backref='scores')
    
    def __repr__(self):
        return f'<GroupScore {self.group.name}: {self.score}/{self.max_score}>'
    
    def get_percentage(self):
        """获取评分百分比"""
        if self.max_score > 0:
            return (self.score / self.max_score) * 100
        return 0
    
    @classmethod
    def add_score(cls, group_id, scorer_id, scorer_name, score_type, score, max_score=100.0, criteria=None, comment=None):
        """添加小组评分"""
        group_score = cls(
            group_id=group_id,
            scorer_id=scorer_id,
            scorer_name=scorer_name,
            score_type=score_type,
            score=score,
            max_score=max_score,
            criteria=criteria,
            comment=comment
        )
        group_score.save()
        return group_score
    
    @classmethod
    def get_group_scores(cls, group_id):
        """获取小组所有评分"""
        return cls.query.filter_by(group_id=group_id).all()
    
    @classmethod
    def get_average_score(cls, group_id, score_type=None):
        """获取小组平均分"""
        query = cls.query.filter_by(group_id=group_id)
        if score_type:
            query = query.filter_by(score_type=score_type)
        
        scores = query.all()
        if not scores:
            return 0
        
        total_score = sum(s.score for s in scores)
        return total_score / len(scores)