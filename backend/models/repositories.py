# -*- coding: utf-8 -*-
"""
数据仓库模式 - 提供更高级的数据访问接口
"""

from datetime import datetime, timedelta
from .base import db
from . import Classroom, Student, Group, Device, Question, Answer


class ClassroomRepository:
    """课堂数据仓库"""
    
    @staticmethod
    def find_active_classrooms():
        """查找活跃的课堂"""
        return Classroom.query.filter_by(status='active').all()
    
    @staticmethod
    def find_classrooms_by_teacher(teacher_id, limit=10):
        """查找教师的最近课堂"""
        return Classroom.query.filter_by(teacher_id=teacher_id)\
                             .order_by(Classroom.created_at.desc())\
                             .limit(limit).all()
    
    @staticmethod
    def get_classroom_summary(classroom_id):
        """获取课堂汇总信息"""
        classroom = Classroom.get_by_id(classroom_id)
        if not classroom:
            return None
        
        # 使用联接查询优化性能
        from sqlalchemy import func
        
        # 学生统计
        student_stats = db.session.query(
            func.count(Student.id).label('total'),
            func.count(Student.id).filter(Student.status == 'online').label('online'),
            func.count(Student.id).filter(Student.attendance_status == 'present').label('present')
        ).filter(Student.classroom_id == classroom_id).first()
        
        # 小组统计
        group_count = Group.query.filter_by(classroom_id=classroom_id).count()
        
        # 题目统计
        question_stats = db.session.query(
            func.count(Question.id).label('total'),
            func.count(Question.id).filter(Question.status == 'published').label('active')
        ).filter(Question.classroom_id == classroom_id).first()
        
        return {
            'classroom': classroom.to_dict(),
            'students': {
                'total': student_stats.total or 0,
                'online': student_stats.online or 0,
                'present': student_stats.present or 0
            },
            'groups': group_count,
            'questions': {
                'total': question_stats.total or 0,
                'active': question_stats.active or 0
            }
        }


class StudentRepository:
    """学生数据仓库"""
    
    @staticmethod
    def find_students_with_groups(classroom_id):
        """查找学生及其小组信息"""
        return db.session.query(Student, Group)\
                         .outerjoin(Group, Student.group_id == Group.id)\
                         .filter(Student.classroom_id == classroom_id)\
                         .all()
    
    @staticmethod
    def get_participation_ranking(classroom_id, limit=10):
        """获取参与度排行"""
        return Student.query.filter_by(classroom_id=classroom_id)\
                           .order_by(Student.participation_score.desc())\
                           .limit(limit).all()
    
    @staticmethod
    def get_attendance_report(classroom_id):
        """获取考勤报告"""
        from sqlalchemy import func
        
        attendance_stats = db.session.query(
            Student.attendance_status,
            func.count(Student.id).label('count')
        ).filter(Student.classroom_id == classroom_id)\
         .group_by(Student.attendance_status).all()
        
        return {status: count for status, count in attendance_stats}


class QuestionRepository:
    """题目数据仓库"""
    
    @staticmethod
    def find_questions_with_stats(classroom_id):
        """查找题目及统计信息"""
        from sqlalchemy import func
        
        return db.session.query(
            Question,
            func.count(Answer.id).label('answer_count'),
            func.count(Answer.id).filter(Answer.is_correct == True).label('correct_count')
        ).outerjoin(Answer, Question.id == Answer.question_id)\
         .filter(Question.classroom_id == classroom_id)\
         .group_by(Question.id).all()
    
    @staticmethod
    def get_answer_analysis(question_id):
        """获取答案分析"""
        from sqlalchemy import func
        
        # 答案分布
        answer_distribution = db.session.query(
            Answer.content,
            func.count(Answer.id).label('count')
        ).filter(Answer.question_id == question_id)\
         .group_by(Answer.content).all()
        
        # 响应时间分析
        response_times = db.session.query(Answer.response_time)\
                                  .filter(Answer.question_id == question_id,
                                         Answer.response_time.isnot(None)).all()
        
        times = [t[0] for t in response_times]
        avg_time = sum(times) / len(times) if times else 0
        
        return {
            'distribution': {str(content): count for content, count in answer_distribution},
            'response_time': {
                'average': avg_time,
                'count': len(times)
            }
        }


class DeviceRepository:
    """设备数据仓库"""
    
    @staticmethod
    def find_online_devices_by_type(device_type=None):
        """查找在线设备"""
        query = Device.query.filter_by(status='online')
        if device_type:
            query = query.filter_by(device_type=device_type)
        return query.all()
    
    @staticmethod
    def get_device_health_report():
        """获取设备健康报告"""
        from sqlalchemy import func
        
        # 设备状态统计
        status_stats = db.session.query(
            Device.status,
            func.count(Device.id).label('count')
        ).group_by(Device.status).all()
        
        # 设备类型统计
        type_stats = db.session.query(
            Device.device_type,
            func.count(Device.id).label('count')
        ).group_by(Device.device_type).all()
        
        # 离线时间超过阈值的设备
        offline_threshold = datetime.utcnow() - timedelta(minutes=10)
        offline_devices = Device.query.filter(
            Device.last_heartbeat < offline_threshold
        ).all()
        
        return {
            'status_distribution': {status: count for status, count in status_stats},
            'type_distribution': {device_type: count for device_type, count in type_stats},
            'offline_devices': [d.to_dict() for d in offline_devices]
        }