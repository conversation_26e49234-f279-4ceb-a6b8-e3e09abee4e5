# -*- coding: utf-8 -*-
"""
课堂报告和数据分析模型
"""

from datetime import datetime, timedelta
from sqlalchemy import JSON, func
from .base import BaseModel, db


class ClassroomReport(BaseModel):
    """课堂报告模型"""
    __tablename__ = 'classroom_reports'
    
    # 报告基本信息
    report_id = db.Column(db.String(64), unique=True, nullable=False, index=True)
    classroom_id = db.Column(db.In<PERSON>ger, db.ForeignKey('classrooms.id'), nullable=False)
    report_type = db.Column(db.String(50), default='comprehensive')  # comprehensive, attendance, interaction, quiz
    
    # 报告内容
    title = db.Column(db.String(200), nullable=False)
    summary = db.Column(db.Text, nullable=True)
    data = db.Column(JSON, default=dict)  # 报告数据
    charts = db.Column(JSON, default=list)  # 图表配置
    
    # 生成信息
    generated_by = db.Column(db.String(64), nullable=False)
    generated_at = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='generating')  # generating, completed, failed
    
    # 时间范围
    start_time = db.Column(db.DateTime, nullable=True)
    end_time = db.Column(db.DateTime, nullable=True)
    
    # 备注信息
    notes = db.Column(db.Text, nullable=True)
    tags = db.Column(JSON, default=list)
    
    # 关联关系
    classroom = db.relationship('Classroom', backref='reports')
    
    def __repr__(self):
        return f'<ClassroomReport {self.report_id}: {self.title}>'
    
    def mark_completed(self):
        """标记报告完成"""
        self.status = 'completed'
        db.session.commit()
    
    def mark_failed(self, error_message=None):
        """标记报告失败"""
        self.status = 'failed'
        if error_message:
            self.notes = error_message
        db.session.commit()
    
    def add_chart(self, chart_type, chart_data, title=None):
        """添加图表"""
        chart = {
            'type': chart_type,
            'title': title or f'{chart_type.title()} Chart',
            'data': chart_data,
            'created_at': datetime.utcnow().isoformat()
        }
        if not self.charts:
            self.charts = []
        self.charts.append(chart)
        db.session.commit()
    
    def update_data(self, key, value):
        """更新报告数据"""
        if not self.data:
            self.data = {}
        self.data[key] = value
        db.session.commit()
    
    @classmethod
    def create_report(cls, classroom_id, report_type, title, generated_by, start_time=None, end_time=None):
        """创建报告"""
        report_id = f"report_{classroom_id}_{int(datetime.utcnow().timestamp())}"
        
        report = cls(
            report_id=report_id,
            classroom_id=classroom_id,
            report_type=report_type,
            title=title,
            generated_by=generated_by,
            start_time=start_time,
            end_time=end_time
        )
        report.save()
        return report
    
    @classmethod
    def get_by_report_id(cls, report_id):
        """根据报告ID获取报告"""
        return cls.query.filter_by(report_id=report_id).first()
    
    @classmethod
    def get_classroom_reports(cls, classroom_id, limit=20):
        """获取课堂报告列表"""
        return cls.query.filter_by(classroom_id=classroom_id)\
                       .order_by(cls.created_at.desc())\
                       .limit(limit).all()


class ReportTemplate(BaseModel):
    """报告模板模型"""
    __tablename__ = 'report_templates'
    
    # 模板基本信息
    template_id = db.Column(db.String(64), unique=True, nullable=False, index=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)
    
    # 模板配置
    template_type = db.Column(db.String(50), nullable=False)
    config = db.Column(JSON, default=dict)  # 模板配置
    sections = db.Column(JSON, default=list)  # 报告章节
    
    # 创建信息
    created_by = db.Column(db.String(64), nullable=False)
    is_default = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Boolean, default=True)
    
    def __repr__(self):
        return f'<ReportTemplate {self.template_id}: {self.name}>'
    
    @classmethod
    def get_by_template_id(cls, template_id):
        """根据模板ID获取模板"""
        return cls.query.filter_by(template_id=template_id).first()
    
    @classmethod
    def get_default_templates(cls):
        """获取默认模板"""
        return cls.query.filter_by(is_default=True, is_active=True).all()


class LearningAnalytics(BaseModel):
    """学习分析模型"""
    __tablename__ = 'learning_analytics'
    
    # 分析基本信息
    analytics_id = db.Column(db.String(64), unique=True, nullable=False, index=True)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    classroom_id = db.Column(db.Integer, db.ForeignKey('classrooms.id'), nullable=False)
    
    # 分析类型
    analysis_type = db.Column(db.String(50), nullable=False)  # participation, performance, behavior
    
    # 分析数据
    metrics = db.Column(JSON, default=dict)  # 指标数据
    insights = db.Column(JSON, default=list)  # 洞察结果
    recommendations = db.Column(JSON, default=list)  # 推荐建议
    
    # 时间范围
    analysis_period_start = db.Column(db.DateTime, nullable=False)
    analysis_period_end = db.Column(db.DateTime, nullable=False)
    
    # 分析状态
    status = db.Column(db.String(20), default='pending')  # pending, completed, failed
    confidence_score = db.Column(db.Float, default=0.0)  # 置信度评分
    
    # 关联关系
    student = db.relationship('Student', backref='analytics')
    classroom = db.relationship('Classroom', backref='analytics')
    
    def __repr__(self):
        return f'<LearningAnalytics {self.analytics_id}: {self.analysis_type}>'
    
    def add_insight(self, insight_type, description, confidence=0.0):
        """添加洞察"""
        insight = {
            'type': insight_type,
            'description': description,
            'confidence': confidence,
            'timestamp': datetime.utcnow().isoformat()
        }
        if not self.insights:
            self.insights = []
        self.insights.append(insight)
        db.session.commit()
    
    def add_recommendation(self, recommendation_type, description, priority='medium'):
        """添加推荐"""
        recommendation = {
            'type': recommendation_type,
            'description': description,
            'priority': priority,
            'timestamp': datetime.utcnow().isoformat()
        }
        if not self.recommendations:
            self.recommendations = []
        self.recommendations.append(recommendation)
        db.session.commit()
    
    @classmethod
    def create_analysis(cls, student_id, classroom_id, analysis_type, start_time, end_time):
        """创建学习分析"""
        analytics_id = f"analytics_{student_id}_{analysis_type}_{int(datetime.utcnow().timestamp())}"
        
        analytics = cls(
            analytics_id=analytics_id,
            student_id=student_id,
            classroom_id=classroom_id,
            analysis_type=analysis_type,
            analysis_period_start=start_time,
            analysis_period_end=end_time
        )
        analytics.save()
        return analytics
    
    @classmethod
    def get_student_analytics(cls, student_id, analysis_type=None):
        """获取学生分析数据"""
        query = cls.query.filter_by(student_id=student_id)
        if analysis_type:
            query = query.filter_by(analysis_type=analysis_type)
        return query.order_by(cls.created_at.desc()).all()


class DataExport(BaseModel):
    """数据导出模型"""
    __tablename__ = 'data_exports'
    
    # 导出基本信息
    export_id = db.Column(db.String(64), unique=True, nullable=False, index=True)
    classroom_id = db.Column(db.Integer, db.ForeignKey('classrooms.id'), nullable=False)
    
    # 导出配置
    export_type = db.Column(db.String(50), nullable=False)  # csv, excel, pdf, json
    data_type = db.Column(db.String(50), nullable=False)  # attendance, answers, interactions, full_report
    
    # 文件信息
    filename = db.Column(db.String(500), nullable=False)
    file_path = db.Column(db.String(1000), nullable=True)
    file_size = db.Column(db.Integer, nullable=True)
    
    # 导出状态
    status = db.Column(db.String(20), default='pending')  # pending, processing, completed, failed
    progress = db.Column(db.Integer, default=0)  # 进度百分比
    
    # 请求信息
    requested_by = db.Column(db.String(64), nullable=False)
    requested_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime, nullable=True)
    
    # 过滤条件
    filters = db.Column(JSON, default=dict)
    
    # 关联关系
    classroom = db.relationship('Classroom', backref='exports')
    
    def __repr__(self):
        return f'<DataExport {self.export_id}: {self.filename}>'
    
    def update_progress(self, progress):
        """更新进度"""
        self.progress = min(100, max(0, progress))
        if self.progress == 100:
            self.status = 'completed'
            self.completed_at = datetime.utcnow()
        db.session.commit()
    
    def mark_failed(self, error_message=None):
        """标记失败"""
        self.status = 'failed'
        if error_message and not self.filters:
            self.filters = {}
        if error_message:
            self.filters['error'] = error_message
        db.session.commit()
    
    @classmethod
    def create_export(cls, classroom_id, export_type, data_type, filename, requested_by, filters=None):
        """创建导出任务"""
        export_id = f"export_{classroom_id}_{int(datetime.utcnow().timestamp())}"
        
        export = cls(
            export_id=export_id,
            classroom_id=classroom_id,
            export_type=export_type,
            data_type=data_type,
            filename=filename,
            requested_by=requested_by,
            filters=filters or {}
        )
        export.save()
        return export
    
    @classmethod
    def get_by_export_id(cls, export_id):
        """根据导出ID获取导出任务"""
        return cls.query.filter_by(export_id=export_id).first()
    
    @classmethod
    def get_user_exports(cls, user_id, limit=20):
        """获取用户的导出任务"""
        return cls.query.filter_by(requested_by=user_id)\
                       .order_by(cls.created_at.desc())\
                       .limit(limit).all()