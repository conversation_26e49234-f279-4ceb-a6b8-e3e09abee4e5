# -*- coding: utf-8 -*-
"""
数据模型包初始化
"""

from .base import db, BaseModel, init_db
from .classroom import Classroom, ClassroomActivity
from .device import Device, DeviceLog
from .group import Group, GroupScore
from .question import Question, Answer
from .student import Student, StudentActivity
from .whiteboard import Whiteboard, WhiteboardVersion
from .display import DisplayConfig, GroupTimer, DiscussionTopic, DisplayLog
from .report import ClassroomReport, ReportTemplate, LearningAnalytics, DataExport
from .security import (
    User, UserSession, UserPermission, DeviceAccess, 
    AuditLog, SecurityConfig, DataEncryption,
    UserRole, Permission
)
from .repositories import (
    ClassroomRepository, StudentRepository, 
    QuestionRepository, DeviceRepository
)

__all__ = [
    'db', 'BaseModel', 'init_db',
    'Classroom', 'ClassroomActivity',
    'Device', 'DeviceLog',
    'Group', 'GroupScore',
    'Question', 'Answer',
    'Student', 'StudentActivity',
    'Whiteboard', 'WhiteboardVersion',
    'DisplayConfig', 'GroupTimer', 'DiscussionTopic', 'DisplayLog',
    'ClassroomReport', 'ReportTemplate', 'LearningAnalytics', 'DataExport',
    'User', 'UserSession', 'UserPermission', 'DeviceAccess',
    'AuditLog', 'SecurityConfig', 'DataEncryption',
    'UserRole', 'Permission',
    'ClassroomRepository', 'StudentRepository',
    'QuestionRepository', 'DeviceRepository'
]