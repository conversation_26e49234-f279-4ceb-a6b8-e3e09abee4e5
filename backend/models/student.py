# -*- coding: utf-8 -*-
"""
学生管理数据模型
"""

from datetime import datetime
from sqlalchemy import J<PERSON><PERSON>
from .base import BaseModel, db


class Student(BaseModel):
    """学生信息模型"""
    __tablename__ = 'students'
    
    # 学生基本信息
    student_id = db.Column(db.String(64), unique=True, nullable=False, index=True)
    name = db.Column(db.String(100), nullable=False)
    student_number = db.Column(db.String(50), nullable=True)  # 学号
    
    # 联系信息
    email = db.Column(db.String(200), nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    
    # 关联信息
    classroom_id = db.Column(db.Integer, db.ForeignKey('classrooms.id'), nullable=False)
    group_id = db.Column(db.Integer, db.ForeignKey('groups.id'), nullable=True)
    
    # 设备信息
    device_info = db.Column(JSON, default=dict)  # 设备信息
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.String(500), nullable=True)
    
    # 状态信息
    status = db.Column(db.String(20), default='offline')  # online, offline, away
    last_active = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 考勤信息
    attendance_status = db.Column(db.String(20), default='absent')  # present, absent, late
    check_in_time = db.Column(db.DateTime, nullable=True)
    check_in_method = db.Column(db.String(20), nullable=True)  # qr_code, access_code, manual
    
    # 参与统计
    participation_score = db.Column(db.Float, default=0.0)
    interaction_count = db.Column(db.Integer, default=0)
    answer_count = db.Column(db.Integer, default=0)
    correct_answer_count = db.Column(db.Integer, default=0)
    
    # 关联关系
    answers = db.relationship('Answer', backref='student', lazy='dynamic')
    
    def __repr__(self):
        return f'<Student {self.student_id}: {self.name}>'
    
    def check_in(self, method='manual'):
        """学生签到"""
        self.attendance_status = 'present'
        self.check_in_time = datetime.utcnow()
        self.check_in_method = method
        self.status = 'online'
        self.last_active = datetime.utcnow()
        db.session.commit()
    
    def mark_late(self):
        """标记迟到"""
        if self.attendance_status == 'absent':
            self.attendance_status = 'late'
            self.check_in_time = datetime.utcnow()
            db.session.commit()
    
    def update_activity(self):
        """更新活动时间"""
        self.last_active = datetime.utcnow()
        self.status = 'online'
        db.session.commit()
    
    def set_offline(self):
        """设置离线状态"""
        self.status = 'offline'
        db.session.commit()
    
    def is_online(self):
        """检查是否在线"""
        if self.status != 'online':
            return False
        
        # 检查最后活动时间（超过10分钟认为离线）
        if self.last_active:
            time_diff = datetime.utcnow() - self.last_active
            return time_diff.total_seconds() < 600
        return False
    
    def add_interaction(self):
        """增加互动次数"""
        self.interaction_count += 1
        self.update_activity()
        db.session.commit()
    
    def add_answer(self, is_correct=False):
        """增加答题记录"""
        self.answer_count += 1
        if is_correct:
            self.correct_answer_count += 1
        self.add_interaction()
    
    def get_answer_accuracy(self):
        """获取答题准确率"""
        if self.answer_count > 0:
            return (self.correct_answer_count / self.answer_count) * 100
        return 0
    
    def calculate_participation_score(self):
        """计算参与度评分"""
        # 基于互动次数、答题准确率等计算参与度
        base_score = min(self.interaction_count * 2, 50)  # 互动次数，最高50分
        accuracy_score = self.get_answer_accuracy() * 0.3  # 准确率，最高30分
        attendance_score = 20 if self.attendance_status == 'present' else 0  # 出勤，20分
        
        self.participation_score = base_score + accuracy_score + attendance_score
        db.session.commit()
        return self.participation_score
    
    @classmethod
    def get_by_student_id(cls, student_id):
        """根据学生ID获取学生"""
        return cls.query.filter_by(student_id=student_id).first()
    
    @classmethod
    def get_classroom_students(cls, classroom_id):
        """获取课堂的所有学生"""
        return cls.query.filter_by(classroom_id=classroom_id).all()
    
    @classmethod
    def get_group_students(cls, group_id):
        """获取小组的所有学生"""
        return cls.query.filter_by(group_id=group_id).all()
    
    @classmethod
    def get_online_students(cls, classroom_id):
        """获取课堂在线学生"""
        return cls.query.filter_by(classroom_id=classroom_id, status='online').all()
    
    @classmethod
    def get_present_students(cls, classroom_id):
        """获取已签到学生"""
        return cls.query.filter_by(classroom_id=classroom_id, attendance_status='present').all()
    
    @classmethod
    def bulk_create_students(cls, student_data_list, classroom_id):
        """批量创建学生"""
        students = []
        for data in student_data_list:
            student = cls(
                student_id=data.get('student_id'),
                name=data.get('name'),
                student_number=data.get('student_number'),
                email=data.get('email'),
                phone=data.get('phone'),
                classroom_id=classroom_id
            )
            students.append(student)
        
        db.session.add_all(students)
        db.session.commit()
        return students


class StudentActivity(BaseModel):
    """学生活动记录模型"""
    __tablename__ = 'student_activities'
    
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    activity_type = db.Column(db.String(50), nullable=False)  # login, answer, screen_share, etc.
    description = db.Column(db.Text, nullable=False)
    details = db.Column(JSON, nullable=True)
    
    # 关联关系
    student = db.relationship('Student', backref='activities')
    
    def __repr__(self):
        return f'<StudentActivity {self.student.name}: {self.activity_type}>'
    
    @classmethod
    def log_activity(cls, student_id, activity_type, description, details=None):
        """记录学生活动"""
        activity = cls(
            student_id=student_id,
            activity_type=activity_type,
            description=description,
            details=details or {}
        )
        activity.save()
        return activity
    
    @classmethod
    def get_student_activities(cls, student_id, limit=50):
        """获取学生活动记录"""
        return cls.query.filter_by(student_id=student_id)\
                       .order_by(cls.created_at.desc())\
                       .limit(limit).all()