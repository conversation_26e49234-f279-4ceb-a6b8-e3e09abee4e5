# -*- coding: utf-8 -*-
"""
课堂管理数据模型
"""

from datetime import datetime
from sqlalchemy import J<PERSON><PERSON>
from .base import BaseModel, db


class Classroom(BaseModel):
    """课堂信息模型"""
    __tablename__ = 'classrooms'
    
    # 课堂基本信息
    classroom_id = db.Column(db.String(64), unique=True, nullable=False, index=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)
    
    # 教师信息
    teacher_id = db.Column(db.String(64), nullable=False)
    teacher_name = db.Column(db.String(100), nullable=False)
    
    # 时间信息
    start_time = db.Column(db.DateTime, nullable=True)
    end_time = db.Column(db.DateTime, nullable=True)
    duration = db.Column(db.Integer, nullable=True)  # 预计时长（分钟）
    
    # 状态信息
    status = db.Column(db.String(20), default='created')  # created, active, paused, ended
    
    # 课堂配置
    settings = db.Column(JSON, default=dict)  # 课堂设置
    access_code = db.Column(db.String(10), nullable=True)  # 学生加入码
    qr_code_data = db.Column(db.Text, nullable=True)  # 二维码数据
    
    # 统计信息
    total_students = db.Column(db.Integer, default=0)
    total_groups = db.Column(db.Integer, default=0)
    
    # 关联关系
    devices = db.relationship('Device', backref='classroom', lazy='dynamic')
    groups = db.relationship('Group', backref='classroom', lazy='dynamic')
    students = db.relationship('Student', backref='classroom', lazy='dynamic')
    questions = db.relationship('Question', backref='classroom', lazy='dynamic')
    whiteboards = db.relationship('Whiteboard', back_populates='classroom', lazy='dynamic')
    
    def __repr__(self):
        return f'<Classroom {self.classroom_id}: {self.name}>'
    
    def start_class(self):
        """开始上课"""
        self.status = 'active'
        self.start_time = datetime.utcnow()
        db.session.commit()
    
    def pause_class(self):
        """暂停课堂"""
        self.status = 'paused'
        db.session.commit()
    
    def end_class(self):
        """结束课堂"""
        self.status = 'ended'
        self.end_time = datetime.utcnow()
        db.session.commit()
    
    def is_active(self):
        """检查课堂是否活跃"""
        return self.status == 'active'
    
    def get_duration_minutes(self):
        """获取课堂持续时间（分钟）"""
        if self.start_time:
            end_time = self.end_time or datetime.utcnow()
            duration = end_time - self.start_time
            return int(duration.total_seconds() / 60)
        return 0
    
    def generate_access_code(self):
        """生成学生访问码"""
        import random
        import string
        
        # 生成6位数字码
        self.access_code = ''.join(random.choices(string.digits, k=6))
        db.session.commit()
        return self.access_code
    
    def update_statistics(self):
        """更新统计信息"""
        self.total_students = self.students.count()
        self.total_groups = self.groups.count()
        db.session.commit()
    
    @classmethod
    def get_by_classroom_id(cls, classroom_id):
        """根据课堂ID获取课堂"""
        return cls.query.filter_by(classroom_id=classroom_id).first()
    
    @classmethod
    def get_by_access_code(cls, access_code):
        """根据访问码获取课堂"""
        return cls.query.filter_by(access_code=access_code).first()
    
    @classmethod
    def get_active_classrooms(cls):
        """获取活跃课堂"""
        return cls.query.filter_by(status='active').all()
    
    @classmethod
    def get_teacher_classrooms(cls, teacher_id):
        """获取教师的课堂"""
        return cls.query.filter_by(teacher_id=teacher_id).all()


class ClassroomActivity(BaseModel):
    """课堂活动记录模型"""
    __tablename__ = 'classroom_activities'
    
    classroom_id = db.Column(db.Integer, db.ForeignKey('classrooms.id'), nullable=False)
    activity_type = db.Column(db.String(50), nullable=False)  # login, answer, screen_share, etc.
    actor_id = db.Column(db.String(64), nullable=False)  # 操作者ID
    actor_name = db.Column(db.String(100), nullable=False)  # 操作者姓名
    description = db.Column(db.Text, nullable=False)
    details = db.Column(JSON, nullable=True)
    
    # 关联关系
    classroom = db.relationship('Classroom', backref='activities')
    
    def __repr__(self):
        return f'<ClassroomActivity {self.activity_type}: {self.actor_name}>'
    
    @classmethod
    def log_activity(cls, classroom_id, activity_type, actor_id, actor_name, description, details=None):
        """记录课堂活动"""
        activity = cls(
            classroom_id=classroom_id,
            activity_type=activity_type,
            actor_id=actor_id,
            actor_name=actor_name,
            description=description,
            details=details or {}
        )
        activity.save()
        return activity
    
    @classmethod
    def get_classroom_activities(cls, classroom_id, limit=100):
        """获取课堂活动记录"""
        return cls.query.filter_by(classroom_id=classroom_id)\
                       .order_by(cls.created_at.desc())\
                       .limit(limit).all()