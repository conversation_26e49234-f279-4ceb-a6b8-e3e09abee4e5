#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 多屏显示数据模型
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, JSON
from .base import BaseModel, db


class DisplayConfig(BaseModel):
    """显示配置模型"""
    __tablename__ = 'display_configs'
    
    config_id = Column(String(64), unique=True, nullable=False, index=True, comment='配置ID')
    classroom_id = Column(String(64), nullable=False, index=True, comment='课堂ID')
    name = Column(String(128), nullable=False, comment='配置名称')
    description = Column(Text, comment='配置描述')
    mode = Column(String(32), nullable=False, comment='显示模式')
    layout = Column(String(32), nullable=False, comment='布局类型')
    target_devices = Column(JSON, nullable=False, comment='目标设备ID列表')
    content_sources = Column(JSON, nullable=False, comment='内容源ID列表')
    sync_enabled = Column(Boolean, default=True, comment='是否启用同步')
    is_active = Column(Boolean, default=False, comment='是否激活')
    created_time = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    updated_time = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'config_id': self.config_id,
            'classroom_id': self.classroom_id,
            'name': self.name,
            'description': self.description,
            'mode': self.mode,
            'layout': self.layout,
            'target_devices': self.target_devices,
            'content_sources': self.content_sources,
            'sync_enabled': self.sync_enabled,
            'is_active': self.is_active,
            'created_time': self.created_time.isoformat() if self.created_time else None,
            'updated_time': self.updated_time.isoformat() if self.updated_time else None
        }
    
    @classmethod
    def get_by_config_id(cls, config_id):
        """根据配置ID获取配置"""
        return cls.query.filter_by(config_id=config_id).first()
    
    @classmethod
    def get_by_classroom(cls, classroom_id):
        """获取课堂的所有显示配置"""
        return cls.query.filter_by(classroom_id=classroom_id).all()
    
    @classmethod
    def get_active_configs(cls, classroom_id=None):
        """获取活动的显示配置"""
        query = cls.query.filter_by(is_active=True)
        if classroom_id:
            query = query.filter_by(classroom_id=classroom_id)
        return query.all()


class GroupTimer(BaseModel):
    """分组计时器模型"""
    __tablename__ = 'group_timers'
    
    timer_id = Column(String(64), unique=True, nullable=False, index=True, comment='计时器ID')
    classroom_id = Column(String(64), nullable=False, index=True, comment='课堂ID')
    title = Column(String(128), nullable=False, comment='计时器标题')
    description = Column(Text, comment='计时器描述')
    duration = Column(Integer, nullable=False, comment='持续时间（秒）')
    group_ids = Column(JSON, nullable=False, comment='小组ID列表')
    start_time = Column(DateTime, comment='开始时间')
    end_time = Column(DateTime, comment='结束时间')
    is_active = Column(Boolean, default=False, comment='是否激活')
    is_paused = Column(Boolean, default=False, comment='是否暂停')
    pause_time = Column(DateTime, comment='暂停时间')
    remaining_time = Column(Integer, comment='剩余时间（秒）')
    created_time = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    updated_time = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'timer_id': self.timer_id,
            'classroom_id': self.classroom_id,
            'title': self.title,
            'description': self.description,
            'duration': self.duration,
            'group_ids': self.group_ids,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'is_active': self.is_active,
            'is_paused': self.is_paused,
            'pause_time': self.pause_time.isoformat() if self.pause_time else None,
            'remaining_time': self.remaining_time,
            'created_time': self.created_time.isoformat() if self.created_time else None,
            'updated_time': self.updated_time.isoformat() if self.updated_time else None
        }
    
    @classmethod
    def get_by_timer_id(cls, timer_id):
        """根据计时器ID获取计时器"""
        return cls.query.filter_by(timer_id=timer_id).first()
    
    @classmethod
    def get_by_classroom(cls, classroom_id):
        """获取课堂的所有计时器"""
        return cls.query.filter_by(classroom_id=classroom_id).all()
    
    @classmethod
    def get_active_timers(cls, classroom_id=None):
        """获取活动的计时器"""
        query = cls.query.filter_by(is_active=True)
        if classroom_id:
            query = query.filter_by(classroom_id=classroom_id)
        return query.all()


class DiscussionTopic(BaseModel):
    """讨论主题模型"""
    __tablename__ = 'discussion_topics'
    
    topic_id = Column(String(64), unique=True, nullable=False, index=True, comment='主题ID')
    classroom_id = Column(String(64), nullable=False, index=True, comment='课堂ID')
    title = Column(String(128), nullable=False, comment='主题标题')
    content = Column(Text, nullable=False, comment='主题内容')
    image_data = Column(Text, comment='图片数据（Base64编码）')
    target_groups = Column(JSON, comment='目标小组ID列表')
    is_distributed = Column(Boolean, default=False, comment='是否已分发')
    distributed_time = Column(DateTime, comment='分发时间')
    created_time = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    updated_time = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'topic_id': self.topic_id,
            'classroom_id': self.classroom_id,
            'title': self.title,
            'content': self.content,
            'image_data': self.image_data,
            'target_groups': self.target_groups or [],
            'is_distributed': self.is_distributed,
            'distributed_time': self.distributed_time.isoformat() if self.distributed_time else None,
            'created_time': self.created_time.isoformat() if self.created_time else None,
            'updated_time': self.updated_time.isoformat() if self.updated_time else None
        }
    
    @classmethod
    def get_by_topic_id(cls, topic_id):
        """根据主题ID获取主题"""
        return cls.query.filter_by(topic_id=topic_id).first()
    
    @classmethod
    def get_by_classroom(cls, classroom_id):
        """获取课堂的所有讨论主题"""
        return cls.query.filter_by(classroom_id=classroom_id).all()
    
    @classmethod
    def get_distributed_topics(cls, classroom_id=None):
        """获取已分发的讨论主题"""
        query = cls.query.filter_by(is_distributed=True)
        if classroom_id:
            query = query.filter_by(classroom_id=classroom_id)
        return query.all()


class DisplayLog(BaseModel):
    """显示操作日志模型"""
    __tablename__ = 'display_logs'
    
    classroom_id = Column(String(64), nullable=False, index=True, comment='课堂ID')
    config_id = Column(String(64), index=True, comment='配置ID')
    operation = Column(String(32), nullable=False, comment='操作类型')
    description = Column(Text, comment='操作描述')
    operator_id = Column(String(64), comment='操作者ID')
    target_devices = Column(JSON, comment='目标设备列表')
    operation_data = Column(JSON, comment='操作数据')
    result = Column(String(16), comment='操作结果')
    error_message = Column(Text, comment='错误信息')
    created_time = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'classroom_id': self.classroom_id,
            'config_id': self.config_id,
            'operation': self.operation,
            'description': self.description,
            'operator_id': self.operator_id,
            'target_devices': self.target_devices,
            'operation_data': self.operation_data,
            'result': self.result,
            'error_message': self.error_message,
            'created_time': self.created_time.isoformat() if self.created_time else None
        }
    
    @classmethod
    def log_operation(cls, classroom_id, operation, description=None, 
                     config_id=None, operator_id=None, target_devices=None,
                     operation_data=None, result='success', error_message=None):
        """记录显示操作日志"""
        log = cls(
            classroom_id=classroom_id,
            config_id=config_id,
            operation=operation,
            description=description,
            operator_id=operator_id,
            target_devices=target_devices,
            operation_data=operation_data,
            result=result,
            error_message=error_message
        )
        log.save()
        return log
    
    @classmethod
    def get_classroom_logs(cls, classroom_id, limit=50):
        """获取课堂的显示操作日志"""
        return cls.query.filter_by(classroom_id=classroom_id)\
                       .order_by(cls.created_time.desc())\
                       .limit(limit).all()
    
    @classmethod
    def get_config_logs(cls, config_id, limit=20):
        """获取配置的操作日志"""
        return cls.query.filter_by(config_id=config_id)\
                       .order_by(cls.created_time.desc())\
                       .limit(limit).all()