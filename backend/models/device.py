# -*- coding: utf-8 -*-
"""
设备管理数据模型
"""

from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON>
from .base import BaseModel, db


class Device(BaseModel):
    """设备信息模型"""
    __tablename__ = 'devices'
    
    # 设备基本信息
    device_id = db.Column(db.String(64), unique=True, nullable=False, index=True)
    device_type = db.Column(db.String(20), nullable=False)  # teacher, group, student
    device_name = db.Column(db.String(100), nullable=False)
    
    # 网络信息
    ip_address = db.Column(db.String(45), nullable=False)  # 支持IPv6
    mac_address = db.Column(db.String(17), nullable=True)
    port = db.Column(db.Integer, default=8080)
    
    # 状态信息
    status = db.Column(db.String(20), default='offline')  # online, offline, busy, error
    last_heartbeat = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 设备能力和配置
    capabilities = db.Column(JSON, default=list)  # 设备支持的功能列表
    screen_resolution = db.Column(db.String(20), nullable=True)  # 1920x1080
    os_info = db.Column(db.String(100), nullable=True)
    
    # 关联信息
    classroom_id = db.Column(db.Integer, db.ForeignKey('classrooms.id'), nullable=True)
    group_id = db.Column(db.Integer, db.ForeignKey('groups.id'), nullable=True)
    
    def __repr__(self):
        return f'<Device {self.device_id}: {self.device_type}>'
    
    def is_online(self):
        """检查设备是否在线"""
        if self.status != 'online':
            return False
        
        # 检查心跳时间（超过5分钟认为离线）
        if self.last_heartbeat:
            time_diff = datetime.utcnow() - self.last_heartbeat
            return time_diff.total_seconds() < 300
        return False
    
    def update_heartbeat(self):
        """更新心跳时间"""
        self.last_heartbeat = datetime.utcnow()
        self.status = 'online'
        db.session.commit()
    
    def set_offline(self):
        """设置设备离线"""
        self.status = 'offline'
        db.session.commit()
    
    @classmethod
    def get_by_device_id(cls, device_id):
        """根据设备ID获取设备"""
        return cls.query.filter_by(device_id=device_id).first()
    
    @classmethod
    def get_online_devices(cls):
        """获取所有在线设备"""
        return cls.query.filter_by(status='online').all()
    
    @classmethod
    def get_devices_by_type(cls, device_type):
        """根据设备类型获取设备"""
        return cls.query.filter_by(device_type=device_type).all()
    
    @classmethod
    def get_devices_by_classroom(cls, classroom_id):
        """根据课堂ID获取设备"""
        return cls.query.filter_by(classroom_id=classroom_id).all()


class DeviceLog(BaseModel):
    """设备日志模型"""
    __tablename__ = 'device_logs'
    
    device_id = db.Column(db.String(64), db.ForeignKey('devices.device_id'), nullable=False)
    log_type = db.Column(db.String(20), nullable=False)  # connect, disconnect, error, info
    message = db.Column(db.Text, nullable=False)
    details = db.Column(JSON, nullable=True)
    
    # 关联关系
    device = db.relationship('Device', backref='logs')
    
    def __repr__(self):
        return f'<DeviceLog {self.device_id}: {self.log_type}>'
    
    @classmethod
    def log_event(cls, device_id, log_type, message, details=None):
        """记录设备事件"""
        log = cls(
            device_id=device_id,
            log_type=log_type,
            message=message,
            details=details or {}
        )
        log.save()
        return log
    
    @classmethod
    def get_device_logs(cls, device_id, limit=100):
        """获取设备日志"""
        return cls.query.filter_by(device_id=device_id)\
                       .order_by(cls.created_at.desc())\
                       .limit(limit).all()