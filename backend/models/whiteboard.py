#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
白板数据模型
"""

from datetime import datetime
from .base import BaseModel, db

class Whiteboard(BaseModel):
    """白板模型"""
    __tablename__ = 'whiteboards'
    
    whiteboard_id = db.Column(db.String(36), unique=True, nullable=False, index=True)
    name = db.Column(db.String(100), nullable=False)
    group_id = db.Column(db.Integer, db.ForeignKey('groups.id'), nullable=True)
    classroom_id = db.Column(db.Integer, db.<PERSON>ey('classrooms.id'), nullable=True)
    created_by = db.Column(db.String(36), nullable=False)  # 创建者ID
    is_active = db.Column(db.Bo<PERSON>an, default=True)
    
    # 关联关系
    group = db.relationship("Group", back_populates="whiteboards")
    classroom = db.relationship("Classroom", back_populates="whiteboards")
    versions = db.relationship("WhiteboardVersion", back_populates="whiteboard", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Whiteboard(id='{self.id}', name='{self.name}')>"

class WhiteboardVersion(BaseModel):
    """白板版本模型"""
    __tablename__ = 'whiteboard_versions'
    
    version_id = db.Column(db.String(36), unique=True, nullable=False, index=True)
    whiteboard_id = db.Column(db.Integer, db.ForeignKey('whiteboards.id'), nullable=False)
    version_number = db.Column(db.Integer, nullable=False)
    content = db.Column(db.JSON, nullable=True)  # 存储Excalidraw的JSON数据
    thumbnail = db.Column(db.Text, nullable=True)  # Base64编码的缩略图
    created_by = db.Column(db.String(36), nullable=False)  # 创建者ID
    comment = db.Column(db.String(255), nullable=True)
    
    # 关联关系
    whiteboard = db.relationship("Whiteboard", back_populates="versions")
    
    def __repr__(self):
        return f"<WhiteboardVersion(id='{self.id}', whiteboard_id='{self.whiteboard_id}', version={self.version_number})>"