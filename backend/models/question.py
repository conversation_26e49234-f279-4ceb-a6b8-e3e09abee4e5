# -*- coding: utf-8 -*-
"""
题目和答题数据模型
"""

from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON>
from .base import BaseModel, db


class Question(BaseModel):
    """题目信息模型"""
    __tablename__ = 'questions'
    
    # 题目基本信息
    question_id = db.Column(db.String(64), unique=True, nullable=False, index=True)
    title = db.Column(db.String(500), nullable=False)
    content = db.Column(db.Text, nullable=False)
    
    # 题目类型和配置
    question_type = db.Column(db.String(50), nullable=False)  # single_choice, multiple_choice, true_false, text, file_upload
    options = db.Column(JSON, default=list)  # 选项列表
    correct_answer = db.Column(JSON, nullable=True)  # 正确答案
    
    # 关联信息
    classroom_id = db.Column(db.Integer, db.<PERSON><PERSON>('classrooms.id'), nullable=False)
    creator_id = db.Column(db.String(64), nullable=False)  # 创建者ID
    creator_name = db.Column(db.String(100), nullable=False)
    
    # 发布信息
    status = db.Column(db.String(20), default='draft')  # draft, published, closed
    publish_time = db.Column(db.DateTime, nullable=True)
    close_time = db.Column(db.DateTime, nullable=True)
    time_limit = db.Column(db.Integer, nullable=True)  # 答题时限（秒）
    
    # 目标设置
    target_type = db.Column(db.String(20), default='all')  # all, groups, students
    target_groups = db.Column(JSON, default=list)  # 目标小组ID列表
    target_students = db.Column(JSON, default=list)  # 目标学生ID列表
    
    # 统计信息
    total_answers = db.Column(db.Integer, default=0)
    correct_answers = db.Column(db.Integer, default=0)
    
    # 题目配置
    allow_multiple_attempts = db.Column(db.Boolean, default=False)
    show_correct_answer = db.Column(db.Boolean, default=True)
    randomize_options = db.Column(db.Boolean, default=False)
    
    # 关联关系
    answers = db.relationship('Answer', backref='question', lazy='dynamic')
    
    def __repr__(self):
        return f'<Question {self.question_id}: {self.title[:50]}>'
    
    def publish(self, time_limit=None):
        """发布题目"""
        self.status = 'published'
        self.publish_time = datetime.utcnow()
        if time_limit:
            self.time_limit = time_limit
        db.session.commit()
    
    def close(self):
        """关闭题目"""
        self.status = 'closed'
        self.close_time = datetime.utcnow()
        db.session.commit()
    
    def is_active(self):
        """检查题目是否活跃"""
        if self.status != 'published':
            return False
        
        # 检查时间限制
        if self.time_limit and self.publish_time:
            elapsed = (datetime.utcnow() - self.publish_time).total_seconds()
            return elapsed < self.time_limit
        
        return True
    
    def get_remaining_time(self):
        """获取剩余答题时间（秒）"""
        if not self.is_active() or not self.time_limit or not self.publish_time:
            return 0
        
        elapsed = (datetime.utcnow() - self.publish_time).total_seconds()
        remaining = self.time_limit - elapsed
        return max(0, int(remaining))
    
    def check_answer(self, answer_content):
        """检查答案是否正确"""
        if not self.correct_answer:
            return False
        
        if self.question_type in ['single_choice', 'true_false']:
            return answer_content == self.correct_answer
        elif self.question_type == 'multiple_choice':
            if isinstance(answer_content, list) and isinstance(self.correct_answer, list):
                return set(answer_content) == set(self.correct_answer)
        elif self.question_type == 'text':
            # 文本题可以设置多个可接受的答案
            if isinstance(self.correct_answer, list):
                return answer_content.lower().strip() in [ans.lower().strip() for ans in self.correct_answer]
            else:
                return answer_content.lower().strip() == self.correct_answer.lower().strip()
        
        return False
    
    def update_statistics(self):
        """更新统计信息"""
        self.total_answers = self.answers.count()
        self.correct_answers = self.answers.filter_by(is_correct=True).count()
        db.session.commit()
    
    def get_accuracy_rate(self):
        """获取正确率"""
        if self.total_answers > 0:
            return (self.correct_answers / self.total_answers) * 100
        return 0
    
    def get_answer_distribution(self):
        """获取答案分布统计"""
        if self.question_type in ['single_choice', 'multiple_choice', 'true_false']:
            distribution = {}
            for answer in self.answers:
                content = answer.content
                if isinstance(content, list):
                    content = ','.join(sorted(content))
                distribution[content] = distribution.get(content, 0) + 1
            return distribution
        return {}
    
    @classmethod
    def get_by_question_id(cls, question_id):
        """根据题目ID获取题目"""
        return cls.query.filter_by(question_id=question_id).first()
    
    @classmethod
    def get_classroom_questions(cls, classroom_id):
        """获取课堂的所有题目"""
        return cls.query.filter_by(classroom_id=classroom_id).all()
    
    @classmethod
    def get_active_questions(cls, classroom_id):
        """获取课堂的活跃题目"""
        return cls.query.filter_by(classroom_id=classroom_id, status='published').all()


class Answer(BaseModel):
    """答案信息模型"""
    __tablename__ = 'answers'
    
    # 答案基本信息
    answer_id = db.Column(db.String(64), unique=True, nullable=False, index=True)
    content = db.Column(JSON, nullable=False)  # 答案内容
    
    # 关联信息
    question_id = db.Column(db.Integer, db.ForeignKey('questions.id'), nullable=False)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    
    # 答题信息
    submitted_time = db.Column(db.DateTime, default=datetime.utcnow)
    response_time = db.Column(db.Float, nullable=True)  # 答题用时（秒）
    is_correct = db.Column(db.Boolean, default=False)
    
    # 附加信息
    attempt_number = db.Column(db.Integer, default=1)  # 尝试次数
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.String(500), nullable=True)
    
    def __repr__(self):
        return f'<Answer {self.answer_id}: {self.student.name}>'
    
    def calculate_response_time(self, start_time):
        """计算答题用时"""
        if start_time:
            self.response_time = (self.submitted_time - start_time).total_seconds()
            db.session.commit()
    
    @classmethod
    def submit_answer(cls, question_id, student_id, content, start_time=None):
        """提交答案"""
        # 检查是否已经答过题
        existing_answer = cls.query.filter_by(
            question_id=question_id,
            student_id=student_id
        ).first()
        
        question = Question.get_by_id(question_id)
        if not question:
            return None, "题目不存在"
        
        if existing_answer and not question.allow_multiple_attempts:
            return None, "不允许重复答题"
        
        # 检查题目是否还在答题时间内
        if not question.is_active():
            return None, "答题时间已结束"
        
        # 创建答案记录
        answer_id = f"{question_id}_{student_id}_{int(datetime.utcnow().timestamp())}"
        attempt_number = 1
        
        if existing_answer:
            attempt_number = existing_answer.attempt_number + 1
        
        answer = cls(
            answer_id=answer_id,
            question_id=question_id,
            student_id=student_id,
            content=content,
            attempt_number=attempt_number,
            is_correct=question.check_answer(content)
        )
        
        if start_time:
            answer.calculate_response_time(start_time)
        
        answer.save()
        
        # 更新题目统计
        question.update_statistics()
        
        # 更新学生答题记录
        from .student import Student
        student = Student.get_by_id(student_id)
        if student:
            student.add_answer(answer.is_correct)
        
        return answer, "提交成功"
    
    @classmethod
    def get_question_answers(cls, question_id):
        """获取题目的所有答案"""
        return cls.query.filter_by(question_id=question_id).all()
    
    @classmethod
    def get_student_answers(cls, student_id, question_id=None):
        """获取学生的答案"""
        query = cls.query.filter_by(student_id=student_id)
        if question_id:
            query = query.filter_by(question_id=question_id)
        return query.all()
    
    @classmethod
    def get_group_answers(cls, question_id, group_id):
        """获取小组的答案"""
        from .student import Student
        student_ids = [s.id for s in Student.get_group_students(group_id)]
        return cls.query.filter_by(question_id=question_id)\
                       .filter(cls.student_id.in_(student_ids)).all()