#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 启动MediaMTX服务
"""

import os
import sys
import subprocess
import signal
import time
import logging
import argparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('mediamtx')

def get_project_root():
    """获取项目根目录"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    backend_dir = os.path.dirname(current_dir)
    return os.path.dirname(backend_dir)

def start_mediamtx(config_path=None):
    """
    启动MediaMTX服务
    
    Args:
        config_path: 配置文件路径，如果为None则使用默认配置
    
    Returns:
        subprocess.Popen: MediaMTX进程
    """
    project_root = get_project_root()
    
    # 如果未指定配置文件，使用默认配置
    if config_path is None:
        config_path = os.path.join(project_root, 'backend', 'config', 'mediamtx.yml')
    
    # 确保配置文件存在
    if not os.path.exists(config_path):
        logger.error(f"配置文件不存在: {config_path}")
        return None
    
    # 启动MediaMTX
    try:
        command = ["mediamtx", config_path]
        logger.info(f"启动MediaMTX: {' '.join(command)}")
        
        # 创建日志目录
        logs_dir = os.path.join(project_root, 'logs')
        os.makedirs(logs_dir, exist_ok=True)
        
        # 打开日志文件
        log_file = os.path.join(logs_dir, 'mediamtx.log')
        log_fd = open(log_file, 'a')
        
        # 启动进程
        process = subprocess.Popen(
            command,
            stdout=log_fd,
            stderr=log_fd,
            universal_newlines=True
        )
        
        # 等待确认进程已启动
        time.sleep(1)
        
        if process.poll() is not None:
            # 进程已退出
            logger.error(f"MediaMTX启动失败，退出码: {process.returncode}")
            return None
        
        # 保存PID
        pid_file = os.path.join(logs_dir, 'mediamtx.pid')
        with open(pid_file, 'w') as f:
            f.write(str(process.pid))
        
        logger.info(f"MediaMTX已启动 (PID: {process.pid})")
        return process
        
    except FileNotFoundError:
        logger.error("找不到MediaMTX可执行文件，请确保已安装")
        return None
    except Exception as e:
        logger.error(f"启动MediaMTX时出错: {str(e)}")
        return None

def stop_mediamtx():
    """停止MediaMTX服务"""
    project_root = get_project_root()
    pid_file = os.path.join(project_root, 'logs', 'mediamtx.pid')
    
    if not os.path.exists(pid_file):
        logger.warning("找不到MediaMTX PID文件")
        return False
    
    try:
        with open(pid_file, 'r') as f:
            pid = int(f.read().strip())
        
        # 发送终止信号
        os.kill(pid, signal.SIGTERM)
        
        # 等待进程终止
        for _ in range(5):
            try:
                os.kill(pid, 0)  # 检查进程是否存在
                time.sleep(1)
            except OSError:
                # 进程已终止
                break
        else:
            # 如果进程仍在运行，强制终止
            try:
                os.kill(pid, signal.SIGKILL)
                logger.warning(f"强制终止MediaMTX进程 (PID: {pid})")
            except OSError:
                pass
        
        # 删除PID文件
        os.remove(pid_file)
        logger.info(f"MediaMTX已停止 (PID: {pid})")
        return True
        
    except Exception as e:
        logger.error(f"停止MediaMTX时出错: {str(e)}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MediaMTX服务管理')
    parser.add_argument('action', choices=['start', 'stop', 'restart'], help='操作类型')
    parser.add_argument('--config', help='配置文件路径')
    
    args = parser.parse_args()
    
    if args.action == 'start':
        start_mediamtx(args.config)
    elif args.action == 'stop':
        stop_mediamtx()
    elif args.action == 'restart':
        stop_mediamtx()
        time.sleep(1)
        start_mediamtx(args.config)

if __name__ == "__main__":
    main()